#!/usr/bin/env python3
"""
Test Completo del Sistema di Gestione Password
Testa tutti gli endpoint e funzionalità per i 3 tipi di utente.
"""

import os
import sys
import requests
import json
from pathlib import Path

# Aggiungi il path del backend
backend_dir = Path(__file__).parent
sys.path.insert(0, str(backend_dir))

# Carica le variabili d'ambiente
try:
    from dotenv import load_dotenv
    env_file = backend_dir / '.env'
    if env_file.exists():
        load_dotenv(env_file)
except ImportError:
    pass

# Configurazione
API_BASE_URL = "http://localhost:8001/api"
FRONTEND_BASE_URL = "http://localhost:3000/api"

def test_password_validation():
    """Testa la validazione password."""
    print("\n🔍 Test Validazione Password")
    print("=" * 50)
    
    test_passwords = [
        ("123", "Password troppo debole"),
        ("password", "Password senza maiuscole/numeri"),
        ("Password123", "Password buona ma senza caratteri speciali"),
        ("Password123!", "Password forte")
    ]
    
    for password, description in test_passwords:
        try:
            response = requests.post(
                f"{API_BASE_URL}/password/validate-password",
                json={"password": password},
                headers={"Content-Type": "application/json"}
            )
            
            if response.status_code == 200:
                data = response.json()
                print(f"✅ {description}")
                print(f"   Valida: {data['is_valid']}, Score: {data['strength_score']}")
                if data['suggestions']:
                    print(f"   Suggerimenti: {', '.join(data['suggestions'])}")
            else:
                print(f"❌ Errore validazione per '{password}': {response.status_code}")
                
        except Exception as e:
            print(f"❌ Errore connessione: {e}")
            return False
    
    return True

def test_password_reset_request():
    """Testa la richiesta di reset password."""
    print("\n📧 Test Richiesta Reset Password")
    print("=" * 50)
    
    test_cases = [
        {
            "email": "<EMAIL>",
            "user_type": "user",
            "description": "Reset per utente standard"
        },
        {
            "email": "<EMAIL>", 
            "user_type": "cantiere",
            "description": "Reset per cantiere"
        }
    ]
    
    for case in test_cases:
        try:
            response = requests.post(
                f"{API_BASE_URL}/password/request-password-reset",
                json={
                    "email": case["email"],
                    "user_type": case["user_type"]
                },
                headers={"Content-Type": "application/json"}
            )
            
            print(f"📤 {case['description']}")
            print(f"   Status: {response.status_code}")
            
            if response.status_code == 200:
                data = response.json()
                print(f"   ✅ {data.get('message', 'Richiesta inviata')}")
            else:
                data = response.json()
                print(f"   ⚠️  {data.get('detail', 'Errore sconosciuto')}")
                
        except Exception as e:
            print(f"❌ Errore connessione: {e}")
            return False
    
    return True

def test_frontend_api_routes():
    """Testa le API routes del frontend Next.js."""
    print("\n🌐 Test API Routes Frontend")
    print("=" * 50)
    
    # Test validazione password via frontend
    try:
        response = requests.post(
            f"{FRONTEND_BASE_URL}/password/validate-password",
            json={"password": "TestPassword123!"},
            headers={"Content-Type": "application/json"}
        )
        
        if response.status_code == 200:
            print("✅ API Route validazione password funzionante")
        else:
            print(f"❌ API Route validazione password: {response.status_code}")
            
    except Exception as e:
        print(f"❌ Errore connessione frontend: {e}")
        return False
    
    # Test richiesta reset via frontend
    try:
        response = requests.post(
            f"{FRONTEND_BASE_URL}/password/request-password-reset",
            json={
                "email": "<EMAIL>",
                "user_type": "user"
            },
            headers={"Content-Type": "application/json"}
        )
        
        print(f"📧 Test richiesta reset via frontend: {response.status_code}")
        if response.status_code == 200:
            print("✅ API Route richiesta reset funzionante")
        else:
            print(f"⚠️  API Route richiesta reset: {response.status_code}")
            
    except Exception as e:
        print(f"❌ Errore connessione frontend: {e}")
        return False
    
    return True

def test_rate_limiting():
    """Testa il rate limiting."""
    print("\n⏱️  Test Rate Limiting")
    print("=" * 50)
    
    # Fai molte richieste rapidamente per testare il rate limiting
    email = "<EMAIL>"
    
    for i in range(6):  # Più del limite di 5
        try:
            response = requests.post(
                f"{API_BASE_URL}/password/request-password-reset",
                json={
                    "email": email,
                    "user_type": "user"
                },
                headers={"Content-Type": "application/json"}
            )
            
            print(f"   Richiesta {i+1}: Status {response.status_code}")
            
            if response.status_code == 429:
                print("✅ Rate limiting attivo - richiesta bloccata")
                return True
                
        except Exception as e:
            print(f"❌ Errore: {e}")
            return False
    
    print("⚠️  Rate limiting non attivato (potrebbe essere normale)")
    return True

def test_system_health():
    """Testa la salute generale del sistema."""
    print("\n🏥 Test Salute Sistema")
    print("=" * 50)
    
    # Test connessione backend
    try:
        response = requests.get(f"{API_BASE_URL.replace('/api', '')}/health")
        if response.status_code == 200:
            print("✅ Backend FastAPI raggiungibile")
        else:
            print(f"⚠️  Backend status: {response.status_code}")
    except:
        print("❌ Backend non raggiungibile")
        return False
    
    # Test connessione frontend
    try:
        response = requests.get("http://localhost:3000")
        if response.status_code == 200:
            print("✅ Frontend Next.js raggiungibile")
        else:
            print(f"⚠️  Frontend status: {response.status_code}")
    except:
        print("❌ Frontend non raggiungibile")
        return False
    
    return True

def main():
    """Esegue tutti i test."""
    print("🧪 TEST COMPLETO SISTEMA GESTIONE PASSWORD")
    print("=" * 70)
    
    tests = [
        ("Salute Sistema", test_system_health),
        ("Validazione Password", test_password_validation),
        ("Richiesta Reset Password", test_password_reset_request),
        ("API Routes Frontend", test_frontend_api_routes),
        ("Rate Limiting", test_rate_limiting)
    ]
    
    results = []
    for test_name, test_func in tests:
        print(f"\n🔄 Esecuzione: {test_name}")
        try:
            result = test_func()
            results.append((test_name, result))
        except Exception as e:
            print(f"❌ Errore durante {test_name}: {e}")
            results.append((test_name, False))
    
    # Risultati finali
    print("\n" + "=" * 70)
    print("📊 RISULTATI FINALI")
    print("=" * 70)
    
    passed = 0
    for test_name, result in results:
        status = "✅ PASS" if result else "❌ FAIL"
        print(f"{status} {test_name}")
        if result:
            passed += 1
    
    total = len(results)
    print(f"\n🎯 Risultato: {passed}/{total} test superati")
    
    if passed == total:
        print("🎉 Tutti i test superati! Il sistema è funzionante.")
        return True
    else:
        print("🔧 Alcuni test falliti. Controlla la configurazione.")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)

(()=>{var e={};e.id=569,e.ids=[569],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},7962:(e,t,r)=>{Promise.resolve().then(r.bind(r,47733))},9895:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>n});var s=r(60687),i=r(43210),o=r(16189);function n(){let[e,t]=(0,i.useState)(""),[r,n]=(0,i.useState)(""),[a,l]=(0,i.useState)(""),[d,p]=(0,i.useState)(!1),u=(0,o.useRouter)(),c=async t=>{t.preventDefault(),p(!0),l("Logging in...");try{console.log("Starting login with:",{username:e,password:r});let t=new FormData;t.append("username",e),t.append("password",r),console.log("Sending request to backend...");let s=await fetch("http://localhost:8001/api/auth/login",{method:"POST",body:t});console.log("Response status:",s.status);let i=await s.json();console.log("Response data:",i),s.ok?(localStorage.setItem("token",i.access_token),l("SUCCESS: Login riuscito! Token salvato. Reindirizzamento..."),setTimeout(()=>{"owner"===i.role?(console.log("Redirecting to /admin"),u.push("/admin")):"user"===i.role?(console.log("Redirecting to /cantieri"),u.push("/cantieri")):(console.log("Redirecting to /"),u.push("/"))},1e3)):l(`ERROR: ${s.status} - ${i.detail||"Login failed"}`)}catch(e){console.error("Login error:",e),l(`EXCEPTION: ${e.message}`)}finally{p(!1)}};return(0,s.jsx)("div",{style:{minHeight:"100vh",display:"flex",alignItems:"center",justifyContent:"center",backgroundColor:"#f8f9fa",fontFamily:"Arial, sans-serif"},children:(0,s.jsxs)("div",{style:{backgroundColor:"white",padding:"40px",borderRadius:"10px",boxShadow:"0 4px 6px rgba(0, 0, 0, 0.1)",width:"100%",maxWidth:"400px"},children:[(0,s.jsx)("h1",{style:{textAlign:"center",marginBottom:"30px",color:"#333"},children:"Simple Login Test"}),(0,s.jsxs)("form",{onSubmit:c,style:{marginBottom:"20px"},children:[(0,s.jsxs)("div",{style:{marginBottom:"15px"},children:[(0,s.jsx)("label",{style:{display:"block",marginBottom:"5px",fontWeight:"bold"},children:"Username:"}),(0,s.jsx)("input",{type:"text",value:e,onChange:e=>t(e.target.value),placeholder:"admin",required:!0,disabled:d,style:{width:"100%",padding:"10px",border:"1px solid #ddd",borderRadius:"5px",fontSize:"16px"}})]}),(0,s.jsxs)("div",{style:{marginBottom:"20px"},children:[(0,s.jsx)("label",{style:{display:"block",marginBottom:"5px",fontWeight:"bold"},children:"Password:"}),(0,s.jsx)("input",{type:"password",value:r,onChange:e=>n(e.target.value),placeholder:"admin",required:!0,disabled:d,style:{width:"100%",padding:"10px",border:"1px solid #ddd",borderRadius:"5px",fontSize:"16px"}})]}),(0,s.jsx)("button",{type:"submit",disabled:d,style:{width:"100%",padding:"12px",backgroundColor:d?"#ccc":"#007bff",color:"white",border:"none",borderRadius:"5px",fontSize:"16px",cursor:d?"not-allowed":"pointer"},children:d?"Logging in...":"Login"})]}),(0,s.jsx)("div",{style:{backgroundColor:"#f8f9fa",padding:"15px",borderRadius:"5px",whiteSpace:"pre-wrap",minHeight:"100px",fontSize:"14px",fontFamily:"monospace"},children:a||"Enter credentials and click Login..."}),(0,s.jsx)("div",{style:{marginTop:"20px",textAlign:"center"},children:(0,s.jsx)("small",{style:{color:"#666"},children:"Test credentials: admin/admin"})})]})})}},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},12412:e=>{"use strict";e.exports=require("assert")},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},21820:e=>{"use strict";e.exports=require("os")},27910:e=>{"use strict";e.exports=require("stream")},28354:e=>{"use strict";e.exports=require("util")},29021:e=>{"use strict";e.exports=require("fs")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},33873:e=>{"use strict";e.exports=require("path")},42242:(e,t,r)=>{Promise.resolve().then(r.bind(r,9895))},47733:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>s});let s=(0,r(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\CMS\\\\webapp-nextjs\\\\src\\\\app\\\\simple-login\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\CMS\\webapp-nextjs\\src\\app\\simple-login\\page.tsx","default")},50452:(e,t,r)=>{"use strict";r.r(t),r.d(t,{GlobalError:()=>n.a,__next_app__:()=>u,pages:()=>p,routeModule:()=>c,tree:()=>d});var s=r(65239),i=r(48088),o=r(88170),n=r.n(o),a=r(30893),l={};for(let e in a)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(l[e]=()=>a[e]);r.d(t,l);let d={children:["",{children:["simple-login",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(r.bind(r,47733)),"C:\\CMS\\webapp-nextjs\\src\\app\\simple-login\\page.tsx"]}]},{metadata:{icon:[async e=>(await Promise.resolve().then(r.bind(r,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(r.bind(r,94431)),"C:\\CMS\\webapp-nextjs\\src\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(r.t.bind(r,57398,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(r.t.bind(r,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(r.t.bind(r,65284,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(r.bind(r,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]}.children,p=["C:\\CMS\\webapp-nextjs\\src\\app\\simple-login\\page.tsx"],u={require:r,loadChunk:()=>Promise.resolve()},c=new s.AppPageRouteModule({definition:{kind:i.RouteKind.APP_PAGE,page:"/simple-login/page",pathname:"/simple-login",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:d}})},55511:e=>{"use strict";e.exports=require("crypto")},55591:e=>{"use strict";e.exports=require("https")},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},70440:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>i});var s=r(31658);let i=async e=>[{type:"image/x-icon",sizes:"16x16",url:(0,s.fillMetadataSegment)(".",await e.params,"favicon.ico")+""}]},74075:e=>{"use strict";e.exports=require("zlib")},79551:e=>{"use strict";e.exports=require("url")},81630:e=>{"use strict";e.exports=require("http")},83997:e=>{"use strict";e.exports=require("tty")},94735:e=>{"use strict";e.exports=require("events")}};var t=require("../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),s=t.X(0,[447,538,658,615],()=>r(50452));module.exports=s})();
'use client'

import { useState, useEffect } from 'react'
import { useRouter } from 'next/navigation'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Alert, AlertDescription } from '@/components/ui/alert'
import { useAuth } from '@/contexts/AuthContext'
import { useCantiere } from '@/hooks/useCantiere'
import { CantiereErrorBoundary } from '@/components/cantiere/CantiereErrorBoundary'
import { caviApi } from '@/lib/api'
import { Cavo } from '@/types'
import CaviTable from '@/components/cavi/CaviTable'
import CaviStatistics from '@/components/cavi/CaviStatistics'
import InserisciMetriDialog from '@/components/cavi/InserisciMetriDialog'
import ModificaBobinaDialog from '@/components/cavi/ModificaBobinaDialog'
import CollegamentiDialog from '@/components/cavi/CollegamentiDialog'
import CertificazioneDialog from '@/components/cavi/CertificazioneDialog'
import CreaComandaDialog from '@/components/cavi/CreaComandaDialog'
import ImportExcelDialog from '@/components/cavi/ImportExcelDialog'
import ExportDataDialog from '@/components/cavi/ExportDataDialog'
// import { useToast } from '@/hooks/use-toast'
import {
  Package,
  AlertCircle,
  Loader2
} from 'lucide-react'

interface DashboardStats {
  totali: number
  installati: number
  collegati: number
  certificati: number
  percentualeInstallazione: number
  percentualeCollegamento: number
  percentualeCertificazione: number
  metriTotali: number
  metriInstallati: number
  metriCollegati: number
  metriCertificati: number
}

export default function CaviPage() {
  const { user, isAuthenticated, isLoading: authLoading } = useAuth()
  const { cantiereId, cantiere, isValidCantiere, isLoading: cantiereLoading, error: cantiereError } = useCantiere()
  const router = useRouter()

  // Debug cantiere
  console.log('🏗️ CaviPage: Stato auth e cantiere:', {
    cantiereFromHook: cantiere,
    cantiereId: cantiereId,
    isValidCantiere: isValidCantiere,
    user: user,
    userRole: user?.ruolo
  })

  // Sistema toast semplice
  const toast = ({ title, description, variant }: { title: string, description: string, variant?: string }) => {
    // TODO: Implementare sistema toast visuale
  }
  const [cavi, setCavi] = useState<Cavo[]>([])
  const [caviSpare, setCaviSpare] = useState<Cavo[]>([])
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState('')
  const [selectedCavi, setSelectedCavi] = useState<string[]>([])
  const [selectionEnabled, setSelectionEnabled] = useState(true)
  const [filteredCavi, setFilteredCavi] = useState<Cavo[]>([])
  const [revisioneCorrente, setRevisioneCorrente] = useState<string>('')

  // Update filtered cavi when main cavi change
  useEffect(() => {
    setFilteredCavi(cavi)
  }, [cavi])

  // Stati per i dialoghi
  const [inserisciMetriDialog, setInserisciMetriDialog] = useState<{
    open: boolean
    cavo: Cavo | null
  }>({ open: false, cavo: null })

  const [modificaBobinaDialog, setModificaBobinaDialog] = useState<{
    open: boolean
    cavo: Cavo | null
  }>({ open: false, cavo: null })

  const [collegamentiDialog, setCollegamentiDialog] = useState<{
    open: boolean
    cavo: Cavo | null
  }>({ open: false, cavo: null })

  const [certificazioneDialog, setCertificazioneDialog] = useState<{
    open: boolean
    cavo: Cavo | null
  }>({ open: false, cavo: null })

  const [creaComandaDialog, setCreaComandaDialog] = useState<{
    open: boolean
    tipoComanda?: 'POSA' | 'COLLEGAMENTO_PARTENZA' | 'COLLEGAMENTO_ARRIVO' | 'CERTIFICAZIONE'
  }>({ open: false })

  const [importExcelDialog, setImportExcelDialog] = useState<{
    open: boolean
    tipo?: 'cavi' | 'bobine'
  }>({ open: false })

  const [exportDataDialog, setExportDataDialog] = useState(false)
  const [stats, setStats] = useState<DashboardStats>({
    totali: 0,
    installati: 0,
    collegati: 0,
    certificati: 0,
    percentualeInstallazione: 0,
    percentualeCollegamento: 0,
    percentualeCertificazione: 0,
    metriTotali: 0,
    metriInstallati: 0,
    metriCollegati: 0,
    metriCertificati: 0
  })

  useEffect(() => {
    if (!authLoading && !isAuthenticated) {
      router.push('/login')
    }
  }, [isAuthenticated, authLoading, router])

  // Crea oggetto cantiere per il dialog
  const cantiereForDialog = cantiere || (cantiereId && cantiereId > 0 ? {
    id_cantiere: cantiereId,
    commessa: `Cantiere ${cantiereId}`
  } : null)

  // Carica i cavi dal backend - MIGLIORATO con nuovo hook
  useEffect(() => {
    if (isValidCantiere && cantiereId && cantiereId > 0 && !cantiereLoading) {
      console.log('🏗️ CaviPage: Caricamento cavi per cantiere:', cantiereId)
      loadCavi()
      loadRevisioneCorrente()
    } else if (!cantiereLoading && !isValidCantiere) {
      console.warn('🏗️ CaviPage: Cantiere non valido, reset dati')
      setCavi([])
      setCaviSpare([])
      setError(cantiereError || 'Nessun cantiere selezionato')
    }
  }, [cantiereId, isValidCantiere, cantiereLoading, cantiereError])

  const loadRevisioneCorrente = async () => {
    try {
      const response = await fetch(`http://localhost:8001/api/cavi/${cantiereId}/revisione-corrente`, {
        headers: {
          'Authorization': `Bearer ${localStorage.getItem('token')}`,
          'Content-Type': 'application/json'
        }
      })

      if (response.ok) {
        const data = await response.json()
        setRevisioneCorrente(data.revisione_corrente || '00')
      } else {
        setRevisioneCorrente('00')
      }
    } catch (error) {
      setRevisioneCorrente('00')
    }
  }

  const loadCavi = async () => {
    try {
      setLoading(true)
      setError('')

      // Prima prova con l'API normale
      try {
        const data = await caviApi.getCavi(cantiereId!)

        // Separa cavi attivi e spare
        const caviAttivi = data.filter((cavo: Cavo) => !cavo.spare)
        const caviSpareFiltered = data.filter((cavo: Cavo) => cavo.spare)

        setCavi(caviAttivi)
        setCaviSpare(caviSpareFiltered)

        // Calcola statistiche
        calculateStats(caviAttivi)

      } catch (apiError: any) {

        // Fallback: prova con endpoint debug (senza autenticazione)
        try {
          const response = await fetch(`http://localhost:8001/api/cavi/debug/${cantiereId}`)
          const debugData = await response.json()

          if (debugData.cavi && Array.isArray(debugData.cavi)) {
            const caviAttivi = debugData.cavi.filter((cavo: any) => !cavo.spare)
            const caviSpareFiltered = debugData.cavi.filter((cavo: any) => cavo.spare)

            setCavi(caviAttivi)
            setCaviSpare(caviSpareFiltered)
            calculateStats(caviAttivi)

            setError('⚠️ Dati caricati tramite endpoint debug (problema autenticazione)')
          } else {
            throw new Error('Formato dati debug non valido')
          }
        } catch (debugError) {
          throw apiError // Rilancia l'errore originale
        }
      }

    } catch (error: any) {
      setError(`Errore nel caricamento dei cavi: ${error.response?.data?.detail || error.message}`)
    } finally {
      setLoading(false)
    }
  }

  const calculateStats = (caviData: Cavo[]) => {
    const totali = caviData.length
    const installati = caviData.filter(c => (c.metri_posati || c.metratura_reale || 0) > 0).length
    const collegati = caviData.filter(c => (c.collegamento || c.collegamenti) === 3).length // 3 = collegato
    const certificati = caviData.filter(c => c.certificato).length

    const metriTotali = caviData.reduce((sum, c) => sum + (c.metri_teorici || 0), 0)
    const metriInstallati = caviData.reduce((sum, c) => sum + (c.metri_posati || 0), 0)
    const metriCollegati = caviData.filter(c => c.collegamento === 3).reduce((sum, c) => sum + (c.metri_posati || 0), 0)
    const metriCertificati = caviData.filter(c => c.certificato).reduce((sum, c) => sum + (c.metri_posati || 0), 0)

    setStats({
      totali,
      installati,
      collegati,
      certificati,
      percentualeInstallazione: totali > 0 ? Math.round((installati / totali) * 100) : 0,
      percentualeCollegamento: totali > 0 ? Math.round((collegati / totali) * 100) : 0,
      percentualeCertificazione: totali > 0 ? Math.round((certificati / totali) * 100) : 0,
      metriTotali,
      metriInstallati,
      metriCollegati,
      metriCertificati
    })
  }

  // Gestione azioni sui cavi
  const handleStatusAction = (cavo: Cavo, action: string, label?: string) => {

    switch (action) {
      case 'insert_meters':
        setInserisciMetriDialog({ open: true, cavo })
        break
      case 'modify_reel':
        setModificaBobinaDialog({ open: true, cavo })
        break
      case 'view_command':
        toast({
          title: "Visualizza Comanda",
          description: `Apertura comanda ${label} per cavo ${cavo.id_cavo}`,
        })
        break
      case 'connect_cable':
      case 'connect_arrival':
      case 'connect_departure':
      case 'disconnect_cable':
      case 'manage_connections':
        setCollegamentiDialog({ open: true, cavo })
        break
      case 'create_certificate':
      case 'generate_pdf':
        setCertificazioneDialog({ open: true, cavo })
        break
    }
  }

  const handleContextMenuAction = (cavo: Cavo, action: string) => {

    switch (action) {
      case 'view_details':
        toast({
          title: "Visualizza Dettagli",
          description: `Apertura dettagli per cavo ${cavo.id_cavo}`,
        })
        break
      case 'edit':
        toast({
          title: "Modifica Cavo",
          description: "Funzione modifica cavo in sviluppo",
        })
        break
      case 'delete':
        toast({
          title: "Elimina Cavo",
          description: "Funzione eliminazione cavo in sviluppo",
          variant: "destructive"
        })
        break
      case 'add_new':
        toast({
          title: "Aggiungi Nuovo Cavo",
          description: "Funzione aggiunta nuovo cavo in sviluppo",
        })
        break
      case 'select':
        const isSelected = selectedCavi.includes(cavo.id_cavo)
        if (isSelected) {
          setSelectedCavi(selectedCavi.filter(id => id !== cavo.id_cavo))
          toast({
            title: "Cavo Deselezionato",
            description: `Cavo ${cavo.id_cavo} deselezionato`,
          })
        } else {
          setSelectedCavi([...selectedCavi, cavo.id_cavo])
          toast({
            title: "Cavo Selezionato",
            description: `Cavo ${cavo.id_cavo} selezionato`,
          })
        }
        break
      case 'copy_id':
        navigator.clipboard.writeText(cavo.id_cavo)
        toast({
          title: "ID Copiato",
          description: `ID cavo ${cavo.id_cavo} copiato negli appunti`,
        })
        break
      case 'copy_details':
        const details = `ID: ${cavo.id_cavo}, Tipologia: ${cavo.tipologia}, Formazione: ${cavo.formazione || cavo.sezione}, Metri: ${cavo.metri_teorici}`
        navigator.clipboard.writeText(details)
        toast({
          title: "Dettagli Copiati",
          description: "Dettagli cavo copiati negli appunti",
        })
        break
      case 'add_to_command':
        toast({
          title: "Aggiungi a Comanda",
          description: "Funzione aggiunta a comanda in sviluppo",
        })
        break
      case 'remove_from_command':
        toast({
          title: "Rimuovi da Comanda",
          description: "Funzione rimozione da comanda in sviluppo",
        })
        break
      case 'create_command_posa':
        setCreaComandaDialog({ open: true, tipoComanda: 'POSA' })
        break
      case 'create_command_collegamento_partenza':
        setCreaComandaDialog({ open: true, tipoComanda: 'COLLEGAMENTO_PARTENZA' })
        break
      case 'create_command_collegamento_arrivo':
        setCreaComandaDialog({ open: true, tipoComanda: 'COLLEGAMENTO_ARRIVO' })
        break
      case 'create_command_certificazione':
        setCreaComandaDialog({ open: true, tipoComanda: 'CERTIFICAZIONE' })
        break
      case 'add_multiple_to_command':
        toast({
          title: "Aggiungi Tutti a Comanda",
          description: "Funzione aggiunta multipla a comanda in sviluppo",
        })
        break
      case 'remove_multiple_from_commands':
        toast({
          title: "Rimuovi Tutti dalle Comande",
          description: "Funzione rimozione multipla dalle comande in sviluppo",
        })
        break
      default:
        toast({
          title: "Azione non implementata",
          description: `Azione ${action} non ancora implementata`,
        })
        break
    }
  }

  // Gestione successo/errore dialoghi
  const handleDialogSuccess = (message: string) => {
    toast({
      title: "Operazione completata",
      description: message,
    })
    // Ricarica i dati
    loadCavi()
  }

  const handleDialogError = (message: string) => {
    toast({
      title: "Errore",
      description: message,
      variant: "destructive"
    })
  }

  // Mostra loader se stiamo caricando i dati dei cavi
  if (loading && isValidCantiere) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <Loader2 className="h-8 w-8 animate-spin" />
        <span className="ml-2">Caricamento cavi...</span>
      </div>
    )
  }

  return (
    <CantiereErrorBoundary>
      <div className="max-w-[90%] mx-auto p-6">

        {/* Mostra errore specifico dei cavi se presente */}
        {error && (
          <Alert variant="destructive" className="mb-6">
            <AlertCircle className="h-4 w-4" />
            <AlertDescription>{error}</AlertDescription>
          </Alert>
        )}

        {/* Statistics */}
        <CaviStatistics
        cavi={cavi}
        filteredCavi={filteredCavi}
        revisioneCorrente={revisioneCorrente}
        className="mb-2"
      />

      {/* Tabella Cavi Attivi */}
      <div className="mb-8">
        <CaviTable
          cavi={cavi}
          loading={loading}
          selectionEnabled={selectionEnabled}
          selectedCavi={selectedCavi}
          onSelectionChange={setSelectedCavi}
          onStatusAction={handleStatusAction}
          onContextMenuAction={handleContextMenuAction}
        />
      </div>

      {/* Tabella Cavi Spare */}
      {caviSpare.length > 0 && (
        <div className="mb-8">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center space-x-2">
                <Package className="h-5 w-5" />
                <span>Cavi Spare ({caviSpare.length})</span>
              </CardTitle>
            </CardHeader>
            <CardContent>
              <CaviTable
                cavi={caviSpare}
                loading={loading}
                selectionEnabled={false}
                onStatusAction={handleStatusAction}
                onContextMenuAction={handleContextMenuAction}
              />
            </CardContent>
          </Card>
        </div>
      )}

      {/* Debug info - solo in development */}
      {process.env.NODE_ENV === 'development' && (
        <Card className="mt-6">
          <CardHeader>
            <CardTitle>Debug Info</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-2 gap-4 text-sm">
              <div>
                <p><strong>User:</strong> {user ? user.username : 'Non autenticato'}</p>
                <p><strong>User Role:</strong> {user ? user.ruolo : 'N/A'}</p>
                <p><strong>Cantiere ID:</strong> {cantiereId}</p>
                <p><strong>Cantiere context:</strong> {cantiere ? cantiere.commessa : 'Nessuno'}</p>
              </div>
              <div>
                <p><strong>Token presente:</strong> {typeof window !== 'undefined' ? (localStorage.getItem('token') ? 'Sì' : 'No') : 'N/A'}</p>
                <p><strong>Loading:</strong> {loading ? 'Sì' : 'No'}</p>
                <p><strong>Error:</strong> {error || 'Nessuno'}</p>
                <p><strong>Cavi ricevuti:</strong> {cavi.length}</p>
                <p><strong>Cavi spare:</strong> {caviSpare.length}</p>
              </div>
            </div>
            <div className="mt-4">
              <Button
                onClick={async () => {
                  try {
                    const response = await fetch('http://localhost:8001/api/cavi/debug/1')
                    const data = await response.json()
                    alert(`Backend ha ${data.total_cavi} cavi per cantiere 1`)
                  } catch (err) {
                    alert('Errore nel test backend')
                  }
                }}
                variant="outline"
                size="sm"
              >
                Test Backend Diretto
              </Button>
            </div>
          </CardContent>
        </Card>
      )}

      {/* Dialoghi */}
      <InserisciMetriDialog
        open={inserisciMetriDialog.open}
        onClose={() => setInserisciMetriDialog({ open: false, cavo: null })}
        cavo={inserisciMetriDialog.cavo}
        cantiere={cantiereForDialog}
        onSuccess={handleDialogSuccess}
        onError={handleDialogError}
      />

      <ModificaBobinaDialog
        open={modificaBobinaDialog.open}
        onClose={() => setModificaBobinaDialog({ open: false, cavo: null })}
        cavo={modificaBobinaDialog.cavo}
        cantiere={cantiereForDialog}
        onSuccess={handleDialogSuccess}
        onError={handleDialogError}
      />

      <CollegamentiDialog
        open={collegamentiDialog.open}
        onClose={() => setCollegamentiDialog({ open: false, cavo: null })}
        cavo={collegamentiDialog.cavo}
        onSuccess={handleDialogSuccess}
        onError={handleDialogError}
      />

      <CertificazioneDialog
        open={certificazioneDialog.open}
        onClose={() => setCertificazioneDialog({ open: false, cavo: null })}
        cavo={certificazioneDialog.cavo}
        onSuccess={handleDialogSuccess}
        onError={handleDialogError}
      />

      <CreaComandaDialog
        open={creaComandaDialog.open}
        onClose={() => setCreaComandaDialog({ open: false })}
        caviSelezionati={selectedCavi}
        tipoComanda={creaComandaDialog.tipoComanda}
        onSuccess={handleDialogSuccess}
        onError={handleDialogError}
      />

      <ImportExcelDialog
        open={importExcelDialog.open}
        onClose={() => setImportExcelDialog({ open: false })}
        tipo={importExcelDialog.tipo || 'cavi'}
        onSuccess={handleDialogSuccess}
        onError={handleDialogError}
      />

      <ExportDataDialog
        open={exportDataDialog}
        onClose={() => setExportDataDialog(false)}
        onSuccess={handleDialogSuccess}
        onError={handleDialogError}
      />
      </div>
    </CantiereErrorBoundary>
  )
}

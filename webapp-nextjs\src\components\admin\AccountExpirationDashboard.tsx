'use client'

import React, { useState, useEffect } from 'react'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { Alert, AlertDescription } from '@/components/ui/alert'
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs'
import { 
  Calendar, 
  Clock, 
  AlertTriangle, 
  CheckCircle, 
  XCircle, 
  RefreshCw,
  Mail,
  User,
  Settings
} from 'lucide-react'

interface ExpirationSummary {
  users_expiring_soon: number
  users_expired: number
  expiring_details: Array<{
    id: number
    username: string
    email: string
    expiration_date: string
    days_remaining: number
  }>
  expired_details: Array<{
    id: number
    username: string
    email: string
    expiration_date: string
    days_overdue: number
  }>
}

interface ExpirationCheckResult {
  users_warned_5_days: number
  users_warned_1_day: number
  users_expired: number
  cantieri_warned_5_days: number
  cantieri_warned_1_day: number
  cantieri_expired: number
  emails_sent: number
  emails_failed: number
  execution_time: string
}

export default function AccountExpirationDashboard() {
  const [summary, setSummary] = useState<ExpirationSummary | null>(null)
  const [checkResult, setCheckResult] = useState<ExpirationCheckResult | null>(null)
  const [loading, setLoading] = useState(false)
  const [error, setError] = useState<string | null>(null)
  const [lastCheck, setLastCheck] = useState<Date | null>(null)

  const fetchSummary = async () => {
    try {
      setLoading(true)
      setError(null)
      
      const response = await fetch('/api/account-expiration/summary', {
        headers: {
          'Authorization': `Bearer ${localStorage.getItem('token')}`
        }
      })
      
      if (!response.ok) {
        throw new Error('Errore durante il recupero del riepilogo')
      }
      
      const data = await response.json()
      setSummary(data)
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Errore sconosciuto')
    } finally {
      setLoading(false)
    }
  }

  const runExpirationCheck = async () => {
    try {
      setLoading(true)
      setError(null)
      
      const response = await fetch('/api/account-expiration/check', {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${localStorage.getItem('token')}`,
          'Content-Type': 'application/json'
        }
      })
      
      if (!response.ok) {
        throw new Error('Errore durante il controllo scadenze')
      }
      
      const data = await response.json()
      setCheckResult(data)
      setLastCheck(new Date())
      
      // Aggiorna anche il riepilogo
      await fetchSummary()
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Errore sconosciuto')
    } finally {
      setLoading(false)
    }
  }

  useEffect(() => {
    fetchSummary()
  }, [])

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('it-IT')
  }

  const getDaysRemainingBadge = (days: number) => {
    if (days <= 0) {
      return <Badge variant="destructive">Scaduto</Badge>
    } else if (days <= 1) {
      return <Badge variant="destructive">Scade oggi/domani</Badge>
    } else if (days <= 5) {
      return <Badge variant="secondary">Scade tra {days} giorni</Badge>
    } else {
      return <Badge variant="outline">Scade tra {days} giorni</Badge>
    }
  }

  const getOverdueBadge = (days: number) => {
    if (days <= 7) {
      return <Badge variant="destructive">Scaduto da {days} giorni</Badge>
    } else if (days <= 30) {
      return <Badge variant="destructive">Scaduto da {days} giorni</Badge>
    } else {
      return <Badge variant="destructive">Scaduto da oltre un mese</Badge>
    }
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex justify-between items-center">
        <div>
          <h1 className="text-3xl font-bold">Gestione Scadenze Account</h1>
          <p className="text-muted-foreground">
            Monitora e gestisci le scadenze degli account utente
          </p>
        </div>
        <div className="flex gap-2">
          <Button 
            onClick={fetchSummary} 
            variant="outline" 
            disabled={loading}
          >
            <RefreshCw className={`h-4 w-4 mr-2 ${loading ? 'animate-spin' : ''}`} />
            Aggiorna
          </Button>
          <Button 
            onClick={runExpirationCheck} 
            disabled={loading}
          >
            <Clock className="h-4 w-4 mr-2" />
            Controlla Scadenze
          </Button>
        </div>
      </div>

      {/* Error Alert */}
      {error && (
        <Alert variant="destructive">
          <AlertTriangle className="h-4 w-4" />
          <AlertDescription>{error}</AlertDescription>
        </Alert>
      )}

      {/* Last Check Info */}
      {lastCheck && (
        <Alert>
          <CheckCircle className="h-4 w-4" />
          <AlertDescription>
            Ultimo controllo eseguito: {lastCheck.toLocaleString('it-IT')}
          </AlertDescription>
        </Alert>
      )}

      {/* Check Results */}
      {checkResult && (
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Mail className="h-5 w-5" />
              Risultati Ultimo Controllo
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
              <div className="text-center">
                <div className="text-2xl font-bold text-orange-600">
                  {checkResult.users_warned_5_days}
                </div>
                <div className="text-sm text-muted-foreground">
                  Avvisi 5 giorni
                </div>
              </div>
              <div className="text-center">
                <div className="text-2xl font-bold text-red-600">
                  {checkResult.users_warned_1_day}
                </div>
                <div className="text-sm text-muted-foreground">
                  Avvisi 1 giorno
                </div>
              </div>
              <div className="text-center">
                <div className="text-2xl font-bold text-red-800">
                  {checkResult.users_expired}
                </div>
                <div className="text-sm text-muted-foreground">
                  Account disabilitati
                </div>
              </div>
              <div className="text-center">
                <div className="text-2xl font-bold text-green-600">
                  {checkResult.emails_sent}
                </div>
                <div className="text-sm text-muted-foreground">
                  Email inviate
                </div>
              </div>
            </div>
            {checkResult.emails_failed > 0 && (
              <Alert variant="destructive" className="mt-4">
                <XCircle className="h-4 w-4" />
                <AlertDescription>
                  {checkResult.emails_failed} email non sono state inviate correttamente
                </AlertDescription>
              </Alert>
            )}
          </CardContent>
        </Card>
      )}

      {/* Summary Cards */}
      {summary && (
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <AlertTriangle className="h-5 w-5 text-orange-500" />
                Account in Scadenza
              </CardTitle>
              <CardDescription>
                Account che scadranno nei prossimi 7 giorni
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="text-3xl font-bold text-orange-600 mb-4">
                {summary.users_expiring_soon}
              </div>
              {summary.expiring_details.length > 0 ? (
                <div className="space-y-2">
                  {summary.expiring_details.map((user) => (
                    <div key={user.id} className="flex justify-between items-center p-2 border rounded">
                      <div>
                        <div className="font-medium">{user.username}</div>
                        <div className="text-sm text-muted-foreground">{user.email}</div>
                        <div className="text-sm">Scade: {formatDate(user.expiration_date)}</div>
                      </div>
                      <div>
                        {getDaysRemainingBadge(user.days_remaining)}
                      </div>
                    </div>
                  ))}
                </div>
              ) : (
                <p className="text-muted-foreground">Nessun account in scadenza</p>
              )}
            </CardContent>
          </Card>

          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <XCircle className="h-5 w-5 text-red-500" />
                Account Scaduti
              </CardTitle>
              <CardDescription>
                Account scaduti ma ancora abilitati
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="text-3xl font-bold text-red-600 mb-4">
                {summary.users_expired}
              </div>
              {summary.expired_details.length > 0 ? (
                <div className="space-y-2">
                  {summary.expired_details.map((user) => (
                    <div key={user.id} className="flex justify-between items-center p-2 border rounded">
                      <div>
                        <div className="font-medium">{user.username}</div>
                        <div className="text-sm text-muted-foreground">{user.email}</div>
                        <div className="text-sm">Scaduto: {formatDate(user.expiration_date)}</div>
                      </div>
                      <div>
                        {getOverdueBadge(user.days_overdue)}
                      </div>
                    </div>
                  ))}
                </div>
              ) : (
                <p className="text-muted-foreground">Nessun account scaduto</p>
              )}
            </CardContent>
          </Card>
        </div>
      )}

      {/* Actions */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Settings className="h-5 w-5" />
            Azioni Rapide
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <Button variant="outline" className="h-auto p-4">
              <div className="text-center">
                <User className="h-6 w-6 mx-auto mb-2" />
                <div className="font-medium">Gestisci Utenti</div>
                <div className="text-sm text-muted-foreground">
                  Estendi o riattiva account
                </div>
              </div>
            </Button>
            <Button variant="outline" className="h-auto p-4">
              <div className="text-center">
                <Mail className="h-6 w-6 mx-auto mb-2" />
                <div className="font-medium">Log Notifiche</div>
                <div className="text-sm text-muted-foreground">
                  Visualizza storico email
                </div>
              </div>
            </Button>
            <Button variant="outline" className="h-auto p-4">
              <div className="text-center">
                <Calendar className="h-6 w-6 mx-auto mb-2" />
                <div className="font-medium">Pianificazione</div>
                <div className="text-sm text-muted-foreground">
                  Configura controlli automatici
                </div>
              </div>
            </Button>
          </div>
        </CardContent>
      </Card>
    </div>
  )
}

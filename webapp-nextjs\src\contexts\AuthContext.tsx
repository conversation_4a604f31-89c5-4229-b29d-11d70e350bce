'use client'

import React, { createContext, useContext, useState, useEffect, ReactNode } from 'react'
import { User, Cantier<PERSON> } from '@/types'
import { authApi, usersApi } from '@/lib/api'

interface AuthContextType {
  user: User | null
  cantiere: Cantiere | null
  isAuthenticated: boolean
  isLoading: boolean
  isImpersonating: boolean
  impersonatedUser: any | null
  expirationWarning: string | null
  daysUntilExpiration: number | null
  expirationDate: string | null
  login: (username: string, password: string) => Promise<void>
  loginCantiere: (codice_cantiere: string, password_cantiere: string) => Promise<void>
  logout: () => void
  checkAuth: () => Promise<void>
  impersonateUser: (userId: number) => Promise<any>
  selectCantiere: (cantiere: Cantiere) => void
  clearCantiere: () => void
  dismissExpirationWarning: () => void
}

const AuthContext = createContext<AuthContextType | undefined>(undefined)

export function useAuth() {
  const context = useContext(AuthContext)
  if (context === undefined) {
    throw new Error('useAuth must be used within an AuthProvider')
  }
  return context
}

interface AuthProviderProps {
  children: ReactNode
}

export function AuthProvider({ children }: AuthProviderProps) {
  const [user, setUser] = useState<User | null>(null)
  const [cantiere, setCantiere] = useState<Cantiere | null>(null)
  const [isLoading, setIsLoading] = useState(true)
  const [isImpersonating, setIsImpersonating] = useState(() => {
    if (typeof window !== 'undefined') {
      return localStorage.getItem('isImpersonating') === 'true'
    }
    return false
  })
  const [impersonatedUser, setImpersonatedUser] = useState<any | null>(() => {
    if (typeof window !== 'undefined') {
      const stored = localStorage.getItem('impersonatedUser')
      return stored ? JSON.parse(stored) : null
    }
    return null
  })

  // Stati per warning di scadenza
  const [expirationWarning, setExpirationWarning] = useState<string | null>(null)
  const [daysUntilExpiration, setDaysUntilExpiration] = useState<number | null>(null)
  const [expirationDate, setExpirationDate] = useState<string | null>(null)

  const isAuthenticated = !!user || !!cantiere

  // Debug log per vedere lo stato di autenticazione
  console.log('🔐 AuthContext: Stato corrente:', {
    user: user ? { id: user.id_utente, username: user.username, ruolo: user.ruolo } : null,
    cantiere: cantiere ? { id: cantiere.id_cantiere } : null,
    isAuthenticated,
    isLoading
  })

  // Verifica l'autenticazione al caricamento
  useEffect(() => {
    checkAuth()
  }, [])

  // Carica il cantiere selezionato dal localStorage all'avvio - MIGLIORATO per evitare race conditions
  useEffect(() => {
    if (typeof window !== 'undefined' && user && !isLoading && !cantiere) {
      const cantiereId = localStorage.getItem('selectedCantiereId')
      const cantiereName = localStorage.getItem('selectedCantiereName')

      // Validazione robusta dell'ID cantiere
      if (cantiereId && cantiereId !== 'null' && cantiereId !== 'undefined') {
        const parsedId = parseInt(cantiereId, 10)
        if (!isNaN(parsedId) && parsedId > 0) {
          const cantiereData = {
            id_cantiere: parsedId,
            commessa: cantiereName || `Cantiere ${parsedId}`,
            codice_univoco: '',
            id_utente: user.id_utente
          }
          console.log('🏗️ AuthContext: Caricamento cantiere dal localStorage:', cantiereData)
          setCantiere(cantiereData)
        } else {
          console.warn('🏗️ AuthContext: ID cantiere non valido nel localStorage:', cantiereId)
          // Pulisci localStorage se l'ID non è valido
          localStorage.removeItem('selectedCantiereId')
          localStorage.removeItem('selectedCantiereName')
        }
      }
    }
  }, [user, isLoading, cantiere])

  const checkAuth = async () => {
    try {
      // Verifica se siamo nel browser
      if (typeof window === 'undefined') {
        setIsLoading(false)
        return
      }

      // Prima di tutto, imposta loading a true
      setIsLoading(true)

      // Pulisci eventuali token non validi o scaduti
      const token = localStorage.getItem('token')

      if (token) {
        try {
          // Verifica la validità del token
          const response = await authApi.verifyToken()

          // Il backend restituisce direttamente i dati, non wrapped in { user: ... }
          const userData = response

          // Imposta i dati dell'utente come nel sistema React originale
          const userInfo = {
            id_utente: userData.user_id,
            username: userData.username,
            ruolo: userData.role
          }
          setUser(userInfo)

          // Gestisci l'impersonificazione
          const impersonatingState = userData.is_impersonated === true
          setIsImpersonating(impersonatingState)

          if (impersonatingState && userData.impersonated_id) {
            const impersonatedUserData = {
              id: userData.impersonated_id,
              username: userData.impersonated_username,
              role: userData.impersonated_role
            }
            setImpersonatedUser(impersonatedUserData)
            if (typeof window !== 'undefined') {
              localStorage.setItem('impersonatedUser', JSON.stringify(impersonatedUserData))
              localStorage.setItem('isImpersonating', 'true')
            }
          } else {
            setImpersonatedUser(null)
            if (typeof window !== 'undefined') {
              localStorage.removeItem('impersonatedUser')
              localStorage.removeItem('isImpersonating')
            }
          }

          // Se è un utente cantiere, gestisci i dati del cantiere
          if (userData.role === 'cantieri_user' && userData.cantiere_id) {
            const cantiereData = {
              id_cantiere: userData.cantiere_id,
              commessa: userData.cantiere_name || `Cantiere ${userData.cantiere_id}`,
              codice_univoco: '',
              id_utente: userData.user_id
            }
            console.log('🏗️ AuthContext: Impostazione cantiere per utente cantiere:', cantiereData)
            setCantiere(cantiereData)

            // Sincronizza con localStorage usando il formato standard
            localStorage.setItem('selectedCantiereId', userData.cantiere_id.toString())
            localStorage.setItem('selectedCantiereName', cantiereData.commessa)
          } else {
            // Per utenti standard, prova a caricare il cantiere dal localStorage
            console.log('🏗️ AuthContext: Utente standard, controllo cantiere dal localStorage')

            // Controlla se c'è un cantiere salvato dal login cantiere
            const cantiereData = localStorage.getItem('cantiere_data')
            if (cantiereData) {
              try {
                const parsedCantiere = JSON.parse(cantiereData)
                console.log('🏗️ AuthContext: Caricamento cantiere da cantiere_data:', parsedCantiere)
                setCantiere(parsedCantiere)

                // Sincronizza con il formato standard
                localStorage.setItem('selectedCantiereId', parsedCantiere.id_cantiere.toString())
                localStorage.setItem('selectedCantiereName', parsedCantiere.commessa)
              } catch (parseError) {
                console.warn('🏗️ AuthContext: Errore parsing cantiere_data:', parseError)
                localStorage.removeItem('cantiere_data')
              }
            }
          }
        } catch (tokenError) {
          // Se il token non è valido, rimuovilo
          localStorage.removeItem('token')
          localStorage.removeItem('access_token')
          localStorage.removeItem('user_data')
          localStorage.removeItem('cantiere_data')
          setUser(null)
          setCantiere(null)
        }
      } else {
        setUser(null)
        setCantiere(null)
      }
    } catch (error) {
      // In caso di errore generale, assicurati che l'utente non sia autenticato
      if (typeof window !== 'undefined') {
        localStorage.removeItem('token')
        localStorage.removeItem('access_token')
        localStorage.removeItem('user_data')
        localStorage.removeItem('cantiere_data')
      }
      setUser(null)
      setCantiere(null)
    } finally {
      // Assicurati che loading sia impostato a false alla fine
      setTimeout(() => {
        setIsLoading(false)
      }, 500) // Aggiungi un piccolo ritardo come nel sistema React originale
    }
  }

  const login = async (username: string, password: string) => {
    try {
      console.log('🔐 AuthContext: Inizio login per:', username)
      setIsLoading(true)
      const response = await authApi.login({ username, password })
      console.log('📡 AuthContext: Risposta backend ricevuta:', response)

      if (typeof window !== 'undefined') {
        // Salva il token come nel sistema React originale
        localStorage.setItem('token', response.access_token)
        console.log('💾 AuthContext: Token salvato nel localStorage')

        // Il backend restituisce i dati dell'utente direttamente nella risposta
        const userData = {
          id_utente: response.user_id,
          username: response.username,
          ruolo: response.role
        }

        // Gestisci warning di scadenza se presenti
        if (response.expiration_warning) {
          console.log('⚠️ AuthContext: Warning scadenza ricevuto:', response.expiration_warning)
          setExpirationWarning(response.expiration_warning)
          setDaysUntilExpiration(response.days_until_expiration)
          setExpirationDate(response.expiration_date)
        } else {
          // Pulisci warning precedenti
          setExpirationWarning(null)
          setDaysUntilExpiration(null)
          setExpirationDate(null)
        }

        console.log('👤 AuthContext: Dati utente creati:', userData)
        setUser(userData)
        setCantiere(null)
        console.log('✅ AuthContext: Stato utente aggiornato, restituisco userData')

        return userData
      }

      // Fallback se window non è disponibile (SSR)
      console.log('🔄 AuthContext: Fallback SSR')
      return {
        id_utente: response.user_id,
        username: response.username,
        ruolo: response.role
      }
    } catch (error) {
      console.error('❌ AuthContext: Errore durante login:', error)
      throw error
    } finally {
      setIsLoading(false)
    }
  }

  const loginCantiere = async (codice_cantiere: string, password_cantiere: string) => {
    try {
      setIsLoading(true)
      const response = await authApi.loginCantiere({ codice_cantiere, password_cantiere })

      if (typeof window !== 'undefined') {
        // Salva il token come nel sistema React originale
        localStorage.setItem('token', response.access_token)

        // Il backend restituisce i dati del cantiere direttamente nella risposta
        const cantiereData = {
          id_cantiere: response.cantiere_id,
          commessa: response.cantiere_name,
          codice_univoco: codice_cantiere,
          id_utente: response.user_id
        }

        // Salva i dati del cantiere nel localStorage
        localStorage.setItem('cantiere_data', JSON.stringify(cantiereData))

        setCantiere(cantiereData)
        setUser(null)

        // Forza il refresh dell'autenticazione per assicurarsi che tutto sia sincronizzato
        await checkAuth()

        return cantiereData
      }

      // Fallback se window non è disponibile (SSR)
      return {
        id_cantiere: response.cantiere_id,
        commessa: response.cantiere_name,
        codice_univoco: codice_cantiere,
        id_utente: response.user_id
      }
    } catch (error) {
      throw error
    } finally {
      setIsLoading(false)
    }
  }

  const impersonateUser = async (userId: number) => {
    try {
      // Chiama l'endpoint di impersonificazione
      const response = await usersApi.impersonateUser(userId)

      if (typeof window !== 'undefined') {
        // Salva il token nel localStorage
        localStorage.setItem('token', response.access_token)

        // Salva i dati dell'utente impersonato
        const impersonatedUserData = {
          id: response.impersonated_id,
          username: response.impersonated_username,
          role: response.impersonated_role
        }

        // Salva i dati dell'utente impersonato nel localStorage
        localStorage.setItem('impersonatedUser', JSON.stringify(impersonatedUserData))
        setImpersonatedUser(impersonatedUserData)

        // Imposta lo stato di impersonificazione a true
        setIsImpersonating(true)
        localStorage.setItem('isImpersonating', 'true')

        return { impersonatedUser: impersonatedUserData }
      }
    } catch (error) {
      throw error
    }
  }

  // Funzione per selezionare un cantiere - MIGLIORATA per gestione più robusta
  const selectCantiere = (cantiere: Cantiere) => {
    if (!cantiere || !cantiere.id_cantiere || cantiere.id_cantiere <= 0) {
      console.error('🏗️ AuthContext: Tentativo di selezione cantiere non valido:', cantiere)
      return
    }

    try {
      // Usa commessa come nome del cantiere, con fallback su nome se commessa non è disponibile
      const cantiereName = cantiere.commessa || `Cantiere ${cantiere.id_cantiere}`

      // Salva nel localStorage con validazione
      localStorage.setItem('selectedCantiereId', cantiere.id_cantiere.toString())
      localStorage.setItem('selectedCantiereName', cantiereName)

      // Aggiorna lo stato
      const cantiereData = {
        ...cantiere,
        commessa: cantiereName
      }

      console.log('🏗️ AuthContext: Cantiere selezionato:', cantiereData)
      setCantiere(cantiereData)

      // Rimuovi eventuali dati obsoleti
      localStorage.removeItem('cantiere_data')

    } catch (error) {
      console.error('🏗️ AuthContext: Errore nella selezione cantiere:', error)
    }
  }

  // Funzione per pulire lo stato del cantiere
  const clearCantiere = () => {
    console.log('🏗️ AuthContext: Pulizia stato cantiere')
    setCantiere(null)
    localStorage.removeItem('selectedCantiereId')
    localStorage.removeItem('selectedCantiereName')
    localStorage.removeItem('cantiere_data')
  }

  const logout = () => {
    if (typeof window !== 'undefined') {
      // Logout sempre completo - rimuovi tutto
      localStorage.clear() // Pulisce tutto il localStorage
      sessionStorage.clear() // Pulisce anche sessionStorage

      // Reset stati
      setUser(null)
      setCantiere(null)
      setIsImpersonating(false)
      setImpersonatedUser(null)
      setExpirationWarning(null)
      setDaysUntilExpiration(null)
      setExpirationDate(null)

      // Forza reload completo della pagina per evitare cache
      window.location.replace('/login')
    }
  }

  const dismissExpirationWarning = () => {
    setExpirationWarning(null)
    setDaysUntilExpiration(null)
    setExpirationDate(null)
  }

  const value: AuthContextType = {
    user,
    cantiere,
    isAuthenticated,
    isLoading,
    isImpersonating,
    impersonatedUser,
    expirationWarning,
    daysUntilExpiration,
    expirationDate,
    login,
    loginCantiere,
    logout,
    checkAuth,
    impersonateUser,
    selectCantiere,
    clearCantiere,
    dismissExpirationWarning,
  }

  return (
    <AuthContext.Provider value={value}>
      {children}
    </AuthContext.Provider>
  )
}

{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/CMS/webapp-nextjs/src/components/ui/card.tsx"], "sourcesContent": ["import * as React from \"react\"\n\nimport { cn } from \"@/lib/utils\"\n\nfunction Card({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card\"\n      className={cn(\n        \"bg-card text-card-foreground flex flex-col gap-6 rounded-xl border py-6 shadow-sm\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction CardHeader({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-header\"\n      className={cn(\n        \"@container/card-header grid auto-rows-min grid-rows-[auto_auto] items-start gap-1.5 px-6 has-data-[slot=card-action]:grid-cols-[1fr_auto] [.border-b]:pb-6\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction CardTitle({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-title\"\n      className={cn(\"leading-none font-semibold\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction CardDescription({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-description\"\n      className={cn(\"text-muted-foreground text-sm\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction CardAction({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-action\"\n      className={cn(\n        \"col-start-2 row-span-2 row-start-1 self-start justify-self-end\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction CardContent({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-content\"\n      className={cn(\"px-6\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction CardFooter({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-footer\"\n      className={cn(\"flex items-center px-6 [.border-t]:pt-6\", className)}\n      {...props}\n    />\n  )\n}\n\nexport {\n  Card,\n  CardHeader,\n  CardFooter,\n  CardTitle,\n  CardAction,\n  CardDescription,\n  CardContent,\n}\n"], "names": [], "mappings": ";;;;;;;;;;AAEA;;;AAEA,SAAS,KAAK,EAAE,SAAS,EAAE,GAAG,OAAoC;IAChE,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,qFACA;QAED,GAAG,KAAK;;;;;;AAGf;KAXS;AAaT,SAAS,WAAW,EAAE,SAAS,EAAE,GAAG,OAAoC;IACtE,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,8JACA;QAED,GAAG,KAAK;;;;;;AAGf;MAXS;AAaT,SAAS,UAAU,EAAE,SAAS,EAAE,GAAG,OAAoC;IACrE,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,8BAA8B;QAC3C,GAAG,KAAK;;;;;;AAGf;MARS;AAUT,SAAS,gBAAgB,EAAE,SAAS,EAAE,GAAG,OAAoC;IAC3E,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,iCAAiC;QAC9C,GAAG,KAAK;;;;;;AAGf;MARS;AAUT,SAAS,WAAW,EAAE,SAAS,EAAE,GAAG,OAAoC;IACtE,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,kEACA;QAED,GAAG,KAAK;;;;;;AAGf;MAXS;AAaT,SAAS,YAAY,EAAE,SAAS,EAAE,GAAG,OAAoC;IACvE,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,QAAQ;QACrB,GAAG,KAAK;;;;;;AAGf;MARS;AAUT,SAAS,WAAW,EAAE,SAAS,EAAE,GAAG,OAAoC;IACtE,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,2CAA2C;QACxD,GAAG,KAAK;;;;;;AAGf;MARS", "debugId": null}}, {"offset": {"line": 122, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/CMS/webapp-nextjs/src/components/ui/badge.tsx"], "sourcesContent": ["import * as React from \"react\"\nimport { Slot } from \"@radix-ui/react-slot\"\nimport { cva, type VariantProps } from \"class-variance-authority\"\n\nimport { cn } from \"@/lib/utils\"\n\nconst badgeVariants = cva(\n  \"inline-flex items-center justify-center rounded-md border px-2 py-0.5 text-xs font-medium w-fit whitespace-nowrap shrink-0 [&>svg]:size-3 gap-1 [&>svg]:pointer-events-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive transition-[color,box-shadow] overflow-hidden\",\n  {\n    variants: {\n      variant: {\n        default:\n          \"border-transparent bg-primary text-primary-foreground [a&]:hover:bg-primary/90\",\n        secondary:\n          \"border-transparent bg-secondary text-secondary-foreground [a&]:hover:bg-secondary/90\",\n        destructive:\n          \"border-transparent bg-destructive text-white [a&]:hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60\",\n        outline:\n          \"text-foreground [a&]:hover:bg-accent [a&]:hover:text-accent-foreground\",\n      },\n    },\n    defaultVariants: {\n      variant: \"default\",\n    },\n  }\n)\n\nfunction Badge({\n  className,\n  variant,\n  asChild = false,\n  ...props\n}: React.ComponentProps<\"span\"> &\n  VariantProps<typeof badgeVariants> & { asChild?: boolean }) {\n  const Comp = asChild ? Slot : \"span\"\n\n  return (\n    <Comp\n      data-slot=\"badge\"\n      className={cn(badgeVariants({ variant }), className)}\n      {...props}\n    />\n  )\n}\n\nexport { Badge, badgeVariants }\n"], "names": [], "mappings": ";;;;;AACA;AACA;AAEA;;;;;AAEA,MAAM,gBAAgB,CAAA,GAAA,mKAAA,CAAA,MAAG,AAAD,EACtB,kZACA;IACE,UAAU;QACR,SAAS;YACP,SACE;YACF,WACE;YACF,aACE;YACF,SACE;QACJ;IACF;IACA,iBAAiB;QACf,SAAS;IACX;AACF;AAGF,SAAS,MAAM,EACb,SAAS,EACT,OAAO,EACP,UAAU,KAAK,EACf,GAAG,OAEuD;IAC1D,MAAM,OAAO,UAAU,mKAAA,CAAA,OAAI,GAAG;IAE9B,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,cAAc;YAAE;QAAQ,IAAI;QACzC,GAAG,KAAK;;;;;;AAGf;KAhBS", "debugId": null}}, {"offset": {"line": 174, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/CMS/webapp-nextjs/src/components/ui/progress.tsx"], "sourcesContent": ["\"use client\"\n\nimport * as React from \"react\"\nimport * as ProgressPrimitive from \"@radix-ui/react-progress\"\n\nimport { cn } from \"@/lib/utils\"\n\nfunction Progress({\n  className,\n  value,\n  ...props\n}: React.ComponentProps<typeof ProgressPrimitive.Root>) {\n  return (\n    <ProgressPrimitive.Root\n      data-slot=\"progress\"\n      className={cn(\n        \"bg-primary/20 relative h-2 w-full overflow-hidden rounded-full\",\n        className\n      )}\n      {...props}\n    >\n      <ProgressPrimitive.Indicator\n        data-slot=\"progress-indicator\"\n        className=\"bg-primary h-full w-full flex-1 transition-all\"\n        style={{ transform: `translateX(-${100 - (value || 0)}%)` }}\n      />\n    </ProgressPrimitive.Root>\n  )\n}\n\nexport { Progress }\n"], "names": [], "mappings": ";;;;AAGA;AAEA;AALA;;;;AAOA,SAAS,SAAS,EAChB,SAAS,EACT,KAAK,EACL,GAAG,OACiD;IACpD,qBACE,6LAAC,uKAAA,CAAA,OAAsB;QACrB,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,kEACA;QAED,GAAG,KAAK;kBAET,cAAA,6LAAC,uKAAA,CAAA,YAA2B;YAC1B,aAAU;YACV,WAAU;YACV,OAAO;gBAAE,WAAW,CAAC,YAAY,EAAE,MAAM,CAAC,SAAS,CAAC,EAAE,EAAE,CAAC;YAAC;;;;;;;;;;;AAIlE;KArBS", "debugId": null}}, {"offset": {"line": 219, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/CMS/webapp-nextjs/src/components/ui/tabs.tsx"], "sourcesContent": ["\"use client\"\n\nimport * as React from \"react\"\nimport * as TabsPrimitive from \"@radix-ui/react-tabs\"\n\nimport { cn } from \"@/lib/utils\"\n\nconst Tabs = TabsPrimitive.Root\n\nconst TabsList = React.forwardRef<\n  React.ElementRef<typeof TabsPrimitive.List>,\n  React.ComponentPropsWithoutRef<typeof TabsPrimitive.List>\n>(({ className, ...props }, ref) => (\n  <TabsPrimitive.List\n    ref={ref}\n    className={cn(\n      \"inline-flex h-10 items-center justify-center rounded-md bg-muted p-1 text-muted-foreground\",\n      className\n    )}\n    {...props}\n  />\n))\nTabsList.displayName = TabsPrimitive.List.displayName\n\nconst TabsTrigger = React.forwardRef<\n  React.ElementRef<typeof TabsPrimitive.Trigger>,\n  React.ComponentPropsWithoutRef<typeof TabsPrimitive.Trigger>\n>(({ className, ...props }, ref) => (\n  <TabsPrimitive.Trigger\n    ref={ref}\n    className={cn(\n      \"inline-flex items-center justify-center whitespace-nowrap rounded-sm px-3 py-1.5 text-sm font-medium ring-offset-background transition-all focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 data-[state=active]:bg-background data-[state=active]:text-foreground data-[state=active]:shadow-sm\",\n      className\n    )}\n    {...props}\n  />\n))\nTabsTrigger.displayName = TabsPrimitive.Trigger.displayName\n\nconst TabsContent = React.forwardRef<\n  React.ElementRef<typeof TabsPrimitive.Content>,\n  React.ComponentPropsWithoutRef<typeof TabsPrimitive.Content>\n>(({ className, ...props }, ref) => (\n  <TabsPrimitive.Content\n    ref={ref}\n    className={cn(\n      \"mt-2 ring-offset-background focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2\",\n      className\n    )}\n    {...props}\n  />\n))\nTabsContent.displayName = TabsPrimitive.Content.displayName\n\nexport { Tabs, TabsList, TabsTrigger, TabsContent }\n"], "names": [], "mappings": ";;;;;;;AAEA;AACA;AAEA;AALA;;;;;AAOA,MAAM,OAAO,mKAAA,CAAA,OAAkB;AAE/B,MAAM,yBAAW,CAAA,GAAA,6JAAA,CAAA,aAAgB,AAAD,OAG9B,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,6LAAC,mKAAA,CAAA,OAAkB;QACjB,KAAK;QACL,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,8FACA;QAED,GAAG,KAAK;;;;;;;AAGb,SAAS,WAAW,GAAG,mKAAA,CAAA,OAAkB,CAAC,WAAW;AAErD,MAAM,4BAAc,CAAA,GAAA,6JAAA,CAAA,aAAgB,AAAD,QAGjC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,6LAAC,mKAAA,CAAA,UAAqB;QACpB,KAAK;QACL,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,uYACA;QAED,GAAG,KAAK;;;;;;;AAGb,YAAY,WAAW,GAAG,mKAAA,CAAA,UAAqB,CAAC,WAAW;AAE3D,MAAM,4BAAc,CAAA,GAAA,6JAAA,CAAA,aAAgB,AAAD,QAGjC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,6LAAC,mKAAA,CAAA,UAAqB;QACpB,KAAK;QACL,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,mIACA;QAED,GAAG,KAAK;;;;;;;AAGb,YAAY,WAAW,GAAG,mKAAA,CAAA,UAAqB,CAAC,WAAW", "debugId": null}}, {"offset": {"line": 285, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/CMS/webapp-nextjs/src/hooks/useCantiere.ts"], "sourcesContent": ["'use client'\n\nimport { useState, useEffect } from 'react'\nimport { useAuth } from '@/contexts/AuthContext'\nimport { Cantiere } from '@/types'\n\ninterface UseCantiereResult {\n  cantiereId: number | null\n  cantiere: Cantiere | null\n  isValidCantiere: boolean\n  isLoading: boolean\n  error: string | null\n  validateCantiere: (id: number | string) => boolean\n  clearError: () => void\n}\n\n/**\n * Hook personalizzato per la gestione robusta del cantiere selezionato\n * Gestisce validazione, errori e sincronizzazione con AuthContext\n */\nexport function useCantiere(): UseCantiereResult {\n  const { cantiere, isLoading: authLoading } = useAuth()\n  const [cantiereId, setCantiereId] = useState<number | null>(null)\n  const [isLoading, setIsLoading] = useState(true)\n  const [error, setError] = useState<string | null>(null)\n\n  // Funzione per validare un ID cantiere\n  const validateCantiere = (id: number | string): boolean => {\n    if (id === null || id === undefined) return false\n    \n    const numId = typeof id === 'string' ? parseInt(id, 10) : id\n    \n    if (isNaN(numId) || numId <= 0) {\n      console.warn('🏗️ useCantiere: ID cantiere non valido:', id)\n      return false\n    }\n    \n    return true\n  }\n\n  // Effetto per sincronizzare con AuthContext e localStorage\n  useEffect(() => {\n    if (authLoading) {\n      console.log('🏗️ useCantiere: Autenticazione in corso...')\n      return\n    }\n\n    setIsLoading(true)\n    setError(null)\n\n    try {\n      let selectedId: number | null = null\n\n      // Priorità 1: Cantiere dal context di autenticazione\n      if (cantiere?.id_cantiere && validateCantiere(cantiere.id_cantiere)) {\n        selectedId = cantiere.id_cantiere\n        console.log('🏗️ useCantiere: Usando cantiere dal context:', selectedId)\n      } else {\n        // Priorità 2: Cantiere dal localStorage\n        const storedId = localStorage.getItem('selectedCantiereId')\n        if (storedId && validateCantiere(storedId)) {\n          selectedId = parseInt(storedId, 10)\n          console.log('🏗️ useCantiere: Usando cantiere dal localStorage:', selectedId)\n        }\n      }\n\n      if (selectedId) {\n        setCantiereId(selectedId)\n        console.log('🏗️ useCantiere: Cantiere valido impostato:', selectedId)\n      } else {\n        console.warn('🏗️ useCantiere: Nessun cantiere valido trovato')\n        setCantiereId(null)\n        setError('Nessun cantiere selezionato. Seleziona un cantiere per continuare.')\n      }\n    } catch (err) {\n      console.error('🏗️ useCantiere: Errore nella gestione cantiere:', err)\n      setError('Errore nella gestione del cantiere selezionato.')\n      setCantiereId(null)\n    } finally {\n      setIsLoading(false)\n    }\n  }, [cantiere, authLoading])\n\n  const clearError = () => setError(null)\n\n  return {\n    cantiereId,\n    cantiere,\n    isValidCantiere: cantiereId !== null && cantiereId > 0,\n    isLoading,\n    error,\n    validateCantiere,\n    clearError\n  }\n}\n\n/**\n * Hook semplificato che restituisce solo l'ID del cantiere valido o null\n */\nexport function useCantiereId(): number | null {\n  const { cantiereId } = useCantiere()\n  return cantiereId\n}\n\n/**\n * Hook che forza la presenza di un cantiere valido\n * Lancia un errore se non c'è un cantiere selezionato\n */\nexport function useRequiredCantiere(): { cantiereId: number; cantiere: Cantiere | null } {\n  const { cantiereId, cantiere, isLoading, error } = useCantiere()\n\n  if (isLoading) {\n    throw new Error('Caricamento cantiere in corso...')\n  }\n\n  if (error) {\n    throw new Error(error)\n  }\n\n  if (!cantiereId || cantiereId <= 0) {\n    throw new Error('Nessun cantiere selezionato. Seleziona un cantiere per continuare.')\n  }\n\n  return { cantiereId, cantiere }\n}\n"], "names": [], "mappings": ";;;;;AAEA;AACA;;AAHA;;;AAoBO,SAAS;;IACd,MAAM,EAAE,QAAQ,EAAE,WAAW,WAAW,EAAE,GAAG,CAAA,GAAA,kIAAA,CAAA,UAAO,AAAD;IACnD,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAiB;IAC5D,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC3C,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAiB;IAElD,uCAAuC;IACvC,MAAM,mBAAmB,CAAC;QACxB,IAAI,OAAO,QAAQ,OAAO,WAAW,OAAO;QAE5C,MAAM,QAAQ,OAAO,OAAO,WAAW,SAAS,IAAI,MAAM;QAE1D,IAAI,MAAM,UAAU,SAAS,GAAG;YAC9B,QAAQ,IAAI,CAAC,4CAA4C;YACzD,OAAO;QACT;QAEA,OAAO;IACT;IAEA,2DAA2D;IAC3D,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;iCAAE;YACR,IAAI,aAAa;gBACf,QAAQ,GAAG,CAAC;gBACZ;YACF;YAEA,aAAa;YACb,SAAS;YAET,IAAI;gBACF,IAAI,aAA4B;gBAEhC,qDAAqD;gBACrD,IAAI,UAAU,eAAe,iBAAiB,SAAS,WAAW,GAAG;oBACnE,aAAa,SAAS,WAAW;oBACjC,QAAQ,GAAG,CAAC,iDAAiD;gBAC/D,OAAO;oBACL,wCAAwC;oBACxC,MAAM,WAAW,aAAa,OAAO,CAAC;oBACtC,IAAI,YAAY,iBAAiB,WAAW;wBAC1C,aAAa,SAAS,UAAU;wBAChC,QAAQ,GAAG,CAAC,sDAAsD;oBACpE;gBACF;gBAEA,IAAI,YAAY;oBACd,cAAc;oBACd,QAAQ,GAAG,CAAC,+CAA+C;gBAC7D,OAAO;oBACL,QAAQ,IAAI,CAAC;oBACb,cAAc;oBACd,SAAS;gBACX;YACF,EAAE,OAAO,KAAK;gBACZ,QAAQ,KAAK,CAAC,oDAAoD;gBAClE,SAAS;gBACT,cAAc;YAChB,SAAU;gBACR,aAAa;YACf;QACF;gCAAG;QAAC;QAAU;KAAY;IAE1B,MAAM,aAAa,IAAM,SAAS;IAElC,OAAO;QACL;QACA;QACA,iBAAiB,eAAe,QAAQ,aAAa;QACrD;QACA;QACA;QACA;IACF;AACF;GA1EgB;;QAC+B,kIAAA,CAAA,UAAO;;;AA8E/C,SAAS;;IACd,MAAM,EAAE,UAAU,EAAE,GAAG;IACvB,OAAO;AACT;IAHgB;;QACS;;;AAQlB,SAAS;;IACd,MAAM,EAAE,UAAU,EAAE,QAAQ,EAAE,SAAS,EAAE,KAAK,EAAE,GAAG;IAEnD,IAAI,WAAW;QACb,MAAM,IAAI,MAAM;IAClB;IAEA,IAAI,OAAO;QACT,MAAM,IAAI,MAAM;IAClB;IAEA,IAAI,CAAC,cAAc,cAAc,GAAG;QAClC,MAAM,IAAI,MAAM;IAClB;IAEA,OAAO;QAAE;QAAY;IAAS;AAChC;IAhBgB;;QACqC", "debugId": null}}, {"offset": {"line": 412, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/CMS/webapp-nextjs/src/app/reports/page.tsx"], "sourcesContent": ["'use client'\n\nimport { useState, useEffect } from 'react'\n\nimport { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'\nimport { Button } from '@/components/ui/button'\nimport { Badge } from '@/components/ui/badge'\nimport { Progress } from '@/components/ui/progress'\nimport { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs'\nimport { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'\nimport { useAuth } from '@/contexts/AuthContext'\nimport { useCantiere } from '@/hooks/useCantiere'\nimport { reportsApi, cantieriApi } from '@/lib/api'\nimport { ReportAvanzamento, ReportBOQ, Cantiere } from '@/types'\nimport {\n  BarChart3,\n  Download,\n  Calendar,\n  TrendingUp,\n  Target,\n  Activity,\n  Clock,\n  CheckCircle,\n  Loader2,\n  AlertCircle,\n  AlertTriangle,\n  FileText,\n  Package,\n  Users,\n  Zap,\n  RefreshCw\n} from 'lucide-react'\nimport {\n  BarChart,\n  Bar,\n  XAxis,\n  YAxis,\n  CartesianGrid,\n  Tooltip,\n  ResponsiveContainer,\n  PieChart,\n  Pie,\n  Cell,\n  LineChart,\n  Line,\n  Area,\n  AreaChart\n} from 'recharts'\n\nexport default function ReportsPage() {\n  const [activeTab, setActiveTab] = useState('avanzamento')\n  const [selectedPeriod, setSelectedPeriod] = useState('month')\n  const [reportAvanzamento, setReportAvanzamento] = useState<any>(null)\n  const [reportBOQ, setReportBOQ] = useState<any>(null)\n  const [reportUtilizzoBobine, setReportUtilizzoBobine] = useState<any>(null)\n  const [reportProgress, setReportProgress] = useState<any>(null)\n  const [isLoading, setIsLoading] = useState(true)\n  const [error, setError] = useState('')\n\n  const { user, isLoading: authLoading } = useAuth()\n  const { cantiereId, cantiere, isValidCantiere, isLoading: cantiereLoading, error: cantiereError } = useCantiere()\n\n  // Load all basic reports on component mount - MIGLIORATO per evitare race conditions\n  useEffect(() => {\n\n    const loadAllReports = async () => {\n      setIsLoading(true)\n      try {\n        // Usa il cantiere dal nuovo hook per gestione robusta\n        const currentCantiereId = cantiereId\n\n        // Test del token\n        const token = localStorage.getItem('token')\n        console.log('🏗️ ReportsPage: Caricamento report per cantiere:', currentCantiereId)\n\n        if (!currentCantiereId || currentCantiereId <= 0) {\n          console.warn('🏗️ ReportsPage: Nessun cantiere valido selezionato')\n          setError(cantiereError || 'Nessun cantiere selezionato. Seleziona un cantiere da \"Gestisci Cantieri\".')\n          setIsLoading(false)\n          return\n        }\n\n        // Create individual promises that handle their own errors - come nella webapp originale\n        // Aggiungiamo il parametro formato=video per ottenere i dati invece dei file\n\n        // Helper function per timeout - aumentato timeout per API lente\n        const fetchWithTimeout = (url: string, options: any, timeout = 30000) => {\n          return Promise.race([\n            fetch(url, options),\n            new Promise((_, reject) =>\n              setTimeout(() => reject(new Error(`Timeout dopo ${timeout/1000}s per ${url}`)), timeout)\n            )\n          ]) as Promise<Response>\n        }\n\n        const progressPromise = fetchWithTimeout(`http://localhost:8001/api/reports/${currentCantiereId}/progress?formato=video`, {\n          headers: {\n            'Authorization': `Bearer ${localStorage.getItem('token')}`,\n            'Content-Type': 'application/json'\n          }\n        })\n          .then(res => {\n            return res.json()\n          })\n          .then(data => {\n            return data\n          })\n          .catch(err => {\n            return { content: null, error: err.message.includes('Timeout') ? 'Timeout API Progress' : 'Errore API Progress' }\n          })\n\n        const boqPromise = fetchWithTimeout(`http://localhost:8001/api/reports/${currentCantiereId}/boq?formato=video`, {\n          headers: {\n            'Authorization': `Bearer ${localStorage.getItem('token')}`,\n            'Content-Type': 'application/json'\n          }\n        })\n          .then(res => {\n            return res.json()\n          })\n          .then(data => {\n            return data\n          })\n          .catch(err => {\n            return { content: null, error: err.message.includes('Timeout') ? 'Timeout API BOQ' : 'Errore API BOQ' }\n          })\n\n        const utilizzoPromise = fetchWithTimeout(`http://localhost:8001/api/reports/${currentCantiereId}/storico-bobine?formato=video`, {\n          headers: {\n            'Authorization': `Bearer ${localStorage.getItem('token')}`,\n            'Content-Type': 'application/json'\n          }\n        })\n          .then(res => {\n            return res.json()\n          })\n          .then(data => {\n            return data\n          })\n          .catch(err => {\n            return { content: null, error: err.message.includes('Timeout') ? 'Timeout API Utilizzo Bobine' : 'Errore API Utilizzo Bobine' }\n          })\n\n        // Wait for all promises to resolve (they won't reject due to the catch handlers)\n        const [progressData, boqData, utilizzoData] = await Promise.all([\n          progressPromise,\n          boqPromise,\n          utilizzoPromise\n        ])\n\n        // Debug: vediamo la struttura dei dati BOQ\n\n        // Set the data for each report - manteniamo la struttura completa con .content\n        setReportProgress(progressData)\n        setReportBOQ(boqData)\n        setReportUtilizzoBobine(utilizzoData)\n\n        // Check for timeout errors specifically\n        const hasTimeoutErrors = [progressData, boqData, utilizzoData].some(data =>\n          data?.error?.includes('Timeout')\n        )\n\n        // Only set error to null if we successfully loaded at least one report\n        if (progressData?.content || boqData?.content || utilizzoData?.content ||\n            progressData || boqData || utilizzoData) {\n          if (hasTimeoutErrors) {\n            setError('Alcuni report hanno riscontrato timeout. I dati disponibili sono mostrati sotto.')\n          } else {\n            setError('')\n          }\n        } else {\n          setError('Errore nel caricamento dei report. Riprova più tardi.')\n        }\n\n        // Always stop loading after processing the data\n        setIsLoading(false)\n      } catch (err) {\n        // This catch block should rarely be hit due to the individual error handling above\n        setError('Errore nel caricamento dei report. Riprova più tardi.')\n      } finally {\n        setIsLoading(false)\n      }\n    }\n\n    // Aspetta che l'autenticazione e il cantiere siano caricati\n    if (authLoading || cantiereLoading) {\n      console.log('🏗️ ReportsPage: Caricamento in corso, attendo...', { authLoading, cantiereLoading })\n      return\n    }\n\n    // Carica i report se c'è un cantiere valido\n    if (isValidCantiere && cantiereId && cantiereId > 0) {\n      console.log('🏗️ ReportsPage: Cantiere valido trovato, carico report')\n      loadAllReports()\n    } else {\n      console.warn('🏗️ ReportsPage: Nessun cantiere valido')\n      setIsLoading(false)\n      setError(cantiereError || 'Nessun cantiere selezionato. Seleziona un cantiere da \"Gestisci Cantieri\".')\n    }\n  }, [cantiereId, isValidCantiere, authLoading, cantiereLoading, cantiereError])\n\n  const handleRefresh = () => {\n    // Ricarica i report per il cantiere corrente\n    if (isValidCantiere && cantiereId) {\n      console.log('🏗️ ReportsPage: Refresh report per cantiere:', cantiereId)\n      setIsLoading(true)\n      setError('')\n      setReportProgress(null)\n      setReportBOQ(null)\n      setReportUtilizzoBobine(null)\n      setReportAvanzamento(null)\n      // Ricarica direttamente senza reload della pagina\n      loadAllReports()\n    } else {\n      console.warn('🏗️ ReportsPage: Impossibile fare refresh, nessun cantiere valido')\n    }\n  }\n\n  const handleExportReport = async (reportType: string, format: string = 'pdf') => {\n    try {\n      const currentCantiereId = cantiere?.id_cantiere\n      if (!currentCantiereId) {\n        return\n      }\n\n      let response\n      switch (reportType) {\n        case 'progress':\n          response = await reportsApi.getReportProgress(currentCantiereId)\n          break\n        case 'boq':\n          response = await reportsApi.getReportBOQ(currentCantiereId)\n          break\n        case 'utilizzo-bobine':\n          response = await reportsApi.getReportUtilizzoBobine(currentCantiereId)\n          break\n        default:\n          return\n      }\n\n      if (response.data.file_url) {\n        // Apri il file in una nuova finestra\n        window.open(response.data.file_url, '_blank')\n      }\n    } catch (error) {\n    }\n  }\n\n  // Calcolo IAP (Indice di Avanzamento Ponderato)\n  const calculateIAP = (nTot: number, nInst: number, nColl: number, nCert: number): number => {\n    const Wp = 2.0  // Peso fase Posa\n    const Wc = 1.5  // Peso fase Collegamento\n    const Wz = 0.5  // Peso fase Certificazione\n\n    if (nTot === 0) return 0\n\n    const sforzoSoloInstallati = (nInst - nColl) * Wp\n    const sforzoSoloCollegati = (nColl - nCert) * (Wp + Wc)\n    const sforzoCertificati = nCert * (Wp + Wc + Wz)\n    const numeratore = sforzoSoloInstallati + sforzoSoloCollegati + sforzoCertificati\n\n    const denominatore = nTot * (Wp + Wc + Wz)\n    return Math.round((numeratore / denominatore) * 10000) / 100\n  }\n\n  return (\n    <div className=\"min-h-screen bg-gradient-to-br from-slate-50 to-slate-100 p-6\">\n      <div className=\"max-w-[90%] mx-auto space-y-6\">\n\n        {/* Header */}\n        <div className=\"flex justify-between items-center\">\n          <div>\n            <h1 className=\"text-3xl font-bold text-slate-900\">Reports</h1>\n            <p className=\"text-slate-600 mt-1\">\n              {cantiere?.commessa ? `Cantiere: ${cantiere.commessa}` : 'Seleziona un cantiere per visualizzare i report'}\n            </p>\n          </div>\n          <div className=\"flex gap-2\">\n            <Button variant=\"outline\" size=\"sm\" onClick={handleRefresh}>\n              <RefreshCw className=\"h-4 w-4 mr-2\" />\n              Aggiorna\n            </Button>\n          </div>\n        </div>\n\n        {/* Loading State */}\n        {(isLoading || authLoading) ? (\n          <div className=\"flex items-center justify-center py-12\">\n            <div className=\"flex items-center gap-2\">\n              <Loader2 className=\"h-6 w-6 animate-spin\" />\n              <span>Caricamento report...</span>\n            </div>\n          </div>\n        ) : error ? (\n          <div className=\"p-6 border border-amber-200 rounded-lg bg-amber-50\">\n            <div className=\"flex items-center mb-4\">\n              <AlertCircle className=\"h-5 w-5 text-amber-600 mr-2\" />\n              <span className=\"text-amber-800 font-medium\">\n                {error.includes('Nessun cantiere selezionato') || error.includes('Cantiere non selezionato') ? 'Cantiere non selezionato' :\n                 error.includes('timeout') || error.includes('Timeout') ? 'Timeout API' : 'Errore caricamento report'}\n              </span>\n            </div>\n            <p className=\"text-amber-700 mb-4\">{error}</p>\n            {error.includes('timeout') || error.includes('Timeout') ? (\n              <div className=\"mb-4 p-3 bg-blue-50 border border-blue-200 rounded\">\n                <p className=\"text-blue-800 text-sm\">\n                  💡 <strong>Suggerimento:</strong> Le API stanno impiegando più tempo del previsto.\n                  Prova ad aggiornare la pagina o riprova tra qualche minuto.\n                </p>\n              </div>\n            ) : null}\n            <div className=\"flex gap-2\">\n              <Button\n                onClick={() => window.location.href = '/cantieri'}\n                className=\"bg-amber-600 hover:bg-amber-700 text-white\"\n              >\n                Gestisci Cantieri\n              </Button>\n              <Button\n                variant=\"outline\"\n                onClick={handleRefresh}\n                className=\"border-amber-600 text-amber-700 hover:bg-amber-100\"\n              >\n                <RefreshCw className=\"h-4 w-4 mr-2\" />\n                Riprova\n              </Button>\n            </div>\n          </div>\n        ) : (\n          <Tabs value={activeTab} onValueChange={setActiveTab} className=\"w-full\">\n            <TabsList className=\"grid w-full grid-cols-4\">\n              <TabsTrigger value=\"avanzamento\" className=\"flex items-center gap-2\">\n                <Target className=\"h-4 w-4\" />\n                Avanzamento\n              </TabsTrigger>\n              <TabsTrigger value=\"boq\" className=\"flex items-center gap-2\">\n                <FileText className=\"h-4 w-4\" />\n                BOQ\n              </TabsTrigger>\n              <TabsTrigger value=\"bobine\" className=\"flex items-center gap-2\">\n                <Package className=\"h-4 w-4\" />\n                Bobine\n              </TabsTrigger>\n              <TabsTrigger value=\"produttivita\" className=\"flex items-center gap-2\">\n                <Zap className=\"h-4 w-4\" />\n                Produttività\n              </TabsTrigger>\n            </TabsList>\n\n            {/* Tab Content: Avanzamento */}\n            <TabsContent value=\"avanzamento\" className=\"space-y-6\">\n              {reportProgress?.content ? (\n                <>\n                  {/* KPI Cards */}\n                  <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6\">\n                    <Card className=\"border-l-4 border-l-blue-500\">\n                      <CardHeader className=\"flex flex-row items-center justify-between space-y-0 pb-2\">\n                        <CardTitle className=\"text-sm font-medium text-slate-600\">Metri Totali</CardTitle>\n                        <Target className=\"h-4 w-4 text-blue-500\" />\n                      </CardHeader>\n                      <CardContent>\n                        <div className=\"text-2xl font-bold text-slate-900\">\n                          {reportProgress.content.metri_totali?.toLocaleString() || 0}m\n                        </div>\n                        <p className=\"text-xs text-slate-500 mt-2\">\n                          {reportProgress.content.totale_cavi || 0} cavi totali\n                        </p>\n                      </CardContent>\n                    </Card>\n\n                    <Card className=\"border-l-4 border-l-green-500\">\n                      <CardHeader className=\"flex flex-row items-center justify-between space-y-0 pb-2\">\n                        <CardTitle className=\"text-sm font-medium text-slate-600\">Metri Posati</CardTitle>\n                        <CheckCircle className=\"h-4 w-4 text-green-500\" />\n                      </CardHeader>\n                      <CardContent>\n                        <div className=\"text-2xl font-bold text-slate-900\">\n                          {reportProgress.content.metri_posati?.toLocaleString() || 0}m\n                        </div>\n                        <Progress\n                          value={reportProgress.content.percentuale_avanzamento || 0}\n                          className=\"mt-2\"\n                        />\n                        <p className=\"text-xs text-slate-500 mt-2\">\n                          {reportProgress.content.percentuale_avanzamento?.toFixed(1) || 0}% completato\n                        </p>\n                      </CardContent>\n                    </Card>\n\n                    <Card className=\"border-l-4 border-l-purple-500\">\n                      <CardHeader className=\"flex flex-row items-center justify-between space-y-0 pb-2\">\n                        <CardTitle className=\"text-sm font-medium text-slate-600\">Media Giornaliera</CardTitle>\n                        <TrendingUp className=\"h-4 w-4 text-purple-500\" />\n                      </CardHeader>\n                      <CardContent>\n                        <div className=\"text-2xl font-bold text-slate-900\">\n                          {reportProgress.content.media_giornaliera?.toFixed(1) || 0}m\n                        </div>\n                        <p className=\"text-xs text-slate-500\">metri/giorno</p>\n                        <div className=\"flex items-center mt-2\">\n                          <Activity className=\"h-3 w-3 text-purple-500 mr-1\" />\n                          <span className=\"text-xs text-purple-600\">\n                            {reportProgress.content.giorni_lavorativi_effettivi || 0} giorni attivi\n                          </span>\n                        </div>\n                      </CardContent>\n                    </Card>\n\n                    <Card className=\"border-l-4 border-l-orange-500\">\n                      <CardHeader className=\"flex flex-row items-center justify-between space-y-0 pb-2\">\n                        <CardTitle className=\"text-sm font-medium text-slate-600\">Completamento</CardTitle>\n                        <Clock className=\"h-4 w-4 text-orange-500\" />\n                      </CardHeader>\n                      <CardContent>\n                        <div className=\"text-2xl font-bold text-slate-900\">\n                          {reportProgress.content.data_completamento || 'N/A'}\n                        </div>\n                        <p className=\"text-xs text-slate-500\">stima completamento</p>\n                        <div className=\"flex items-center mt-2\">\n                          <Calendar className=\"h-3 w-3 text-orange-500 mr-1\" />\n                          <span className=\"text-xs text-orange-600\">\n                            {reportProgress.content.giorni_stimati || 0} giorni rimanenti\n                          </span>\n                        </div>\n                      </CardContent>\n                    </Card>\n                  </div>\n\n                  {/* Charts */}\n                  <div className=\"grid grid-cols-1 lg:grid-cols-2 gap-6\">\n                    {/* Posa Recente */}\n                    <Card>\n                      <CardHeader className=\"flex flex-row items-center justify-between\">\n                        <div>\n                          <CardTitle>Posa Recente</CardTitle>\n                          <CardDescription>Ultimi 10 giorni di attività</CardDescription>\n                        </div>\n                        <Button\n                          variant=\"outline\"\n                          size=\"sm\"\n                          onClick={() => handleExportReport('progress', 'pdf')}\n                        >\n                          <Download className=\"h-4 w-4 mr-2\" />\n                          PDF\n                        </Button>\n                      </CardHeader>\n                      <CardContent>\n                        <ResponsiveContainer width=\"100%\" height={300}>\n                          <BarChart data={reportProgress.content.posa_recente || []}>\n                            <CartesianGrid strokeDasharray=\"3 3\" />\n                            <XAxis dataKey=\"data\" />\n                            <YAxis />\n                            <Tooltip />\n                            <Bar dataKey=\"metri\" fill=\"#3b82f6\" name=\"Metri Posati\" />\n                          </BarChart>\n                        </ResponsiveContainer>\n                      </CardContent>\n                    </Card>\n\n                    {/* Statistiche Cavi */}\n                    <Card>\n                      <CardHeader>\n                        <CardTitle>Stato Cavi</CardTitle>\n                        <CardDescription>Distribuzione per stato di avanzamento</CardDescription>\n                      </CardHeader>\n                      <CardContent>\n                        <div className=\"space-y-4\">\n                          <div className=\"flex justify-between items-center\">\n                            <span className=\"text-sm font-medium\">Cavi Posati</span>\n                            <Badge variant=\"secondary\">\n                              {reportProgress.content.cavi_posati || 0}\n                            </Badge>\n                          </div>\n                          <div className=\"flex justify-between items-center\">\n                            <span className=\"text-sm font-medium\">Cavi Rimanenti</span>\n                            <Badge variant=\"outline\">\n                              {reportProgress.content.cavi_rimanenti || 0}\n                            </Badge>\n                          </div>\n                          <div className=\"flex justify-between items-center\">\n                            <span className=\"text-sm font-medium\">Percentuale Cavi</span>\n                            <Badge variant=\"default\">\n                              {reportProgress.content.percentuale_cavi?.toFixed(1) || 0}%\n                            </Badge>\n                          </div>\n                        </div>\n                      </CardContent>\n                    </Card>\n                  </div>\n                </>\n              ) : (\n                <div className=\"text-center py-12 text-slate-500\">\n                  <Target className=\"h-12 w-12 mx-auto mb-4 text-slate-300\" />\n                  <p>Nessun dato di avanzamento disponibile</p>\n                </div>\n              )}\n            </TabsContent>\n\n            {/* Tab Content: BOQ */}\n            <TabsContent value=\"boq\" className=\"space-y-6\">\n              {reportBOQ?.error ? (\n                <div className=\"text-center py-12\">\n                  <AlertCircle className=\"h-12 w-12 mx-auto mb-4 text-amber-500\" />\n                  <h3 className=\"text-lg font-semibold text-slate-700 mb-2\">API BOQ Temporaneamente Non Disponibile</h3>\n                  <p className=\"text-slate-500 mb-4\">Il servizio BOQ sta riscontrando problemi di performance.</p>\n                  <p className=\"text-sm text-slate-400\">Stiamo lavorando per risolvere il problema.</p>\n                </div>\n              ) : reportBOQ?.content ? (\n                <>\n                  <div className=\"flex justify-between items-center\">\n                    <div>\n                      <h3 className=\"text-lg font-semibold\">📋 Bill of Quantities - Distinta Materiali</h3>\n                      <p className=\"text-sm text-slate-600\">Riepilogo completo dei materiali per tipologia di cavo</p>\n                    </div>\n                    <Button\n                      variant=\"outline\"\n                      size=\"sm\"\n                      onClick={() => handleExportReport('boq', 'excel')}\n                    >\n                      <Download className=\"h-4 w-4 mr-2\" />\n                      Excel\n                    </Button>\n                  </div>\n\n                  {/* Alert Metri Orfani */}\n                  {reportBOQ.content.metri_orfani && reportBOQ.content.metri_orfani.metri_orfani_totali > 0 && (\n                    <Card className=\"border-red-200 bg-red-50\">\n                      <CardHeader className=\"pb-3\">\n                        <CardTitle className=\"text-red-700 flex items-center\">\n                          <AlertTriangle className=\"h-5 w-5 mr-2\" />\n                          🚨 METRI POSATI SENZA TRACCIABILITÀ BOBINA\n                        </CardTitle>\n                      </CardHeader>\n                      <CardContent>\n                        <p className=\"text-red-800 font-medium mb-2\">\n                          <strong>{reportBOQ.content.metri_orfani.metri_orfani_totali}m</strong> installati con BOBINA_VUOTA\n                          ({reportBOQ.content.metri_orfani.num_cavi_orfani} cavi)\n                        </p>\n                        <div className=\"text-sm text-red-700 space-y-1\">\n                          {Array.isArray(reportBOQ.content.metri_orfani.dettaglio_per_categoria) ?\n                            reportBOQ.content.metri_orfani.dettaglio_per_categoria.map((categoria: any, index: number) => (\n                              <div key={index}>\n                                • <strong>{categoria.tipologia} {categoria.formazione}</strong>: {categoria.metri_orfani}m ({categoria.num_cavi} cavi)\n                              </div>\n                            )) : (\n                              <div>Dettaglio metri orfani non disponibile</div>\n                            )\n                          }\n                        </div>\n                        <div className=\"mt-3 p-3 bg-amber-50 border border-amber-200 rounded\">\n                          <p className=\"text-amber-800 text-sm\">\n                            ⚠️ <strong>NOTA:</strong> I metri orfani NON sono inclusi nel calcolo acquisti.\n                            Prima di acquistare, verificare se questi metri possono essere associati a bobine esistenti.\n                          </p>\n                        </div>\n                      </CardContent>\n                    </Card>\n                  )}\n\n                  {/* Riepilogo Generale */}\n                  {reportBOQ.content.riepilogo && (\n                    <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4\">\n                      <Card className=\"border-l-4 border-l-blue-500\">\n                        <CardHeader className=\"pb-2\">\n                          <CardTitle className=\"text-sm font-medium text-slate-600\">Metri da Acquistare</CardTitle>\n                        </CardHeader>\n                        <CardContent>\n                          <div className=\"text-2xl font-bold text-blue-600\">\n                            {reportBOQ.content.riepilogo.totale_metri_mancanti?.toLocaleString() || 0}m\n                          </div>\n                          <p className=\"text-xs text-slate-500 mt-1\">per completamento progetto</p>\n                        </CardContent>\n                      </Card>\n\n                      <Card className=\"border-l-4 border-l-green-500\">\n                        <CardHeader className=\"pb-2\">\n                          <CardTitle className=\"text-sm font-medium text-slate-600\">Metri Residui</CardTitle>\n                        </CardHeader>\n                        <CardContent>\n                          <div className=\"text-2xl font-bold text-green-600\">\n                            {reportBOQ.content.riepilogo.totale_metri_residui?.toLocaleString() || 0}m\n                          </div>\n                          <p className=\"text-xs text-slate-500 mt-1\">disponibili in magazzino</p>\n                        </CardContent>\n                      </Card>\n\n                      <Card className=\"border-l-4 border-l-purple-500\">\n                        <CardHeader className=\"pb-2\">\n                          <CardTitle className=\"text-sm font-medium text-slate-600\">Completamento</CardTitle>\n                        </CardHeader>\n                        <CardContent>\n                          <div className=\"text-2xl font-bold text-purple-600\">\n                            {reportBOQ.content.riepilogo.percentuale_completamento?.toFixed(1) || 0}%\n                          </div>\n                          <p className=\"text-xs text-slate-500 mt-1\">progetto completato</p>\n                        </CardContent>\n                      </Card>\n\n                      <Card className=\"border-l-4 border-l-orange-500\">\n                        <CardHeader className=\"pb-2\">\n                          <CardTitle className=\"text-sm font-medium text-slate-600\">Categorie</CardTitle>\n                        </CardHeader>\n                        <CardContent>\n                          <div className=\"text-2xl font-bold text-orange-600\">\n                            {reportBOQ.content.riepilogo.categorie_necessitano_acquisto || 0}\n                          </div>\n                          <p className=\"text-xs text-slate-500 mt-1\">necessitano acquisto</p>\n                        </CardContent>\n                      </Card>\n                    </div>\n                  )}\n\n                  {/* Distinta Materiali */}\n                  <Card>\n                    <CardHeader>\n                      <CardTitle>Distinta Materiali</CardTitle>\n                      <CardDescription>Fabbisogno materiali raggruppati per tipologia e formazione</CardDescription>\n                    </CardHeader>\n                    <CardContent>\n                      <div className=\"overflow-x-auto\">\n                        <table className=\"w-full text-sm\">\n                          <thead>\n                            <tr className=\"border-b\">\n                              <th className=\"text-left p-2\">Tipologia</th>\n                              <th className=\"text-left p-2\">Formazione</th>\n                              <th className=\"text-right p-2\">Cavi</th>\n                              <th className=\"text-right p-2\">Metri Teorici</th>\n                              <th className=\"text-right p-2\">Metri Posati</th>\n                              <th className=\"text-right p-2\">Metri da Posare</th>\n                              <th className=\"text-right p-2\">Bobine</th>\n                              <th className=\"text-right p-2\">Metri Residui</th>\n                              <th className=\"text-right p-2\">Metri Mancanti</th>\n                              <th className=\"text-center p-2\">Acquisto</th>\n                            </tr>\n                          </thead>\n                          <tbody>\n                            {reportBOQ.content.distinta_materiali?.map((item: any, index: number) => (\n                              <tr key={index} className={`border-b hover:bg-slate-50 ${item.ha_bobina_vuota ? 'bg-red-50' : ''}`}>\n                                <td className=\"p-2 font-medium\">{item.tipologia}</td>\n                                <td className=\"p-2\">{item.formazione}</td>\n                                <td className=\"p-2 text-right\">{item.num_cavi}</td>\n                                <td className=\"p-2 text-right\">{item.metri_teorici_totali?.toLocaleString()}</td>\n                                <td className=\"p-2 text-right\">{item.metri_reali_posati?.toLocaleString()}</td>\n                                <td className=\"p-2 text-right\">{item.metri_da_posare?.toLocaleString()}</td>\n                                <td className=\"p-2 text-right\">{item.num_bobine}</td>\n                                <td className=\"p-2 text-right\">{item.metri_residui?.toLocaleString()}</td>\n                                <td className=\"p-2 text-right font-medium\">\n                                  {item.metri_mancanti > 0 ? (\n                                    <span className=\"text-red-600\">{item.metri_mancanti?.toLocaleString()}m</span>\n                                  ) : (\n                                    <span className=\"text-green-600\">0m</span>\n                                  )}\n                                </td>\n                                <td className=\"p-2 text-center\">\n                                  {item.necessita_acquisto ? (\n                                    <Badge variant=\"destructive\">Sì</Badge>\n                                  ) : (\n                                    <Badge variant=\"secondary\">No</Badge>\n                                  )}\n                                </td>\n                              </tr>\n                            ))}\n                          </tbody>\n                        </table>\n                      </div>\n                    </CardContent>\n                  </Card>\n\n                  {/* Bobine per Tipo */}\n                  <Card>\n                    <CardHeader>\n                      <CardTitle>Bobine Disponibili</CardTitle>\n                      <CardDescription>Inventario bobine per tipologia</CardDescription>\n                    </CardHeader>\n                    <CardContent>\n                      <div className=\"overflow-x-auto\">\n                        <table className=\"w-full text-sm\">\n                          <thead>\n                            <tr className=\"border-b\">\n                              <th className=\"text-left p-2\">Tipologia</th>\n                              <th className=\"text-left p-2\">Formazione</th>\n                              <th className=\"text-right p-2\">Numero Bobine</th>\n                              <th className=\"text-right p-2\">Metri Disponibili</th>\n                            </tr>\n                          </thead>\n                          <tbody>\n                            {reportBOQ.content.bobine_per_tipo?.map((item: any, index: number) => (\n                              <tr key={index} className=\"border-b hover:bg-slate-50\">\n                                <td className=\"p-2 font-medium\">{item.tipologia}</td>\n                                <td className=\"p-2\">{item.formazione}</td>\n                                <td className=\"p-2 text-right\">{item.num_bobine}</td>\n                                <td className=\"p-2 text-right\">\n                                  <span className={item.metri_disponibili > 0 ? 'text-green-600' : 'text-red-600'}>\n                                    {item.metri_disponibili?.toLocaleString()}\n                                  </span>\n                                </td>\n                              </tr>\n                            ))}\n                          </tbody>\n                        </table>\n                      </div>\n                    </CardContent>\n                  </Card>\n                </>\n              ) : (\n                <div className=\"text-center py-12 text-slate-500\">\n                  <FileText className=\"h-12 w-12 mx-auto mb-4 text-slate-300\" />\n                  <p>Nessun dato BOQ disponibile</p>\n                </div>\n              )}\n            </TabsContent>\n\n            {/* Tab Content: Bobine */}\n            <TabsContent value=\"bobine\" className=\"space-y-6\">\n              {reportUtilizzoBobine?.content ? (\n                <>\n                  <div className=\"flex justify-between items-center\">\n                    <div>\n                      <h3 className=\"text-lg font-semibold\">Utilizzo Bobine</h3>\n                      <p className=\"text-sm text-slate-600\">Stato e utilizzo delle bobine</p>\n                    </div>\n                    <Button\n                      variant=\"outline\"\n                      size=\"sm\"\n                      onClick={() => handleExportReport('utilizzo-bobine', 'excel')}\n                    >\n                      <Download className=\"h-4 w-4 mr-2\" />\n                      Excel\n                    </Button>\n                  </div>\n\n                  {/* Statistiche Bobine */}\n                  <div className=\"grid grid-cols-1 md:grid-cols-3 gap-6\">\n                    <Card>\n                      <CardHeader className=\"flex flex-row items-center justify-between space-y-0 pb-2\">\n                        <CardTitle className=\"text-sm font-medium text-slate-600\">Totale Bobine</CardTitle>\n                        <Package className=\"h-4 w-4 text-blue-500\" />\n                      </CardHeader>\n                      <CardContent>\n                        <div className=\"text-2xl font-bold text-slate-900\">\n                          {reportUtilizzoBobine.content.totale_bobine || 0}\n                        </div>\n                        <p className=\"text-xs text-slate-500\">bobine nel cantiere</p>\n                      </CardContent>\n                    </Card>\n\n                    <Card>\n                      <CardHeader className=\"flex flex-row items-center justify-between space-y-0 pb-2\">\n                        <CardTitle className=\"text-sm font-medium text-slate-600\">Bobine Attive</CardTitle>\n                        <Activity className=\"h-4 w-4 text-green-500\" />\n                      </CardHeader>\n                      <CardContent>\n                        <div className=\"text-2xl font-bold text-slate-900\">\n                          {reportUtilizzoBobine.content.bobine?.filter((b: any) =>\n                            b.stato === 'In uso' || b.stato === 'Disponibile'\n                          ).length || 0}\n                        </div>\n                        <p className=\"text-xs text-slate-500\">disponibili/in uso</p>\n                      </CardContent>\n                    </Card>\n\n                    <Card>\n                      <CardHeader className=\"flex flex-row items-center justify-between space-y-0 pb-2\">\n                        <CardTitle className=\"text-sm font-medium text-slate-600\">Utilizzo Medio</CardTitle>\n                        <TrendingUp className=\"h-4 w-4 text-purple-500\" />\n                      </CardHeader>\n                      <CardContent>\n                        <div className=\"text-2xl font-bold text-slate-900\">\n                          {reportUtilizzoBobine.content.bobine?.length > 0 ?\n                            (reportUtilizzoBobine.content.bobine.reduce((acc: number, b: any) =>\n                              acc + (b.percentuale_utilizzo || 0), 0) / reportUtilizzoBobine.content.bobine.length\n                            ).toFixed(1) : 0}%\n                        </div>\n                        <p className=\"text-xs text-slate-500\">utilizzo medio</p>\n                      </CardContent>\n                    </Card>\n                  </div>\n\n                  {/* Lista Bobine */}\n                  <Card>\n                    <CardHeader>\n                      <CardTitle>Dettaglio Bobine</CardTitle>\n                      <CardDescription>Stato dettagliato di tutte le bobine</CardDescription>\n                    </CardHeader>\n                    <CardContent>\n                      <div className=\"overflow-x-auto\">\n                        <table className=\"w-full text-sm\">\n                          <thead>\n                            <tr className=\"border-b\">\n                              <th className=\"text-left p-2\">Codice</th>\n                              <th className=\"text-left p-2\">Tipologia</th>\n                              <th className=\"text-right p-2\">Metri Totali</th>\n                              <th className=\"text-right p-2\">Metri Utilizzati</th>\n                              <th className=\"text-right p-2\">Metri Residui</th>\n                              <th className=\"text-right p-2\">Utilizzo %</th>\n                              <th className=\"text-left p-2\">Stato</th>\n                              <th className=\"text-right p-2\">Cavi</th>\n                            </tr>\n                          </thead>\n                          <tbody>\n                            {reportUtilizzoBobine.content.bobine?.map((bobina: any, index: number) => (\n                              <tr key={index} className=\"border-b hover:bg-slate-50\">\n                                <td className=\"p-2 font-medium\">{bobina.codice}</td>\n                                <td className=\"p-2\">{bobina.tipologia}</td>\n                                <td className=\"p-2 text-right\">{bobina.metri_totali?.toLocaleString()}</td>\n                                <td className=\"p-2 text-right\">{bobina.metri_utilizzati?.toLocaleString()}</td>\n                                <td className=\"p-2 text-right\">{bobina.metri_residui?.toLocaleString()}</td>\n                                <td className=\"p-2 text-right\">\n                                  <div className=\"flex items-center gap-2\">\n                                    <span>{bobina.percentuale_utilizzo?.toFixed(1)}%</span>\n                                    <Progress\n                                      value={Math.min(bobina.percentuale_utilizzo || 0, 100)}\n                                      className=\"w-16 h-2\"\n                                    />\n                                  </div>\n                                </td>\n                                <td className=\"p-2\">\n                                  <Badge\n                                    variant={\n                                      bobina.stato === 'Disponibile' ? 'default' :\n                                      bobina.stato === 'In uso' ? 'secondary' :\n                                      bobina.stato === 'Terminata' ? 'outline' :\n                                      bobina.stato === 'Over' ? 'destructive' : 'outline'\n                                    }\n                                  >\n                                    {bobina.stato}\n                                  </Badge>\n                                </td>\n                                <td className=\"p-2 text-right\">{bobina.totale_cavi_associati || 0}</td>\n                              </tr>\n                            ))}\n                          </tbody>\n                        </table>\n                      </div>\n                    </CardContent>\n                  </Card>\n                </>\n              ) : (\n                <div className=\"text-center py-12 text-slate-500\">\n                  <Package className=\"h-12 w-12 mx-auto mb-4 text-slate-300\" />\n                  <p>Nessun dato bobine disponibile</p>\n                </div>\n              )}\n            </TabsContent>\n\n            {/* Tab Content: Produttività */}\n            <TabsContent value=\"produttivita\" className=\"space-y-6\">\n              <div className=\"text-center py-12 text-slate-500\">\n                <Zap className=\"h-12 w-12 mx-auto mb-4 text-slate-300\" />\n                <h3 className=\"text-lg font-semibold mb-2\">Produttività</h3>\n                <p>Funzionalità in fase di sviluppo</p>\n                <p className=\"text-sm mt-2\">\n                  Includerà calcoli IAP, statistiche team e analisi performance\n                </p>\n              </div>\n            </TabsContent>\n\n          </Tabs>\n        )}\n\n      </div>\n    </div>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AAEA;AACA;AACA;AACA;AACA;AAEA;AACA;AACA;AAEA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAkBA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;AAhCA;;;;;;;;;;;;AAiDe,SAAS;;IACtB,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC3C,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACrD,MAAM,CAAC,mBAAmB,qBAAqB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAO;IAChE,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAO;IAChD,MAAM,CAAC,sBAAsB,wBAAwB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAO;IACtE,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAO;IAC1D,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC3C,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAEnC,MAAM,EAAE,IAAI,EAAE,WAAW,WAAW,EAAE,GAAG,CAAA,GAAA,kIAAA,CAAA,UAAO,AAAD;IAC/C,MAAM,EAAE,UAAU,EAAE,QAAQ,EAAE,eAAe,EAAE,WAAW,eAAe,EAAE,OAAO,aAAa,EAAE,GAAG,CAAA,GAAA,8HAAA,CAAA,cAAW,AAAD;IAE9G,qFAAqF;IACrF,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;iCAAE;YAER,MAAM;wDAAiB;oBACrB,aAAa;oBACb,IAAI;wBACF,sDAAsD;wBACtD,MAAM,oBAAoB;wBAE1B,iBAAiB;wBACjB,MAAM,QAAQ,aAAa,OAAO,CAAC;wBACnC,QAAQ,GAAG,CAAC,qDAAqD;wBAEjE,IAAI,CAAC,qBAAqB,qBAAqB,GAAG;4BAChD,QAAQ,IAAI,CAAC;4BACb,SAAS,iBAAiB;4BAC1B,aAAa;4BACb;wBACF;wBAEA,wFAAwF;wBACxF,6EAA6E;wBAE7E,gEAAgE;wBAChE,MAAM;qFAAmB,CAAC,KAAa,SAAc,UAAU,KAAK;gCAClE,OAAO,QAAQ,IAAI,CAAC;oCAClB,MAAM,KAAK;oCACX,IAAI;iGAAQ,CAAC,GAAG,SACd;yGAAW,IAAM,OAAO,IAAI,MAAM,CAAC,aAAa,EAAE,UAAQ,KAAK,MAAM,EAAE,KAAK;wGAAI;;iCAEnF;4BACH;;wBAEA,MAAM,kBAAkB,iBAAiB,CAAC,kCAAkC,EAAE,kBAAkB,uBAAuB,CAAC,EAAE;4BACxH,SAAS;gCACP,iBAAiB,CAAC,OAAO,EAAE,aAAa,OAAO,CAAC,UAAU;gCAC1D,gBAAgB;4BAClB;wBACF,GACG,IAAI;oFAAC,CAAA;gCACJ,OAAO,IAAI,IAAI;4BACjB;mFACC,IAAI;oFAAC,CAAA;gCACJ,OAAO;4BACT;mFACC,KAAK;oFAAC,CAAA;gCACL,OAAO;oCAAE,SAAS;oCAAM,OAAO,IAAI,OAAO,CAAC,QAAQ,CAAC,aAAa,yBAAyB;gCAAsB;4BAClH;;wBAEF,MAAM,aAAa,iBAAiB,CAAC,kCAAkC,EAAE,kBAAkB,kBAAkB,CAAC,EAAE;4BAC9G,SAAS;gCACP,iBAAiB,CAAC,OAAO,EAAE,aAAa,OAAO,CAAC,UAAU;gCAC1D,gBAAgB;4BAClB;wBACF,GACG,IAAI;+EAAC,CAAA;gCACJ,OAAO,IAAI,IAAI;4BACjB;8EACC,IAAI;+EAAC,CAAA;gCACJ,OAAO;4BACT;8EACC,KAAK;+EAAC,CAAA;gCACL,OAAO;oCAAE,SAAS;oCAAM,OAAO,IAAI,OAAO,CAAC,QAAQ,CAAC,aAAa,oBAAoB;gCAAiB;4BACxG;;wBAEF,MAAM,kBAAkB,iBAAiB,CAAC,kCAAkC,EAAE,kBAAkB,6BAA6B,CAAC,EAAE;4BAC9H,SAAS;gCACP,iBAAiB,CAAC,OAAO,EAAE,aAAa,OAAO,CAAC,UAAU;gCAC1D,gBAAgB;4BAClB;wBACF,GACG,IAAI;oFAAC,CAAA;gCACJ,OAAO,IAAI,IAAI;4BACjB;mFACC,IAAI;oFAAC,CAAA;gCACJ,OAAO;4BACT;mFACC,KAAK;oFAAC,CAAA;gCACL,OAAO;oCAAE,SAAS;oCAAM,OAAO,IAAI,OAAO,CAAC,QAAQ,CAAC,aAAa,gCAAgC;gCAA6B;4BAChI;;wBAEF,iFAAiF;wBACjF,MAAM,CAAC,cAAc,SAAS,aAAa,GAAG,MAAM,QAAQ,GAAG,CAAC;4BAC9D;4BACA;4BACA;yBACD;wBAED,2CAA2C;wBAE3C,+EAA+E;wBAC/E,kBAAkB;wBAClB,aAAa;wBACb,wBAAwB;wBAExB,wCAAwC;wBACxC,MAAM,mBAAmB;4BAAC;4BAAc;4BAAS;yBAAa,CAAC,IAAI;qFAAC,CAAA,OAClE,MAAM,OAAO,SAAS;;wBAGxB,uEAAuE;wBACvE,IAAI,cAAc,WAAW,SAAS,WAAW,cAAc,WAC3D,gBAAgB,WAAW,cAAc;4BAC3C,IAAI,kBAAkB;gCACpB,SAAS;4BACX,OAAO;gCACL,SAAS;4BACX;wBACF,OAAO;4BACL,SAAS;wBACX;wBAEA,gDAAgD;wBAChD,aAAa;oBACf,EAAE,OAAO,KAAK;wBACZ,mFAAmF;wBACnF,SAAS;oBACX,SAAU;wBACR,aAAa;oBACf;gBACF;;YAEA,4DAA4D;YAC5D,IAAI,eAAe,iBAAiB;gBAClC,QAAQ,GAAG,CAAC,qDAAqD;oBAAE;oBAAa;gBAAgB;gBAChG;YACF;YAEA,4CAA4C;YAC5C,IAAI,mBAAmB,cAAc,aAAa,GAAG;gBACnD,QAAQ,GAAG,CAAC;gBACZ;YACF,OAAO;gBACL,QAAQ,IAAI,CAAC;gBACb,aAAa;gBACb,SAAS,iBAAiB;YAC5B;QACF;gCAAG;QAAC;QAAY;QAAiB;QAAa;QAAiB;KAAc;IAE7E,MAAM,gBAAgB;QACpB,6CAA6C;QAC7C,IAAI,mBAAmB,YAAY;YACjC,QAAQ,GAAG,CAAC,iDAAiD;YAC7D,aAAa;YACb,SAAS;YACT,kBAAkB;YAClB,aAAa;YACb,wBAAwB;YACxB,qBAAqB;YACrB,kDAAkD;YAClD;QACF,OAAO;YACL,QAAQ,IAAI,CAAC;QACf;IACF;IAEA,MAAM,qBAAqB,OAAO,YAAoB,SAAiB,KAAK;QAC1E,IAAI;YACF,MAAM,oBAAoB,UAAU;YACpC,IAAI,CAAC,mBAAmB;gBACtB;YACF;YAEA,IAAI;YACJ,OAAQ;gBACN,KAAK;oBACH,WAAW,MAAM,oHAAA,CAAA,aAAU,CAAC,iBAAiB,CAAC;oBAC9C;gBACF,KAAK;oBACH,WAAW,MAAM,oHAAA,CAAA,aAAU,CAAC,YAAY,CAAC;oBACzC;gBACF,KAAK;oBACH,WAAW,MAAM,oHAAA,CAAA,aAAU,CAAC,uBAAuB,CAAC;oBACpD;gBACF;oBACE;YACJ;YAEA,IAAI,SAAS,IAAI,CAAC,QAAQ,EAAE;gBAC1B,qCAAqC;gBACrC,OAAO,IAAI,CAAC,SAAS,IAAI,CAAC,QAAQ,EAAE;YACtC;QACF,EAAE,OAAO,OAAO,CAChB;IACF;IAEA,gDAAgD;IAChD,MAAM,eAAe,CAAC,MAAc,OAAe,OAAe;QAChE,MAAM,KAAK,IAAK,iBAAiB;;QACjC,MAAM,KAAK,IAAK,yBAAyB;;QACzC,MAAM,KAAK,IAAK,2BAA2B;;QAE3C,IAAI,SAAS,GAAG,OAAO;QAEvB,MAAM,uBAAuB,CAAC,QAAQ,KAAK,IAAI;QAC/C,MAAM,sBAAsB,CAAC,QAAQ,KAAK,IAAI,CAAC,KAAK,EAAE;QACtD,MAAM,oBAAoB,QAAQ,CAAC,KAAK,KAAK,EAAE;QAC/C,MAAM,aAAa,uBAAuB,sBAAsB;QAEhE,MAAM,eAAe,OAAO,CAAC,KAAK,KAAK,EAAE;QACzC,OAAO,KAAK,KAAK,CAAC,AAAC,aAAa,eAAgB,SAAS;IAC3D;IAEA,qBACE,6LAAC;QAAI,WAAU;kBACb,cAAA,6LAAC;YAAI,WAAU;;8BAGb,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;;8CACC,6LAAC;oCAAG,WAAU;8CAAoC;;;;;;8CAClD,6LAAC;oCAAE,WAAU;8CACV,UAAU,WAAW,CAAC,UAAU,EAAE,SAAS,QAAQ,EAAE,GAAG;;;;;;;;;;;;sCAG7D,6LAAC;4BAAI,WAAU;sCACb,cAAA,6LAAC,qIAAA,CAAA,SAAM;gCAAC,SAAQ;gCAAU,MAAK;gCAAK,SAAS;;kDAC3C,6LAAC,mNAAA,CAAA,YAAS;wCAAC,WAAU;;;;;;oCAAiB;;;;;;;;;;;;;;;;;;gBAO1C,aAAa,4BACb,6LAAC;oBAAI,WAAU;8BACb,cAAA,6LAAC;wBAAI,WAAU;;0CACb,6LAAC,oNAAA,CAAA,UAAO;gCAAC,WAAU;;;;;;0CACnB,6LAAC;0CAAK;;;;;;;;;;;;;;;;2BAGR,sBACF,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BAAI,WAAU;;8CACb,6LAAC,uNAAA,CAAA,cAAW;oCAAC,WAAU;;;;;;8CACvB,6LAAC;oCAAK,WAAU;8CACb,MAAM,QAAQ,CAAC,kCAAkC,MAAM,QAAQ,CAAC,8BAA8B,6BAC9F,MAAM,QAAQ,CAAC,cAAc,MAAM,QAAQ,CAAC,aAAa,gBAAgB;;;;;;;;;;;;sCAG9E,6LAAC;4BAAE,WAAU;sCAAuB;;;;;;wBACnC,MAAM,QAAQ,CAAC,cAAc,MAAM,QAAQ,CAAC,2BAC3C,6LAAC;4BAAI,WAAU;sCACb,cAAA,6LAAC;gCAAE,WAAU;;oCAAwB;kDAChC,6LAAC;kDAAO;;;;;;oCAAsB;;;;;;;;;;;mCAInC;sCACJ,6LAAC;4BAAI,WAAU;;8CACb,6LAAC,qIAAA,CAAA,SAAM;oCACL,SAAS,IAAM,OAAO,QAAQ,CAAC,IAAI,GAAG;oCACtC,WAAU;8CACX;;;;;;8CAGD,6LAAC,qIAAA,CAAA,SAAM;oCACL,SAAQ;oCACR,SAAS;oCACT,WAAU;;sDAEV,6LAAC,mNAAA,CAAA,YAAS;4CAAC,WAAU;;;;;;wCAAiB;;;;;;;;;;;;;;;;;;yCAM5C,6LAAC,mIAAA,CAAA,OAAI;oBAAC,OAAO;oBAAW,eAAe;oBAAc,WAAU;;sCAC7D,6LAAC,mIAAA,CAAA,WAAQ;4BAAC,WAAU;;8CAClB,6LAAC,mIAAA,CAAA,cAAW;oCAAC,OAAM;oCAAc,WAAU;;sDACzC,6LAAC,yMAAA,CAAA,SAAM;4CAAC,WAAU;;;;;;wCAAY;;;;;;;8CAGhC,6LAAC,mIAAA,CAAA,cAAW;oCAAC,OAAM;oCAAM,WAAU;;sDACjC,6LAAC,iNAAA,CAAA,WAAQ;4CAAC,WAAU;;;;;;wCAAY;;;;;;;8CAGlC,6LAAC,mIAAA,CAAA,cAAW;oCAAC,OAAM;oCAAS,WAAU;;sDACpC,6LAAC,2MAAA,CAAA,UAAO;4CAAC,WAAU;;;;;;wCAAY;;;;;;;8CAGjC,6LAAC,mIAAA,CAAA,cAAW;oCAAC,OAAM;oCAAe,WAAU;;sDAC1C,6LAAC,mMAAA,CAAA,MAAG;4CAAC,WAAU;;;;;;wCAAY;;;;;;;;;;;;;sCAM/B,6LAAC,mIAAA,CAAA,cAAW;4BAAC,OAAM;4BAAc,WAAU;sCACxC,gBAAgB,wBACf;;kDAEE,6LAAC;wCAAI,WAAU;;0DACb,6LAAC,mIAAA,CAAA,OAAI;gDAAC,WAAU;;kEACd,6LAAC,mIAAA,CAAA,aAAU;wDAAC,WAAU;;0EACpB,6LAAC,mIAAA,CAAA,YAAS;gEAAC,WAAU;0EAAqC;;;;;;0EAC1D,6LAAC,yMAAA,CAAA,SAAM;gEAAC,WAAU;;;;;;;;;;;;kEAEpB,6LAAC,mIAAA,CAAA,cAAW;;0EACV,6LAAC;gEAAI,WAAU;;oEACZ,eAAe,OAAO,CAAC,YAAY,EAAE,oBAAoB;oEAAE;;;;;;;0EAE9D,6LAAC;gEAAE,WAAU;;oEACV,eAAe,OAAO,CAAC,WAAW,IAAI;oEAAE;;;;;;;;;;;;;;;;;;;0DAK/C,6LAAC,mIAAA,CAAA,OAAI;gDAAC,WAAU;;kEACd,6LAAC,mIAAA,CAAA,aAAU;wDAAC,WAAU;;0EACpB,6LAAC,mIAAA,CAAA,YAAS;gEAAC,WAAU;0EAAqC;;;;;;0EAC1D,6LAAC,8NAAA,CAAA,cAAW;gEAAC,WAAU;;;;;;;;;;;;kEAEzB,6LAAC,mIAAA,CAAA,cAAW;;0EACV,6LAAC;gEAAI,WAAU;;oEACZ,eAAe,OAAO,CAAC,YAAY,EAAE,oBAAoB;oEAAE;;;;;;;0EAE9D,6LAAC,uIAAA,CAAA,WAAQ;gEACP,OAAO,eAAe,OAAO,CAAC,uBAAuB,IAAI;gEACzD,WAAU;;;;;;0EAEZ,6LAAC;gEAAE,WAAU;;oEACV,eAAe,OAAO,CAAC,uBAAuB,EAAE,QAAQ,MAAM;oEAAE;;;;;;;;;;;;;;;;;;;0DAKvE,6LAAC,mIAAA,CAAA,OAAI;gDAAC,WAAU;;kEACd,6LAAC,mIAAA,CAAA,aAAU;wDAAC,WAAU;;0EACpB,6LAAC,mIAAA,CAAA,YAAS;gEAAC,WAAU;0EAAqC;;;;;;0EAC1D,6LAAC,qNAAA,CAAA,aAAU;gEAAC,WAAU;;;;;;;;;;;;kEAExB,6LAAC,mIAAA,CAAA,cAAW;;0EACV,6LAAC;gEAAI,WAAU;;oEACZ,eAAe,OAAO,CAAC,iBAAiB,EAAE,QAAQ,MAAM;oEAAE;;;;;;;0EAE7D,6LAAC;gEAAE,WAAU;0EAAyB;;;;;;0EACtC,6LAAC;gEAAI,WAAU;;kFACb,6LAAC,6MAAA,CAAA,WAAQ;wEAAC,WAAU;;;;;;kFACpB,6LAAC;wEAAK,WAAU;;4EACb,eAAe,OAAO,CAAC,2BAA2B,IAAI;4EAAE;;;;;;;;;;;;;;;;;;;;;;;;;0DAMjE,6LAAC,mIAAA,CAAA,OAAI;gDAAC,WAAU;;kEACd,6LAAC,mIAAA,CAAA,aAAU;wDAAC,WAAU;;0EACpB,6LAAC,mIAAA,CAAA,YAAS;gEAAC,WAAU;0EAAqC;;;;;;0EAC1D,6LAAC,uMAAA,CAAA,QAAK;gEAAC,WAAU;;;;;;;;;;;;kEAEnB,6LAAC,mIAAA,CAAA,cAAW;;0EACV,6LAAC;gEAAI,WAAU;0EACZ,eAAe,OAAO,CAAC,kBAAkB,IAAI;;;;;;0EAEhD,6LAAC;gEAAE,WAAU;0EAAyB;;;;;;0EACtC,6LAAC;gEAAI,WAAU;;kFACb,6LAAC,6MAAA,CAAA,WAAQ;wEAAC,WAAU;;;;;;kFACpB,6LAAC;wEAAK,WAAU;;4EACb,eAAe,OAAO,CAAC,cAAc,IAAI;4EAAE;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;kDAQtD,6LAAC;wCAAI,WAAU;;0DAEb,6LAAC,mIAAA,CAAA,OAAI;;kEACH,6LAAC,mIAAA,CAAA,aAAU;wDAAC,WAAU;;0EACpB,6LAAC;;kFACC,6LAAC,mIAAA,CAAA,YAAS;kFAAC;;;;;;kFACX,6LAAC,mIAAA,CAAA,kBAAe;kFAAC;;;;;;;;;;;;0EAEnB,6LAAC,qIAAA,CAAA,SAAM;gEACL,SAAQ;gEACR,MAAK;gEACL,SAAS,IAAM,mBAAmB,YAAY;;kFAE9C,6LAAC,6MAAA,CAAA,WAAQ;wEAAC,WAAU;;;;;;oEAAiB;;;;;;;;;;;;;kEAIzC,6LAAC,mIAAA,CAAA,cAAW;kEACV,cAAA,6LAAC,sKAAA,CAAA,sBAAmB;4DAAC,OAAM;4DAAO,QAAQ;sEACxC,cAAA,6LAAC,uJAAA,CAAA,WAAQ;gEAAC,MAAM,eAAe,OAAO,CAAC,YAAY,IAAI,EAAE;;kFACvD,6LAAC,gKAAA,CAAA,gBAAa;wEAAC,iBAAgB;;;;;;kFAC/B,6LAAC,wJAAA,CAAA,QAAK;wEAAC,SAAQ;;;;;;kFACf,6LAAC,wJAAA,CAAA,QAAK;;;;;kFACN,6LAAC,0JAAA,CAAA,UAAO;;;;;kFACR,6LAAC,sJAAA,CAAA,MAAG;wEAAC,SAAQ;wEAAQ,MAAK;wEAAU,MAAK;;;;;;;;;;;;;;;;;;;;;;;;;;;;0DAOjD,6LAAC,mIAAA,CAAA,OAAI;;kEACH,6LAAC,mIAAA,CAAA,aAAU;;0EACT,6LAAC,mIAAA,CAAA,YAAS;0EAAC;;;;;;0EACX,6LAAC,mIAAA,CAAA,kBAAe;0EAAC;;;;;;;;;;;;kEAEnB,6LAAC,mIAAA,CAAA,cAAW;kEACV,cAAA,6LAAC;4DAAI,WAAU;;8EACb,6LAAC;oEAAI,WAAU;;sFACb,6LAAC;4EAAK,WAAU;sFAAsB;;;;;;sFACtC,6LAAC,oIAAA,CAAA,QAAK;4EAAC,SAAQ;sFACZ,eAAe,OAAO,CAAC,WAAW,IAAI;;;;;;;;;;;;8EAG3C,6LAAC;oEAAI,WAAU;;sFACb,6LAAC;4EAAK,WAAU;sFAAsB;;;;;;sFACtC,6LAAC,oIAAA,CAAA,QAAK;4EAAC,SAAQ;sFACZ,eAAe,OAAO,CAAC,cAAc,IAAI;;;;;;;;;;;;8EAG9C,6LAAC;oEAAI,WAAU;;sFACb,6LAAC;4EAAK,WAAU;sFAAsB;;;;;;sFACtC,6LAAC,oIAAA,CAAA,QAAK;4EAAC,SAAQ;;gFACZ,eAAe,OAAO,CAAC,gBAAgB,EAAE,QAAQ,MAAM;gFAAE;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;6DASxE,6LAAC;gCAAI,WAAU;;kDACb,6LAAC,yMAAA,CAAA,SAAM;wCAAC,WAAU;;;;;;kDAClB,6LAAC;kDAAE;;;;;;;;;;;;;;;;;sCAMT,6LAAC,mIAAA,CAAA,cAAW;4BAAC,OAAM;4BAAM,WAAU;sCAChC,WAAW,sBACV,6LAAC;gCAAI,WAAU;;kDACb,6LAAC,uNAAA,CAAA,cAAW;wCAAC,WAAU;;;;;;kDACvB,6LAAC;wCAAG,WAAU;kDAA4C;;;;;;kDAC1D,6LAAC;wCAAE,WAAU;kDAAsB;;;;;;kDACnC,6LAAC;wCAAE,WAAU;kDAAyB;;;;;;;;;;;uCAEtC,WAAW,wBACb;;kDACE,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;;kEACC,6LAAC;wDAAG,WAAU;kEAAwB;;;;;;kEACtC,6LAAC;wDAAE,WAAU;kEAAyB;;;;;;;;;;;;0DAExC,6LAAC,qIAAA,CAAA,SAAM;gDACL,SAAQ;gDACR,MAAK;gDACL,SAAS,IAAM,mBAAmB,OAAO;;kEAEzC,6LAAC,6MAAA,CAAA,WAAQ;wDAAC,WAAU;;;;;;oDAAiB;;;;;;;;;;;;;oCAMxC,UAAU,OAAO,CAAC,YAAY,IAAI,UAAU,OAAO,CAAC,YAAY,CAAC,mBAAmB,GAAG,mBACtF,6LAAC,mIAAA,CAAA,OAAI;wCAAC,WAAU;;0DACd,6LAAC,mIAAA,CAAA,aAAU;gDAAC,WAAU;0DACpB,cAAA,6LAAC,mIAAA,CAAA,YAAS;oDAAC,WAAU;;sEACnB,6LAAC,2NAAA,CAAA,gBAAa;4DAAC,WAAU;;;;;;wDAAiB;;;;;;;;;;;;0DAI9C,6LAAC,mIAAA,CAAA,cAAW;;kEACV,6LAAC;wDAAE,WAAU;;0EACX,6LAAC;;oEAAQ,UAAU,OAAO,CAAC,YAAY,CAAC,mBAAmB;oEAAC;;;;;;;4DAAU;4DACpE,UAAU,OAAO,CAAC,YAAY,CAAC,eAAe;4DAAC;;;;;;;kEAEnD,6LAAC;wDAAI,WAAU;kEACZ,MAAM,OAAO,CAAC,UAAU,OAAO,CAAC,YAAY,CAAC,uBAAuB,IACnE,UAAU,OAAO,CAAC,YAAY,CAAC,uBAAuB,CAAC,GAAG,CAAC,CAAC,WAAgB,sBAC1E,6LAAC;;oEAAgB;kFACb,6LAAC;;4EAAQ,UAAU,SAAS;4EAAC;4EAAE,UAAU,UAAU;;;;;;;oEAAU;oEAAG,UAAU,YAAY;oEAAC;oEAAI,UAAU,QAAQ;oEAAC;;+DADxG;;;;sFAIV,6LAAC;sEAAI;;;;;;;;;;;kEAIX,6LAAC;wDAAI,WAAU;kEACb,cAAA,6LAAC;4DAAE,WAAU;;gEAAyB;8EACjC,6LAAC;8EAAO;;;;;;gEAAc;;;;;;;;;;;;;;;;;;;;;;;;oCASlC,UAAU,OAAO,CAAC,SAAS,kBAC1B,6LAAC;wCAAI,WAAU;;0DACb,6LAAC,mIAAA,CAAA,OAAI;gDAAC,WAAU;;kEACd,6LAAC,mIAAA,CAAA,aAAU;wDAAC,WAAU;kEACpB,cAAA,6LAAC,mIAAA,CAAA,YAAS;4DAAC,WAAU;sEAAqC;;;;;;;;;;;kEAE5D,6LAAC,mIAAA,CAAA,cAAW;;0EACV,6LAAC;gEAAI,WAAU;;oEACZ,UAAU,OAAO,CAAC,SAAS,CAAC,qBAAqB,EAAE,oBAAoB;oEAAE;;;;;;;0EAE5E,6LAAC;gEAAE,WAAU;0EAA8B;;;;;;;;;;;;;;;;;;0DAI/C,6LAAC,mIAAA,CAAA,OAAI;gDAAC,WAAU;;kEACd,6LAAC,mIAAA,CAAA,aAAU;wDAAC,WAAU;kEACpB,cAAA,6LAAC,mIAAA,CAAA,YAAS;4DAAC,WAAU;sEAAqC;;;;;;;;;;;kEAE5D,6LAAC,mIAAA,CAAA,cAAW;;0EACV,6LAAC;gEAAI,WAAU;;oEACZ,UAAU,OAAO,CAAC,SAAS,CAAC,oBAAoB,EAAE,oBAAoB;oEAAE;;;;;;;0EAE3E,6LAAC;gEAAE,WAAU;0EAA8B;;;;;;;;;;;;;;;;;;0DAI/C,6LAAC,mIAAA,CAAA,OAAI;gDAAC,WAAU;;kEACd,6LAAC,mIAAA,CAAA,aAAU;wDAAC,WAAU;kEACpB,cAAA,6LAAC,mIAAA,CAAA,YAAS;4DAAC,WAAU;sEAAqC;;;;;;;;;;;kEAE5D,6LAAC,mIAAA,CAAA,cAAW;;0EACV,6LAAC;gEAAI,WAAU;;oEACZ,UAAU,OAAO,CAAC,SAAS,CAAC,yBAAyB,EAAE,QAAQ,MAAM;oEAAE;;;;;;;0EAE1E,6LAAC;gEAAE,WAAU;0EAA8B;;;;;;;;;;;;;;;;;;0DAI/C,6LAAC,mIAAA,CAAA,OAAI;gDAAC,WAAU;;kEACd,6LAAC,mIAAA,CAAA,aAAU;wDAAC,WAAU;kEACpB,cAAA,6LAAC,mIAAA,CAAA,YAAS;4DAAC,WAAU;sEAAqC;;;;;;;;;;;kEAE5D,6LAAC,mIAAA,CAAA,cAAW;;0EACV,6LAAC;gEAAI,WAAU;0EACZ,UAAU,OAAO,CAAC,SAAS,CAAC,8BAA8B,IAAI;;;;;;0EAEjE,6LAAC;gEAAE,WAAU;0EAA8B;;;;;;;;;;;;;;;;;;;;;;;;kDAOnD,6LAAC,mIAAA,CAAA,OAAI;;0DACH,6LAAC,mIAAA,CAAA,aAAU;;kEACT,6LAAC,mIAAA,CAAA,YAAS;kEAAC;;;;;;kEACX,6LAAC,mIAAA,CAAA,kBAAe;kEAAC;;;;;;;;;;;;0DAEnB,6LAAC,mIAAA,CAAA,cAAW;0DACV,cAAA,6LAAC;oDAAI,WAAU;8DACb,cAAA,6LAAC;wDAAM,WAAU;;0EACf,6LAAC;0EACC,cAAA,6LAAC;oEAAG,WAAU;;sFACZ,6LAAC;4EAAG,WAAU;sFAAgB;;;;;;sFAC9B,6LAAC;4EAAG,WAAU;sFAAgB;;;;;;sFAC9B,6LAAC;4EAAG,WAAU;sFAAiB;;;;;;sFAC/B,6LAAC;4EAAG,WAAU;sFAAiB;;;;;;sFAC/B,6LAAC;4EAAG,WAAU;sFAAiB;;;;;;sFAC/B,6LAAC;4EAAG,WAAU;sFAAiB;;;;;;sFAC/B,6LAAC;4EAAG,WAAU;sFAAiB;;;;;;sFAC/B,6LAAC;4EAAG,WAAU;sFAAiB;;;;;;sFAC/B,6LAAC;4EAAG,WAAU;sFAAiB;;;;;;sFAC/B,6LAAC;4EAAG,WAAU;sFAAkB;;;;;;;;;;;;;;;;;0EAGpC,6LAAC;0EACE,UAAU,OAAO,CAAC,kBAAkB,EAAE,IAAI,CAAC,MAAW,sBACrD,6LAAC;wEAAe,WAAW,CAAC,2BAA2B,EAAE,KAAK,eAAe,GAAG,cAAc,IAAI;;0FAChG,6LAAC;gFAAG,WAAU;0FAAmB,KAAK,SAAS;;;;;;0FAC/C,6LAAC;gFAAG,WAAU;0FAAO,KAAK,UAAU;;;;;;0FACpC,6LAAC;gFAAG,WAAU;0FAAkB,KAAK,QAAQ;;;;;;0FAC7C,6LAAC;gFAAG,WAAU;0FAAkB,KAAK,oBAAoB,EAAE;;;;;;0FAC3D,6LAAC;gFAAG,WAAU;0FAAkB,KAAK,kBAAkB,EAAE;;;;;;0FACzD,6LAAC;gFAAG,WAAU;0FAAkB,KAAK,eAAe,EAAE;;;;;;0FACtD,6LAAC;gFAAG,WAAU;0FAAkB,KAAK,UAAU;;;;;;0FAC/C,6LAAC;gFAAG,WAAU;0FAAkB,KAAK,aAAa,EAAE;;;;;;0FACpD,6LAAC;gFAAG,WAAU;0FACX,KAAK,cAAc,GAAG,kBACrB,6LAAC;oFAAK,WAAU;;wFAAgB,KAAK,cAAc,EAAE;wFAAiB;;;;;;yGAEtE,6LAAC;oFAAK,WAAU;8FAAiB;;;;;;;;;;;0FAGrC,6LAAC;gFAAG,WAAU;0FACX,KAAK,kBAAkB,iBACtB,6LAAC,oIAAA,CAAA,QAAK;oFAAC,SAAQ;8FAAc;;;;;yGAE7B,6LAAC,oIAAA,CAAA,QAAK;oFAAC,SAAQ;8FAAY;;;;;;;;;;;;uEApBxB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;kDAgCrB,6LAAC,mIAAA,CAAA,OAAI;;0DACH,6LAAC,mIAAA,CAAA,aAAU;;kEACT,6LAAC,mIAAA,CAAA,YAAS;kEAAC;;;;;;kEACX,6LAAC,mIAAA,CAAA,kBAAe;kEAAC;;;;;;;;;;;;0DAEnB,6LAAC,mIAAA,CAAA,cAAW;0DACV,cAAA,6LAAC;oDAAI,WAAU;8DACb,cAAA,6LAAC;wDAAM,WAAU;;0EACf,6LAAC;0EACC,cAAA,6LAAC;oEAAG,WAAU;;sFACZ,6LAAC;4EAAG,WAAU;sFAAgB;;;;;;sFAC9B,6LAAC;4EAAG,WAAU;sFAAgB;;;;;;sFAC9B,6LAAC;4EAAG,WAAU;sFAAiB;;;;;;sFAC/B,6LAAC;4EAAG,WAAU;sFAAiB;;;;;;;;;;;;;;;;;0EAGnC,6LAAC;0EACE,UAAU,OAAO,CAAC,eAAe,EAAE,IAAI,CAAC,MAAW,sBAClD,6LAAC;wEAAe,WAAU;;0FACxB,6LAAC;gFAAG,WAAU;0FAAmB,KAAK,SAAS;;;;;;0FAC/C,6LAAC;gFAAG,WAAU;0FAAO,KAAK,UAAU;;;;;;0FACpC,6LAAC;gFAAG,WAAU;0FAAkB,KAAK,UAAU;;;;;;0FAC/C,6LAAC;gFAAG,WAAU;0FACZ,cAAA,6LAAC;oFAAK,WAAW,KAAK,iBAAiB,GAAG,IAAI,mBAAmB;8FAC9D,KAAK,iBAAiB,EAAE;;;;;;;;;;;;uEANtB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;6DAkBvB,6LAAC;gCAAI,WAAU;;kDACb,6LAAC,iNAAA,CAAA,WAAQ;wCAAC,WAAU;;;;;;kDACpB,6LAAC;kDAAE;;;;;;;;;;;;;;;;;sCAMT,6LAAC,mIAAA,CAAA,cAAW;4BAAC,OAAM;4BAAS,WAAU;sCACnC,sBAAsB,wBACrB;;kDACE,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;;kEACC,6LAAC;wDAAG,WAAU;kEAAwB;;;;;;kEACtC,6LAAC;wDAAE,WAAU;kEAAyB;;;;;;;;;;;;0DAExC,6LAAC,qIAAA,CAAA,SAAM;gDACL,SAAQ;gDACR,MAAK;gDACL,SAAS,IAAM,mBAAmB,mBAAmB;;kEAErD,6LAAC,6MAAA,CAAA,WAAQ;wDAAC,WAAU;;;;;;oDAAiB;;;;;;;;;;;;;kDAMzC,6LAAC;wCAAI,WAAU;;0DACb,6LAAC,mIAAA,CAAA,OAAI;;kEACH,6LAAC,mIAAA,CAAA,aAAU;wDAAC,WAAU;;0EACpB,6LAAC,mIAAA,CAAA,YAAS;gEAAC,WAAU;0EAAqC;;;;;;0EAC1D,6LAAC,2MAAA,CAAA,UAAO;gEAAC,WAAU;;;;;;;;;;;;kEAErB,6LAAC,mIAAA,CAAA,cAAW;;0EACV,6LAAC;gEAAI,WAAU;0EACZ,qBAAqB,OAAO,CAAC,aAAa,IAAI;;;;;;0EAEjD,6LAAC;gEAAE,WAAU;0EAAyB;;;;;;;;;;;;;;;;;;0DAI1C,6LAAC,mIAAA,CAAA,OAAI;;kEACH,6LAAC,mIAAA,CAAA,aAAU;wDAAC,WAAU;;0EACpB,6LAAC,mIAAA,CAAA,YAAS;gEAAC,WAAU;0EAAqC;;;;;;0EAC1D,6LAAC,6MAAA,CAAA,WAAQ;gEAAC,WAAU;;;;;;;;;;;;kEAEtB,6LAAC,mIAAA,CAAA,cAAW;;0EACV,6LAAC;gEAAI,WAAU;0EACZ,qBAAqB,OAAO,CAAC,MAAM,EAAE,OAAO,CAAC,IAC5C,EAAE,KAAK,KAAK,YAAY,EAAE,KAAK,KAAK,eACpC,UAAU;;;;;;0EAEd,6LAAC;gEAAE,WAAU;0EAAyB;;;;;;;;;;;;;;;;;;0DAI1C,6LAAC,mIAAA,CAAA,OAAI;;kEACH,6LAAC,mIAAA,CAAA,aAAU;wDAAC,WAAU;;0EACpB,6LAAC,mIAAA,CAAA,YAAS;gEAAC,WAAU;0EAAqC;;;;;;0EAC1D,6LAAC,qNAAA,CAAA,aAAU;gEAAC,WAAU;;;;;;;;;;;;kEAExB,6LAAC,mIAAA,CAAA,cAAW;;0EACV,6LAAC;gEAAI,WAAU;;oEACZ,qBAAqB,OAAO,CAAC,MAAM,EAAE,SAAS,IAC7C,CAAC,qBAAqB,OAAO,CAAC,MAAM,CAAC,MAAM,CAAC,CAAC,KAAa,IACxD,MAAM,CAAC,EAAE,oBAAoB,IAAI,CAAC,GAAG,KAAK,qBAAqB,OAAO,CAAC,MAAM,CAAC,MAAM,AACtF,EAAE,OAAO,CAAC,KAAK;oEAAE;;;;;;;0EAErB,6LAAC;gEAAE,WAAU;0EAAyB;;;;;;;;;;;;;;;;;;;;;;;;kDAM5C,6LAAC,mIAAA,CAAA,OAAI;;0DACH,6LAAC,mIAAA,CAAA,aAAU;;kEACT,6LAAC,mIAAA,CAAA,YAAS;kEAAC;;;;;;kEACX,6LAAC,mIAAA,CAAA,kBAAe;kEAAC;;;;;;;;;;;;0DAEnB,6LAAC,mIAAA,CAAA,cAAW;0DACV,cAAA,6LAAC;oDAAI,WAAU;8DACb,cAAA,6LAAC;wDAAM,WAAU;;0EACf,6LAAC;0EACC,cAAA,6LAAC;oEAAG,WAAU;;sFACZ,6LAAC;4EAAG,WAAU;sFAAgB;;;;;;sFAC9B,6LAAC;4EAAG,WAAU;sFAAgB;;;;;;sFAC9B,6LAAC;4EAAG,WAAU;sFAAiB;;;;;;sFAC/B,6LAAC;4EAAG,WAAU;sFAAiB;;;;;;sFAC/B,6LAAC;4EAAG,WAAU;sFAAiB;;;;;;sFAC/B,6LAAC;4EAAG,WAAU;sFAAiB;;;;;;sFAC/B,6LAAC;4EAAG,WAAU;sFAAgB;;;;;;sFAC9B,6LAAC;4EAAG,WAAU;sFAAiB;;;;;;;;;;;;;;;;;0EAGnC,6LAAC;0EACE,qBAAqB,OAAO,CAAC,MAAM,EAAE,IAAI,CAAC,QAAa,sBACtD,6LAAC;wEAAe,WAAU;;0FACxB,6LAAC;gFAAG,WAAU;0FAAmB,OAAO,MAAM;;;;;;0FAC9C,6LAAC;gFAAG,WAAU;0FAAO,OAAO,SAAS;;;;;;0FACrC,6LAAC;gFAAG,WAAU;0FAAkB,OAAO,YAAY,EAAE;;;;;;0FACrD,6LAAC;gFAAG,WAAU;0FAAkB,OAAO,gBAAgB,EAAE;;;;;;0FACzD,6LAAC;gFAAG,WAAU;0FAAkB,OAAO,aAAa,EAAE;;;;;;0FACtD,6LAAC;gFAAG,WAAU;0FACZ,cAAA,6LAAC;oFAAI,WAAU;;sGACb,6LAAC;;gGAAM,OAAO,oBAAoB,EAAE,QAAQ;gGAAG;;;;;;;sGAC/C,6LAAC,uIAAA,CAAA,WAAQ;4FACP,OAAO,KAAK,GAAG,CAAC,OAAO,oBAAoB,IAAI,GAAG;4FAClD,WAAU;;;;;;;;;;;;;;;;;0FAIhB,6LAAC;gFAAG,WAAU;0FACZ,cAAA,6LAAC,oIAAA,CAAA,QAAK;oFACJ,SACE,OAAO,KAAK,KAAK,gBAAgB,YACjC,OAAO,KAAK,KAAK,WAAW,cAC5B,OAAO,KAAK,KAAK,cAAc,YAC/B,OAAO,KAAK,KAAK,SAAS,gBAAgB;8FAG3C,OAAO,KAAK;;;;;;;;;;;0FAGjB,6LAAC;gFAAG,WAAU;0FAAkB,OAAO,qBAAqB,IAAI;;;;;;;uEA3BzD;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;6DAqCvB,6LAAC;gCAAI,WAAU;;kDACb,6LAAC,2MAAA,CAAA,UAAO;wCAAC,WAAU;;;;;;kDACnB,6LAAC;kDAAE;;;;;;;;;;;;;;;;;sCAMT,6LAAC,mIAAA,CAAA,cAAW;4BAAC,OAAM;4BAAe,WAAU;sCAC1C,cAAA,6LAAC;gCAAI,WAAU;;kDACb,6LAAC,mMAAA,CAAA,MAAG;wCAAC,WAAU;;;;;;kDACf,6LAAC;wCAAG,WAAU;kDAA6B;;;;;;kDAC3C,6LAAC;kDAAE;;;;;;kDACH,6LAAC;wCAAE,WAAU;kDAAe;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAY5C;GA9yBwB;;QAUmB,kIAAA,CAAA,UAAO;QACoD,8HAAA,CAAA,cAAW;;;KAXzF", "debugId": null}}]}
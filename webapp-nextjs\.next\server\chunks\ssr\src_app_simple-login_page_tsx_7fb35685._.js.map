{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/CMS/webapp-nextjs/src/app/simple-login/page.tsx"], "sourcesContent": ["'use client'\n\nimport { useState } from 'react'\nimport { useRouter } from 'next/navigation'\n\nexport default function SimpleLoginPage() {\n  const [username, setUsername] = useState('')\n  const [password, setPassword] = useState('')\n  const [result, setResult] = useState('')\n  const [loading, setLoading] = useState(false)\n  const router = useRouter()\n\n  const handleLogin = async (e: React.FormEvent) => {\n    e.preventDefault()\n    setLoading(true)\n    setResult('Logging in...')\n    \n    try {\n      console.log('Starting login with:', { username, password })\n      \n      const formData = new FormData()\n      formData.append('username', username)\n      formData.append('password', password)\n\n      console.log('Sending request to backend...')\n      \n      const response = await fetch('http://localhost:8001/api/auth/login', {\n        method: 'POST',\n        body: formData\n      })\n\n      console.log('Response status:', response.status)\n      const data = await response.json()\n      console.log('Response data:', data)\n\n      if (response.ok) {\n        // Salva il token\n        localStorage.setItem('token', data.access_token)\n        \n        setResult(`SUCCESS: Login riuscito! Token salvato. Reindirizzamento...`)\n        \n        // Reindirizza in base al ruolo\n        setTimeout(() => {\n          if (data.role === 'owner') {\n            console.log('Redirecting to /admin')\n            router.push('/admin')\n          } else if (data.role === 'user') {\n            console.log('Redirecting to /cantieri')\n            router.push('/cantieri')\n          } else {\n            console.log('Redirecting to /')\n            router.push('/')\n          }\n        }, 1000)\n      } else {\n        setResult(`ERROR: ${response.status} - ${data.detail || 'Login failed'}`)\n      }\n    } catch (error) {\n      console.error('Login error:', error)\n      setResult(`EXCEPTION: ${error.message}`)\n    } finally {\n      setLoading(false)\n    }\n  }\n\n  return (\n    <div style={{ \n      minHeight: '100vh', \n      display: 'flex', \n      alignItems: 'center', \n      justifyContent: 'center',\n      backgroundColor: '#f8f9fa',\n      fontFamily: 'Arial, sans-serif'\n    }}>\n      <div style={{\n        backgroundColor: 'white',\n        padding: '40px',\n        borderRadius: '10px',\n        boxShadow: '0 4px 6px rgba(0, 0, 0, 0.1)',\n        width: '100%',\n        maxWidth: '400px'\n      }}>\n        <h1 style={{ textAlign: 'center', marginBottom: '30px', color: '#333' }}>\n          Simple Login Test\n        </h1>\n        \n        <form onSubmit={handleLogin} style={{ marginBottom: '20px' }}>\n          <div style={{ marginBottom: '15px' }}>\n            <label style={{ display: 'block', marginBottom: '5px', fontWeight: 'bold' }}>\n              Username:\n            </label>\n            <input\n              type=\"text\"\n              value={username}\n              onChange={(e) => setUsername(e.target.value)}\n              placeholder=\"admin\"\n              required\n              disabled={loading}\n              style={{\n                width: '100%',\n                padding: '10px',\n                border: '1px solid #ddd',\n                borderRadius: '5px',\n                fontSize: '16px'\n              }}\n            />\n          </div>\n          \n          <div style={{ marginBottom: '20px' }}>\n            <label style={{ display: 'block', marginBottom: '5px', fontWeight: 'bold' }}>\n              Password:\n            </label>\n            <input\n              type=\"password\"\n              value={password}\n              onChange={(e) => setPassword(e.target.value)}\n              placeholder=\"admin\"\n              required\n              disabled={loading}\n              style={{\n                width: '100%',\n                padding: '10px',\n                border: '1px solid #ddd',\n                borderRadius: '5px',\n                fontSize: '16px'\n              }}\n            />\n          </div>\n          \n          <button\n            type=\"submit\"\n            disabled={loading}\n            style={{\n              width: '100%',\n              padding: '12px',\n              backgroundColor: loading ? '#ccc' : '#007bff',\n              color: 'white',\n              border: 'none',\n              borderRadius: '5px',\n              fontSize: '16px',\n              cursor: loading ? 'not-allowed' : 'pointer'\n            }}\n          >\n            {loading ? 'Logging in...' : 'Login'}\n          </button>\n        </form>\n\n        <div style={{ \n          backgroundColor: '#f8f9fa', \n          padding: '15px', \n          borderRadius: '5px',\n          whiteSpace: 'pre-wrap',\n          minHeight: '100px',\n          fontSize: '14px',\n          fontFamily: 'monospace'\n        }}>\n          {result || 'Enter credentials and click Login...'}\n        </div>\n        \n        <div style={{ marginTop: '20px', textAlign: 'center' }}>\n          <small style={{ color: '#666' }}>\n            Test credentials: admin/admin\n          </small>\n        </div>\n      </div>\n    </div>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAHA;;;;AAKe,SAAS;IACtB,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACzC,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACzC,MAAM,CAAC,QAAQ,UAAU,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACrC,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACvC,MAAM,SAAS,CAAA,GAAA,kIAAA,CAAA,YAAS,AAAD;IAEvB,MAAM,cAAc,OAAO;QACzB,EAAE,cAAc;QAChB,WAAW;QACX,UAAU;QAEV,IAAI;YACF,QAAQ,GAAG,CAAC,wBAAwB;gBAAE;gBAAU;YAAS;YAEzD,MAAM,WAAW,IAAI;YACrB,SAAS,MAAM,CAAC,YAAY;YAC5B,SAAS,MAAM,CAAC,YAAY;YAE5B,QAAQ,GAAG,CAAC;YAEZ,MAAM,WAAW,MAAM,MAAM,wCAAwC;gBACnE,QAAQ;gBACR,MAAM;YACR;YAEA,QAAQ,GAAG,CAAC,oBAAoB,SAAS,MAAM;YAC/C,MAAM,OAAO,MAAM,SAAS,IAAI;YAChC,QAAQ,GAAG,CAAC,kBAAkB;YAE9B,IAAI,SAAS,EAAE,EAAE;gBACf,iBAAiB;gBACjB,aAAa,OAAO,CAAC,SAAS,KAAK,YAAY;gBAE/C,UAAU,CAAC,2DAA2D,CAAC;gBAEvE,+BAA+B;gBAC/B,WAAW;oBACT,IAAI,KAAK,IAAI,KAAK,SAAS;wBACzB,QAAQ,GAAG,CAAC;wBACZ,OAAO,IAAI,CAAC;oBACd,OAAO,IAAI,KAAK,IAAI,KAAK,QAAQ;wBAC/B,QAAQ,GAAG,CAAC;wBACZ,OAAO,IAAI,CAAC;oBACd,OAAO;wBACL,QAAQ,GAAG,CAAC;wBACZ,OAAO,IAAI,CAAC;oBACd;gBACF,GAAG;YACL,OAAO;gBACL,UAAU,CAAC,OAAO,EAAE,SAAS,MAAM,CAAC,GAAG,EAAE,KAAK,MAAM,IAAI,gBAAgB;YAC1E;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,gBAAgB;YAC9B,UAAU,CAAC,WAAW,EAAE,MAAM,OAAO,EAAE;QACzC,SAAU;YACR,WAAW;QACb;IACF;IAEA,qBACE,8OAAC;QAAI,OAAO;YACV,WAAW;YACX,SAAS;YACT,YAAY;YACZ,gBAAgB;YAChB,iBAAiB;YACjB,YAAY;QACd;kBACE,cAAA,8OAAC;YAAI,OAAO;gBACV,iBAAiB;gBACjB,SAAS;gBACT,cAAc;gBACd,WAAW;gBACX,OAAO;gBACP,UAAU;YACZ;;8BACE,8OAAC;oBAAG,OAAO;wBAAE,WAAW;wBAAU,cAAc;wBAAQ,OAAO;oBAAO;8BAAG;;;;;;8BAIzE,8OAAC;oBAAK,UAAU;oBAAa,OAAO;wBAAE,cAAc;oBAAO;;sCACzD,8OAAC;4BAAI,OAAO;gCAAE,cAAc;4BAAO;;8CACjC,8OAAC;oCAAM,OAAO;wCAAE,SAAS;wCAAS,cAAc;wCAAO,YAAY;oCAAO;8CAAG;;;;;;8CAG7E,8OAAC;oCACC,MAAK;oCACL,OAAO;oCACP,UAAU,CAAC,IAAM,YAAY,EAAE,MAAM,CAAC,KAAK;oCAC3C,aAAY;oCACZ,QAAQ;oCACR,UAAU;oCACV,OAAO;wCACL,OAAO;wCACP,SAAS;wCACT,QAAQ;wCACR,cAAc;wCACd,UAAU;oCACZ;;;;;;;;;;;;sCAIJ,8OAAC;4BAAI,OAAO;gCAAE,cAAc;4BAAO;;8CACjC,8OAAC;oCAAM,OAAO;wCAAE,SAAS;wCAAS,cAAc;wCAAO,YAAY;oCAAO;8CAAG;;;;;;8CAG7E,8OAAC;oCACC,MAAK;oCACL,OAAO;oCACP,UAAU,CAAC,IAAM,YAAY,EAAE,MAAM,CAAC,KAAK;oCAC3C,aAAY;oCACZ,QAAQ;oCACR,UAAU;oCACV,OAAO;wCACL,OAAO;wCACP,SAAS;wCACT,QAAQ;wCACR,cAAc;wCACd,UAAU;oCACZ;;;;;;;;;;;;sCAIJ,8OAAC;4BACC,MAAK;4BACL,UAAU;4BACV,OAAO;gCACL,OAAO;gCACP,SAAS;gCACT,iBAAiB,UAAU,SAAS;gCACpC,OAAO;gCACP,QAAQ;gCACR,cAAc;gCACd,UAAU;gCACV,QAAQ,UAAU,gBAAgB;4BACpC;sCAEC,UAAU,kBAAkB;;;;;;;;;;;;8BAIjC,8OAAC;oBAAI,OAAO;wBACV,iBAAiB;wBACjB,SAAS;wBACT,cAAc;wBACd,YAAY;wBACZ,WAAW;wBACX,UAAU;wBACV,YAAY;oBACd;8BACG,UAAU;;;;;;8BAGb,8OAAC;oBAAI,OAAO;wBAAE,WAAW;wBAAQ,WAAW;oBAAS;8BACnD,cAAA,8OAAC;wBAAM,OAAO;4BAAE,OAAO;wBAAO;kCAAG;;;;;;;;;;;;;;;;;;;;;;AAO3C", "debugId": null}}]}
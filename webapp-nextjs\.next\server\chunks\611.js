"use strict";exports.id=611,exports.ids=[611],exports.modules={3589:(e,t,n)=>{n.d(t,{A:()=>r});let r=(0,n(62688).A)("chevron-up",[["path",{d:"m18 15-6-6-6 6",key:"153udz"}]])},13964:(e,t,n)=>{n.d(t,{A:()=>r});let r=(0,n(62688).A)("check",[["path",{d:"M20 6 9 17l-5-5",key:"1gmf2c"}]])},18853:(e,t,n)=>{n.d(t,{X:()=>i});var r=n(43210),o=n(66156);function i(e){let[t,n]=r.useState(void 0);return(0,o.N)(()=>{if(e){n({width:e.offsetWidth,height:e.offsetHeight});let t=new ResizeObserver(t=>{let r,o;if(!Array.isArray(t)||!t.length)return;let i=t[0];if("borderBoxSize"in i){let e=i.borderBoxSize,t=Array.isArray(e)?e[0]:e;r=t.inlineSize,o=t.blockSize}else r=e.offsetWidth,o=e.offsetHeight;n({width:r,height:o})});return t.observe(e,{box:"border-box"}),()=>t.unobserve(e)}n(void 0)},[e]),t}},40211:(e,t,n)=>{n.d(t,{C1:()=>C,bL:()=>b});var r=n(43210),o=n(98599),i=n(11273),l=n(70569),a=n(65551),s=n(83721),u=n(18853),c=n(46059),d=n(14163),f=n(60687),p="Checkbox",[h,m]=(0,i.A)(p),[g,v]=h(p);function w(e){let{__scopeCheckbox:t,checked:n,children:o,defaultChecked:i,disabled:l,form:s,name:u,onCheckedChange:c,required:d,value:h="on",internal_do_not_use_render:m}=e,[v,w]=(0,a.i)({prop:n,defaultProp:i??!1,onChange:c,caller:p}),[y,x]=r.useState(null),[b,S]=r.useState(null),C=r.useRef(!1),R=!y||!!s||!!y.closest("form"),k={checked:v,disabled:l,setChecked:w,control:y,setControl:x,name:u,form:s,value:h,hasConsumerStoppedPropagationRef:C,required:d,defaultChecked:!T(i)&&i,isFormControl:R,bubbleInput:b,setBubbleInput:S};return(0,f.jsx)(g,{scope:t,...k,children:"function"==typeof m?m(k):o})}var y="CheckboxTrigger",x=r.forwardRef(({__scopeCheckbox:e,onKeyDown:t,onClick:n,...i},a)=>{let{control:s,value:u,disabled:c,checked:p,required:h,setControl:m,setChecked:g,hasConsumerStoppedPropagationRef:w,isFormControl:x,bubbleInput:b}=v(y,e),S=(0,o.s)(a,m),C=r.useRef(p);return r.useEffect(()=>{let e=s?.form;if(e){let t=()=>g(C.current);return e.addEventListener("reset",t),()=>e.removeEventListener("reset",t)}},[s,g]),(0,f.jsx)(d.sG.button,{type:"button",role:"checkbox","aria-checked":T(p)?"mixed":p,"aria-required":h,"data-state":A(p),"data-disabled":c?"":void 0,disabled:c,value:u,...i,ref:S,onKeyDown:(0,l.m)(t,e=>{"Enter"===e.key&&e.preventDefault()}),onClick:(0,l.m)(n,e=>{g(e=>!!T(e)||!e),b&&x&&(w.current=e.isPropagationStopped(),w.current||e.stopPropagation())})})});x.displayName=y;var b=r.forwardRef((e,t)=>{let{__scopeCheckbox:n,name:r,checked:o,defaultChecked:i,required:l,disabled:a,value:s,onCheckedChange:u,form:c,...d}=e;return(0,f.jsx)(w,{__scopeCheckbox:n,checked:o,defaultChecked:i,disabled:a,required:l,onCheckedChange:u,name:r,form:c,value:s,internal_do_not_use_render:({isFormControl:e})=>(0,f.jsxs)(f.Fragment,{children:[(0,f.jsx)(x,{...d,ref:t,__scopeCheckbox:n}),e&&(0,f.jsx)(k,{__scopeCheckbox:n})]})})});b.displayName=p;var S="CheckboxIndicator",C=r.forwardRef((e,t)=>{let{__scopeCheckbox:n,forceMount:r,...o}=e,i=v(S,n);return(0,f.jsx)(c.C,{present:r||T(i.checked)||!0===i.checked,children:(0,f.jsx)(d.sG.span,{"data-state":A(i.checked),"data-disabled":i.disabled?"":void 0,...o,ref:t,style:{pointerEvents:"none",...e.style}})})});C.displayName=S;var R="CheckboxBubbleInput",k=r.forwardRef(({__scopeCheckbox:e,...t},n)=>{let{control:i,hasConsumerStoppedPropagationRef:l,checked:a,defaultChecked:c,required:p,disabled:h,name:m,value:g,form:w,bubbleInput:y,setBubbleInput:x}=v(R,e),b=(0,o.s)(n,x),S=(0,s.Z)(a),C=(0,u.X)(i);r.useEffect(()=>{if(!y)return;let e=Object.getOwnPropertyDescriptor(window.HTMLInputElement.prototype,"checked").set,t=!l.current;if(S!==a&&e){let n=new Event("click",{bubbles:t});y.indeterminate=T(a),e.call(y,!T(a)&&a),y.dispatchEvent(n)}},[y,S,a,l]);let k=r.useRef(!T(a)&&a);return(0,f.jsx)(d.sG.input,{type:"checkbox","aria-hidden":!0,defaultChecked:c??k.current,required:p,disabled:h,name:m,value:g,form:w,...t,tabIndex:-1,ref:b,style:{...t.style,...C,position:"absolute",pointerEvents:"none",opacity:0,margin:0,transform:"translateX(-100%)"}})});function T(e){return"indeterminate"===e}function A(e){return T(e)?"indeterminate":e?"checked":"unchecked"}k.displayName=R},55509:(e,t,n)=>{n.d(t,{Mz:()=>eY,i3:()=>eJ,UC:()=>eZ,bL:()=>eq,Bk:()=>eD});var r=n(43210);let o=["top","right","bottom","left"],i=Math.min,l=Math.max,a=Math.round,s=Math.floor,u=e=>({x:e,y:e}),c={left:"right",right:"left",bottom:"top",top:"bottom"},d={start:"end",end:"start"};function f(e,t){return"function"==typeof e?e(t):e}function p(e){return e.split("-")[0]}function h(e){return e.split("-")[1]}function m(e){return"x"===e?"y":"x"}function g(e){return"y"===e?"height":"width"}function v(e){return["top","bottom"].includes(p(e))?"y":"x"}function w(e){return e.replace(/start|end/g,e=>d[e])}function y(e){return e.replace(/left|right|bottom|top/g,e=>c[e])}function x(e){return"number"!=typeof e?{top:0,right:0,bottom:0,left:0,...e}:{top:e,right:e,bottom:e,left:e}}function b(e){let{x:t,y:n,width:r,height:o}=e;return{width:r,height:o,top:n,left:t,right:t+r,bottom:n+o,x:t,y:n}}function S(e,t,n){let r,{reference:o,floating:i}=e,l=v(t),a=m(v(t)),s=g(a),u=p(t),c="y"===l,d=o.x+o.width/2-i.width/2,f=o.y+o.height/2-i.height/2,w=o[s]/2-i[s]/2;switch(u){case"top":r={x:d,y:o.y-i.height};break;case"bottom":r={x:d,y:o.y+o.height};break;case"right":r={x:o.x+o.width,y:f};break;case"left":r={x:o.x-i.width,y:f};break;default:r={x:o.x,y:o.y}}switch(h(t)){case"start":r[a]-=w*(n&&c?-1:1);break;case"end":r[a]+=w*(n&&c?-1:1)}return r}let C=async(e,t,n)=>{let{placement:r="bottom",strategy:o="absolute",middleware:i=[],platform:l}=n,a=i.filter(Boolean),s=await (null==l.isRTL?void 0:l.isRTL(t)),u=await l.getElementRects({reference:e,floating:t,strategy:o}),{x:c,y:d}=S(u,r,s),f=r,p={},h=0;for(let n=0;n<a.length;n++){let{name:i,fn:m}=a[n],{x:g,y:v,data:w,reset:y}=await m({x:c,y:d,initialPlacement:r,placement:f,strategy:o,middlewareData:p,rects:u,platform:l,elements:{reference:e,floating:t}});c=null!=g?g:c,d=null!=v?v:d,p={...p,[i]:{...p[i],...w}},y&&h<=50&&(h++,"object"==typeof y&&(y.placement&&(f=y.placement),y.rects&&(u=!0===y.rects?await l.getElementRects({reference:e,floating:t,strategy:o}):y.rects),{x:c,y:d}=S(u,f,s)),n=-1)}return{x:c,y:d,placement:f,strategy:o,middlewareData:p}};async function R(e,t){var n;void 0===t&&(t={});let{x:r,y:o,platform:i,rects:l,elements:a,strategy:s}=e,{boundary:u="clippingAncestors",rootBoundary:c="viewport",elementContext:d="floating",altBoundary:p=!1,padding:h=0}=f(t,e),m=x(h),g=a[p?"floating"===d?"reference":"floating":d],v=b(await i.getClippingRect({element:null==(n=await (null==i.isElement?void 0:i.isElement(g)))||n?g:g.contextElement||await (null==i.getDocumentElement?void 0:i.getDocumentElement(a.floating)),boundary:u,rootBoundary:c,strategy:s})),w="floating"===d?{x:r,y:o,width:l.floating.width,height:l.floating.height}:l.reference,y=await (null==i.getOffsetParent?void 0:i.getOffsetParent(a.floating)),S=await (null==i.isElement?void 0:i.isElement(y))&&await (null==i.getScale?void 0:i.getScale(y))||{x:1,y:1},C=b(i.convertOffsetParentRelativeRectToViewportRelativeRect?await i.convertOffsetParentRelativeRectToViewportRelativeRect({elements:a,rect:w,offsetParent:y,strategy:s}):w);return{top:(v.top-C.top+m.top)/S.y,bottom:(C.bottom-v.bottom+m.bottom)/S.y,left:(v.left-C.left+m.left)/S.x,right:(C.right-v.right+m.right)/S.x}}function k(e,t){return{top:e.top-t.height,right:e.right-t.width,bottom:e.bottom-t.height,left:e.left-t.width}}function T(e){return o.some(t=>e[t]>=0)}async function A(e,t){let{placement:n,platform:r,elements:o}=e,i=await (null==r.isRTL?void 0:r.isRTL(o.floating)),l=p(n),a=h(n),s="y"===v(n),u=["left","top"].includes(l)?-1:1,c=i&&s?-1:1,d=f(t,e),{mainAxis:m,crossAxis:g,alignmentAxis:w}="number"==typeof d?{mainAxis:d,crossAxis:0,alignmentAxis:null}:{mainAxis:d.mainAxis||0,crossAxis:d.crossAxis||0,alignmentAxis:d.alignmentAxis};return a&&"number"==typeof w&&(g="end"===a?-1*w:w),s?{x:g*c,y:m*u}:{x:m*u,y:g*c}}function j(){return"undefined"!=typeof window}function E(e){return N(e)?(e.nodeName||"").toLowerCase():"#document"}function P(e){var t;return(null==e||null==(t=e.ownerDocument)?void 0:t.defaultView)||window}function L(e){var t;return null==(t=(N(e)?e.ownerDocument:e.document)||window.document)?void 0:t.documentElement}function N(e){return!!j()&&(e instanceof Node||e instanceof P(e).Node)}function D(e){return!!j()&&(e instanceof Element||e instanceof P(e).Element)}function M(e){return!!j()&&(e instanceof HTMLElement||e instanceof P(e).HTMLElement)}function I(e){return!!j()&&"undefined"!=typeof ShadowRoot&&(e instanceof ShadowRoot||e instanceof P(e).ShadowRoot)}function O(e){let{overflow:t,overflowX:n,overflowY:r,display:o}=W(e);return/auto|scroll|overlay|hidden|clip/.test(t+r+n)&&!["inline","contents"].includes(o)}function H(e){return[":popover-open",":modal"].some(t=>{try{return e.matches(t)}catch(e){return!1}})}function B(e){let t=F(),n=D(e)?W(e):e;return["transform","translate","scale","rotate","perspective"].some(e=>!!n[e]&&"none"!==n[e])||!!n.containerType&&"normal"!==n.containerType||!t&&!!n.backdropFilter&&"none"!==n.backdropFilter||!t&&!!n.filter&&"none"!==n.filter||["transform","translate","scale","rotate","perspective","filter"].some(e=>(n.willChange||"").includes(e))||["paint","layout","strict","content"].some(e=>(n.contain||"").includes(e))}function F(){return"undefined"!=typeof CSS&&!!CSS.supports&&CSS.supports("-webkit-backdrop-filter","none")}function V(e){return["html","body","#document"].includes(E(e))}function W(e){return P(e).getComputedStyle(e)}function _(e){return D(e)?{scrollLeft:e.scrollLeft,scrollTop:e.scrollTop}:{scrollLeft:e.scrollX,scrollTop:e.scrollY}}function z(e){if("html"===E(e))return e;let t=e.assignedSlot||e.parentNode||I(e)&&e.host||L(e);return I(t)?t.host:t}function G(e,t,n){var r;void 0===t&&(t=[]),void 0===n&&(n=!0);let o=function e(t){let n=z(t);return V(n)?t.ownerDocument?t.ownerDocument.body:t.body:M(n)&&O(n)?n:e(n)}(e),i=o===(null==(r=e.ownerDocument)?void 0:r.body),l=P(o);if(i){let e=$(l);return t.concat(l,l.visualViewport||[],O(o)?o:[],e&&n?G(e):[])}return t.concat(o,G(o,[],n))}function $(e){return e.parent&&Object.getPrototypeOf(e.parent)?e.frameElement:null}function K(e){let t=W(e),n=parseFloat(t.width)||0,r=parseFloat(t.height)||0,o=M(e),i=o?e.offsetWidth:n,l=o?e.offsetHeight:r,s=a(n)!==i||a(r)!==l;return s&&(n=i,r=l),{width:n,height:r,$:s}}function U(e){return D(e)?e:e.contextElement}function X(e){let t=U(e);if(!M(t))return u(1);let n=t.getBoundingClientRect(),{width:r,height:o,$:i}=K(t),l=(i?a(n.width):n.width)/r,s=(i?a(n.height):n.height)/o;return l&&Number.isFinite(l)||(l=1),s&&Number.isFinite(s)||(s=1),{x:l,y:s}}let q=u(0);function Y(e){let t=P(e);return F()&&t.visualViewport?{x:t.visualViewport.offsetLeft,y:t.visualViewport.offsetTop}:q}function Z(e,t,n,r){var o;void 0===t&&(t=!1),void 0===n&&(n=!1);let i=e.getBoundingClientRect(),l=U(e),a=u(1);t&&(r?D(r)&&(a=X(r)):a=X(e));let s=(void 0===(o=n)&&(o=!1),r&&(!o||r===P(l))&&o)?Y(l):u(0),c=(i.left+s.x)/a.x,d=(i.top+s.y)/a.y,f=i.width/a.x,p=i.height/a.y;if(l){let e=P(l),t=r&&D(r)?P(r):r,n=e,o=$(n);for(;o&&r&&t!==n;){let e=X(o),t=o.getBoundingClientRect(),r=W(o),i=t.left+(o.clientLeft+parseFloat(r.paddingLeft))*e.x,l=t.top+(o.clientTop+parseFloat(r.paddingTop))*e.y;c*=e.x,d*=e.y,f*=e.x,p*=e.y,c+=i,d+=l,o=$(n=P(o))}}return b({width:f,height:p,x:c,y:d})}function J(e,t){let n=_(e).scrollLeft;return t?t.left+n:Z(L(e)).left+n}function Q(e,t,n){void 0===n&&(n=!1);let r=e.getBoundingClientRect();return{x:r.left+t.scrollLeft-(n?0:J(e,r)),y:r.top+t.scrollTop}}function ee(e,t,n){let r;if("viewport"===t)r=function(e,t){let n=P(e),r=L(e),o=n.visualViewport,i=r.clientWidth,l=r.clientHeight,a=0,s=0;if(o){i=o.width,l=o.height;let e=F();(!e||e&&"fixed"===t)&&(a=o.offsetLeft,s=o.offsetTop)}return{width:i,height:l,x:a,y:s}}(e,n);else if("document"===t)r=function(e){let t=L(e),n=_(e),r=e.ownerDocument.body,o=l(t.scrollWidth,t.clientWidth,r.scrollWidth,r.clientWidth),i=l(t.scrollHeight,t.clientHeight,r.scrollHeight,r.clientHeight),a=-n.scrollLeft+J(e),s=-n.scrollTop;return"rtl"===W(r).direction&&(a+=l(t.clientWidth,r.clientWidth)-o),{width:o,height:i,x:a,y:s}}(L(e));else if(D(t))r=function(e,t){let n=Z(e,!0,"fixed"===t),r=n.top+e.clientTop,o=n.left+e.clientLeft,i=M(e)?X(e):u(1),l=e.clientWidth*i.x,a=e.clientHeight*i.y;return{width:l,height:a,x:o*i.x,y:r*i.y}}(t,n);else{let n=Y(e);r={x:t.x-n.x,y:t.y-n.y,width:t.width,height:t.height}}return b(r)}function et(e){return"static"===W(e).position}function en(e,t){if(!M(e)||"fixed"===W(e).position)return null;if(t)return t(e);let n=e.offsetParent;return L(e)===n&&(n=n.ownerDocument.body),n}function er(e,t){let n=P(e);if(H(e))return n;if(!M(e)){let t=z(e);for(;t&&!V(t);){if(D(t)&&!et(t))return t;t=z(t)}return n}let r=en(e,t);for(;r&&["table","td","th"].includes(E(r))&&et(r);)r=en(r,t);return r&&V(r)&&et(r)&&!B(r)?n:r||function(e){let t=z(e);for(;M(t)&&!V(t);){if(B(t))return t;if(H(t))break;t=z(t)}return null}(e)||n}let eo=async function(e){let t=this.getOffsetParent||er,n=this.getDimensions,r=await n(e.floating);return{reference:function(e,t,n){let r=M(t),o=L(t),i="fixed"===n,l=Z(e,!0,i,t),a={scrollLeft:0,scrollTop:0},s=u(0);if(r||!r&&!i)if(("body"!==E(t)||O(o))&&(a=_(t)),r){let e=Z(t,!0,i,t);s.x=e.x+t.clientLeft,s.y=e.y+t.clientTop}else o&&(s.x=J(o));i&&!r&&o&&(s.x=J(o));let c=!o||r||i?u(0):Q(o,a);return{x:l.left+a.scrollLeft-s.x-c.x,y:l.top+a.scrollTop-s.y-c.y,width:l.width,height:l.height}}(e.reference,await t(e.floating),e.strategy),floating:{x:0,y:0,width:r.width,height:r.height}}},ei={convertOffsetParentRelativeRectToViewportRelativeRect:function(e){let{elements:t,rect:n,offsetParent:r,strategy:o}=e,i="fixed"===o,l=L(r),a=!!t&&H(t.floating);if(r===l||a&&i)return n;let s={scrollLeft:0,scrollTop:0},c=u(1),d=u(0),f=M(r);if((f||!f&&!i)&&(("body"!==E(r)||O(l))&&(s=_(r)),M(r))){let e=Z(r);c=X(r),d.x=e.x+r.clientLeft,d.y=e.y+r.clientTop}let p=!l||f||i?u(0):Q(l,s,!0);return{width:n.width*c.x,height:n.height*c.y,x:n.x*c.x-s.scrollLeft*c.x+d.x+p.x,y:n.y*c.y-s.scrollTop*c.y+d.y+p.y}},getDocumentElement:L,getClippingRect:function(e){let{element:t,boundary:n,rootBoundary:r,strategy:o}=e,a=[..."clippingAncestors"===n?H(t)?[]:function(e,t){let n=t.get(e);if(n)return n;let r=G(e,[],!1).filter(e=>D(e)&&"body"!==E(e)),o=null,i="fixed"===W(e).position,l=i?z(e):e;for(;D(l)&&!V(l);){let t=W(l),n=B(l);n||"fixed"!==t.position||(o=null),(i?!n&&!o:!n&&"static"===t.position&&!!o&&["absolute","fixed"].includes(o.position)||O(l)&&!n&&function e(t,n){let r=z(t);return!(r===n||!D(r)||V(r))&&("fixed"===W(r).position||e(r,n))}(e,l))?r=r.filter(e=>e!==l):o=t,l=z(l)}return t.set(e,r),r}(t,this._c):[].concat(n),r],s=a[0],u=a.reduce((e,n)=>{let r=ee(t,n,o);return e.top=l(r.top,e.top),e.right=i(r.right,e.right),e.bottom=i(r.bottom,e.bottom),e.left=l(r.left,e.left),e},ee(t,s,o));return{width:u.right-u.left,height:u.bottom-u.top,x:u.left,y:u.top}},getOffsetParent:er,getElementRects:eo,getClientRects:function(e){return Array.from(e.getClientRects())},getDimensions:function(e){let{width:t,height:n}=K(e);return{width:t,height:n}},getScale:X,isElement:D,isRTL:function(e){return"rtl"===W(e).direction}};function el(e,t){return e.x===t.x&&e.y===t.y&&e.width===t.width&&e.height===t.height}let ea=e=>({name:"arrow",options:e,async fn(t){let{x:n,y:r,placement:o,rects:a,platform:s,elements:u,middlewareData:c}=t,{element:d,padding:p=0}=f(e,t)||{};if(null==d)return{};let w=x(p),y={x:n,y:r},b=m(v(o)),S=g(b),C=await s.getDimensions(d),R="y"===b,k=R?"clientHeight":"clientWidth",T=a.reference[S]+a.reference[b]-y[b]-a.floating[S],A=y[b]-a.reference[b],j=await (null==s.getOffsetParent?void 0:s.getOffsetParent(d)),E=j?j[k]:0;E&&await (null==s.isElement?void 0:s.isElement(j))||(E=u.floating[k]||a.floating[S]);let P=E/2-C[S]/2-1,L=i(w[R?"top":"left"],P),N=i(w[R?"bottom":"right"],P),D=E-C[S]-N,M=E/2-C[S]/2+(T/2-A/2),I=l(L,i(M,D)),O=!c.arrow&&null!=h(o)&&M!==I&&a.reference[S]/2-(M<L?L:N)-C[S]/2<0,H=O?M<L?M-L:M-D:0;return{[b]:y[b]+H,data:{[b]:I,centerOffset:M-I-H,...O&&{alignmentOffset:H}},reset:O}}}),es=(e,t,n)=>{let r=new Map,o={platform:ei,...n},i={...o.platform,_c:r};return C(e,t,{...o,platform:i})};var eu=n(51215),ec="undefined"!=typeof document?r.useLayoutEffect:function(){};function ed(e,t){let n,r,o;if(e===t)return!0;if(typeof e!=typeof t)return!1;if("function"==typeof e&&e.toString()===t.toString())return!0;if(e&&t&&"object"==typeof e){if(Array.isArray(e)){if((n=e.length)!==t.length)return!1;for(r=n;0!=r--;)if(!ed(e[r],t[r]))return!1;return!0}if((n=(o=Object.keys(e)).length)!==Object.keys(t).length)return!1;for(r=n;0!=r--;)if(!({}).hasOwnProperty.call(t,o[r]))return!1;for(r=n;0!=r--;){let n=o[r];if(("_owner"!==n||!e.$$typeof)&&!ed(e[n],t[n]))return!1}return!0}return e!=e&&t!=t}function ef(e){return"undefined"==typeof window?1:(e.ownerDocument.defaultView||window).devicePixelRatio||1}function ep(e,t){let n=ef(e);return Math.round(t*n)/n}function eh(e){let t=r.useRef(e);return ec(()=>{t.current=e}),t}let em=e=>({name:"arrow",options:e,fn(t){let{element:n,padding:r}="function"==typeof e?e(t):e;return n&&({}).hasOwnProperty.call(n,"current")?null!=n.current?ea({element:n.current,padding:r}).fn(t):{}:n?ea({element:n,padding:r}).fn(t):{}}}),eg=(e,t)=>({...function(e){return void 0===e&&(e=0),{name:"offset",options:e,async fn(t){var n,r;let{x:o,y:i,placement:l,middlewareData:a}=t,s=await A(t,e);return l===(null==(n=a.offset)?void 0:n.placement)&&null!=(r=a.arrow)&&r.alignmentOffset?{}:{x:o+s.x,y:i+s.y,data:{...s,placement:l}}}}}(e),options:[e,t]}),ev=(e,t)=>({...function(e){return void 0===e&&(e={}),{name:"shift",options:e,async fn(t){let{x:n,y:r,placement:o}=t,{mainAxis:a=!0,crossAxis:s=!1,limiter:u={fn:e=>{let{x:t,y:n}=e;return{x:t,y:n}}},...c}=f(e,t),d={x:n,y:r},h=await R(t,c),g=v(p(o)),w=m(g),y=d[w],x=d[g];if(a){let e="y"===w?"top":"left",t="y"===w?"bottom":"right",n=y+h[e],r=y-h[t];y=l(n,i(y,r))}if(s){let e="y"===g?"top":"left",t="y"===g?"bottom":"right",n=x+h[e],r=x-h[t];x=l(n,i(x,r))}let b=u.fn({...t,[w]:y,[g]:x});return{...b,data:{x:b.x-n,y:b.y-r,enabled:{[w]:a,[g]:s}}}}}}(e),options:[e,t]}),ew=(e,t)=>({...function(e){return void 0===e&&(e={}),{options:e,fn(t){let{x:n,y:r,placement:o,rects:i,middlewareData:l}=t,{offset:a=0,mainAxis:s=!0,crossAxis:u=!0}=f(e,t),c={x:n,y:r},d=v(o),h=m(d),g=c[h],w=c[d],y=f(a,t),x="number"==typeof y?{mainAxis:y,crossAxis:0}:{mainAxis:0,crossAxis:0,...y};if(s){let e="y"===h?"height":"width",t=i.reference[h]-i.floating[e]+x.mainAxis,n=i.reference[h]+i.reference[e]-x.mainAxis;g<t?g=t:g>n&&(g=n)}if(u){var b,S;let e="y"===h?"width":"height",t=["top","left"].includes(p(o)),n=i.reference[d]-i.floating[e]+(t&&(null==(b=l.offset)?void 0:b[d])||0)+(t?0:x.crossAxis),r=i.reference[d]+i.reference[e]+(t?0:(null==(S=l.offset)?void 0:S[d])||0)-(t?x.crossAxis:0);w<n?w=n:w>r&&(w=r)}return{[h]:g,[d]:w}}}}(e),options:[e,t]}),ey=(e,t)=>({...function(e){return void 0===e&&(e={}),{name:"flip",options:e,async fn(t){var n,r,o,i,l;let{placement:a,middlewareData:s,rects:u,initialPlacement:c,platform:d,elements:x}=t,{mainAxis:b=!0,crossAxis:S=!0,fallbackPlacements:C,fallbackStrategy:k="bestFit",fallbackAxisSideDirection:T="none",flipAlignment:A=!0,...j}=f(e,t);if(null!=(n=s.arrow)&&n.alignmentOffset)return{};let E=p(a),P=v(c),L=p(c)===c,N=await (null==d.isRTL?void 0:d.isRTL(x.floating)),D=C||(L||!A?[y(c)]:function(e){let t=y(e);return[w(e),t,w(t)]}(c)),M="none"!==T;!C&&M&&D.push(...function(e,t,n,r){let o=h(e),i=function(e,t,n){let r=["left","right"],o=["right","left"];switch(e){case"top":case"bottom":if(n)return t?o:r;return t?r:o;case"left":case"right":return t?["top","bottom"]:["bottom","top"];default:return[]}}(p(e),"start"===n,r);return o&&(i=i.map(e=>e+"-"+o),t&&(i=i.concat(i.map(w)))),i}(c,A,T,N));let I=[c,...D],O=await R(t,j),H=[],B=(null==(r=s.flip)?void 0:r.overflows)||[];if(b&&H.push(O[E]),S){let e=function(e,t,n){void 0===n&&(n=!1);let r=h(e),o=m(v(e)),i=g(o),l="x"===o?r===(n?"end":"start")?"right":"left":"start"===r?"bottom":"top";return t.reference[i]>t.floating[i]&&(l=y(l)),[l,y(l)]}(a,u,N);H.push(O[e[0]],O[e[1]])}if(B=[...B,{placement:a,overflows:H}],!H.every(e=>e<=0)){let e=((null==(o=s.flip)?void 0:o.index)||0)+1,t=I[e];if(t&&("alignment"!==S||P===v(t)||B.every(e=>e.overflows[0]>0&&v(e.placement)===P)))return{data:{index:e,overflows:B},reset:{placement:t}};let n=null==(i=B.filter(e=>e.overflows[0]<=0).sort((e,t)=>e.overflows[1]-t.overflows[1])[0])?void 0:i.placement;if(!n)switch(k){case"bestFit":{let e=null==(l=B.filter(e=>{if(M){let t=v(e.placement);return t===P||"y"===t}return!0}).map(e=>[e.placement,e.overflows.filter(e=>e>0).reduce((e,t)=>e+t,0)]).sort((e,t)=>e[1]-t[1])[0])?void 0:l[0];e&&(n=e);break}case"initialPlacement":n=c}if(a!==n)return{reset:{placement:n}}}return{}}}}(e),options:[e,t]}),ex=(e,t)=>({...function(e){return void 0===e&&(e={}),{name:"size",options:e,async fn(t){var n,r;let o,a,{placement:s,rects:u,platform:c,elements:d}=t,{apply:m=()=>{},...g}=f(e,t),w=await R(t,g),y=p(s),x=h(s),b="y"===v(s),{width:S,height:C}=u.floating;"top"===y||"bottom"===y?(o=y,a=x===(await (null==c.isRTL?void 0:c.isRTL(d.floating))?"start":"end")?"left":"right"):(a=y,o="end"===x?"top":"bottom");let k=C-w.top-w.bottom,T=S-w.left-w.right,A=i(C-w[o],k),j=i(S-w[a],T),E=!t.middlewareData.shift,P=A,L=j;if(null!=(n=t.middlewareData.shift)&&n.enabled.x&&(L=T),null!=(r=t.middlewareData.shift)&&r.enabled.y&&(P=k),E&&!x){let e=l(w.left,0),t=l(w.right,0),n=l(w.top,0),r=l(w.bottom,0);b?L=S-2*(0!==e||0!==t?e+t:l(w.left,w.right)):P=C-2*(0!==n||0!==r?n+r:l(w.top,w.bottom))}await m({...t,availableWidth:L,availableHeight:P});let N=await c.getDimensions(d.floating);return S!==N.width||C!==N.height?{reset:{rects:!0}}:{}}}}(e),options:[e,t]}),eb=(e,t)=>({...function(e){return void 0===e&&(e={}),{name:"hide",options:e,async fn(t){let{rects:n}=t,{strategy:r="referenceHidden",...o}=f(e,t);switch(r){case"referenceHidden":{let e=k(await R(t,{...o,elementContext:"reference"}),n.reference);return{data:{referenceHiddenOffsets:e,referenceHidden:T(e)}}}case"escaped":{let e=k(await R(t,{...o,altBoundary:!0}),n.floating);return{data:{escapedOffsets:e,escaped:T(e)}}}default:return{}}}}}(e),options:[e,t]}),eS=(e,t)=>({...em(e),options:[e,t]});var eC=n(14163),eR=n(60687),ek=r.forwardRef((e,t)=>{let{children:n,width:r=10,height:o=5,...i}=e;return(0,eR.jsx)(eC.sG.svg,{...i,ref:t,width:r,height:o,viewBox:"0 0 30 10",preserveAspectRatio:"none",children:e.asChild?n:(0,eR.jsx)("polygon",{points:"0,0 30,0 15,10"})})});ek.displayName="Arrow";var eT=n(98599),eA=n(11273),ej=n(13495),eE=n(66156),eP=n(18853),eL="Popper",[eN,eD]=(0,eA.A)(eL),[eM,eI]=eN(eL),eO=e=>{let{__scopePopper:t,children:n}=e,[o,i]=r.useState(null);return(0,eR.jsx)(eM,{scope:t,anchor:o,onAnchorChange:i,children:n})};eO.displayName=eL;var eH="PopperAnchor",eB=r.forwardRef((e,t)=>{let{__scopePopper:n,virtualRef:o,...i}=e,l=eI(eH,n),a=r.useRef(null),s=(0,eT.s)(t,a);return r.useEffect(()=>{l.onAnchorChange(o?.current||a.current)}),o?null:(0,eR.jsx)(eC.sG.div,{...i,ref:s})});eB.displayName=eH;var eF="PopperContent",[eV,eW]=eN(eF),e_=r.forwardRef((e,t)=>{let{__scopePopper:n,side:o="bottom",sideOffset:a=0,align:u="center",alignOffset:c=0,arrowPadding:d=0,avoidCollisions:f=!0,collisionBoundary:p=[],collisionPadding:h=0,sticky:m="partial",hideWhenDetached:g=!1,updatePositionStrategy:v="optimized",onPlaced:w,...y}=e,x=eI(eF,n),[b,S]=r.useState(null),C=(0,eT.s)(t,e=>S(e)),[R,k]=r.useState(null),T=(0,eP.X)(R),A=T?.width??0,j=T?.height??0,E="number"==typeof h?h:{top:0,right:0,bottom:0,left:0,...h},P=Array.isArray(p)?p:[p],N=P.length>0,D={padding:E,boundary:P.filter(eK),altBoundary:N},{refs:M,floatingStyles:I,placement:O,isPositioned:H,middlewareData:B}=function(e){void 0===e&&(e={});let{placement:t="bottom",strategy:n="absolute",middleware:o=[],platform:i,elements:{reference:l,floating:a}={},transform:s=!0,whileElementsMounted:u,open:c}=e,[d,f]=r.useState({x:0,y:0,strategy:n,placement:t,middlewareData:{},isPositioned:!1}),[p,h]=r.useState(o);ed(p,o)||h(o);let[m,g]=r.useState(null),[v,w]=r.useState(null),y=r.useCallback(e=>{e!==C.current&&(C.current=e,g(e))},[]),x=r.useCallback(e=>{e!==R.current&&(R.current=e,w(e))},[]),b=l||m,S=a||v,C=r.useRef(null),R=r.useRef(null),k=r.useRef(d),T=null!=u,A=eh(u),j=eh(i),E=eh(c),P=r.useCallback(()=>{if(!C.current||!R.current)return;let e={placement:t,strategy:n,middleware:p};j.current&&(e.platform=j.current),es(C.current,R.current,e).then(e=>{let t={...e,isPositioned:!1!==E.current};L.current&&!ed(k.current,t)&&(k.current=t,eu.flushSync(()=>{f(t)}))})},[p,t,n,j,E]);ec(()=>{!1===c&&k.current.isPositioned&&(k.current.isPositioned=!1,f(e=>({...e,isPositioned:!1})))},[c]);let L=r.useRef(!1);ec(()=>(L.current=!0,()=>{L.current=!1}),[]),ec(()=>{if(b&&(C.current=b),S&&(R.current=S),b&&S){if(A.current)return A.current(b,S,P);P()}},[b,S,P,A,T]);let N=r.useMemo(()=>({reference:C,floating:R,setReference:y,setFloating:x}),[y,x]),D=r.useMemo(()=>({reference:b,floating:S}),[b,S]),M=r.useMemo(()=>{let e={position:n,left:0,top:0};if(!D.floating)return e;let t=ep(D.floating,d.x),r=ep(D.floating,d.y);return s?{...e,transform:"translate("+t+"px, "+r+"px)",...ef(D.floating)>=1.5&&{willChange:"transform"}}:{position:n,left:t,top:r}},[n,s,D.floating,d.x,d.y]);return r.useMemo(()=>({...d,update:P,refs:N,elements:D,floatingStyles:M}),[d,P,N,D,M])}({strategy:"fixed",placement:o+("center"!==u?"-"+u:""),whileElementsMounted:(...e)=>(function(e,t,n,r){let o;void 0===r&&(r={});let{ancestorScroll:a=!0,ancestorResize:u=!0,elementResize:c="function"==typeof ResizeObserver,layoutShift:d="function"==typeof IntersectionObserver,animationFrame:f=!1}=r,p=U(e),h=a||u?[...p?G(p):[],...G(t)]:[];h.forEach(e=>{a&&e.addEventListener("scroll",n,{passive:!0}),u&&e.addEventListener("resize",n)});let m=p&&d?function(e,t){let n,r=null,o=L(e);function a(){var e;clearTimeout(n),null==(e=r)||e.disconnect(),r=null}return!function u(c,d){void 0===c&&(c=!1),void 0===d&&(d=1),a();let f=e.getBoundingClientRect(),{left:p,top:h,width:m,height:g}=f;if(c||t(),!m||!g)return;let v=s(h),w=s(o.clientWidth-(p+m)),y={rootMargin:-v+"px "+-w+"px "+-s(o.clientHeight-(h+g))+"px "+-s(p)+"px",threshold:l(0,i(1,d))||1},x=!0;function b(t){let r=t[0].intersectionRatio;if(r!==d){if(!x)return u();r?u(!1,r):n=setTimeout(()=>{u(!1,1e-7)},1e3)}1!==r||el(f,e.getBoundingClientRect())||u(),x=!1}try{r=new IntersectionObserver(b,{...y,root:o.ownerDocument})}catch(e){r=new IntersectionObserver(b,y)}r.observe(e)}(!0),a}(p,n):null,g=-1,v=null;c&&(v=new ResizeObserver(e=>{let[r]=e;r&&r.target===p&&v&&(v.unobserve(t),cancelAnimationFrame(g),g=requestAnimationFrame(()=>{var e;null==(e=v)||e.observe(t)})),n()}),p&&!f&&v.observe(p),v.observe(t));let w=f?Z(e):null;return f&&function t(){let r=Z(e);w&&!el(w,r)&&n(),w=r,o=requestAnimationFrame(t)}(),n(),()=>{var e;h.forEach(e=>{a&&e.removeEventListener("scroll",n),u&&e.removeEventListener("resize",n)}),null==m||m(),null==(e=v)||e.disconnect(),v=null,f&&cancelAnimationFrame(o)}})(...e,{animationFrame:"always"===v}),elements:{reference:x.anchor},middleware:[eg({mainAxis:a+j,alignmentAxis:c}),f&&ev({mainAxis:!0,crossAxis:!1,limiter:"partial"===m?ew():void 0,...D}),f&&ey({...D}),ex({...D,apply:({elements:e,rects:t,availableWidth:n,availableHeight:r})=>{let{width:o,height:i}=t.reference,l=e.floating.style;l.setProperty("--radix-popper-available-width",`${n}px`),l.setProperty("--radix-popper-available-height",`${r}px`),l.setProperty("--radix-popper-anchor-width",`${o}px`),l.setProperty("--radix-popper-anchor-height",`${i}px`)}}),R&&eS({element:R,padding:d}),eU({arrowWidth:A,arrowHeight:j}),g&&eb({strategy:"referenceHidden",...D})]}),[F,V]=eX(O),W=(0,ej.c)(w);(0,eE.N)(()=>{H&&W?.()},[H,W]);let _=B.arrow?.x,z=B.arrow?.y,$=B.arrow?.centerOffset!==0,[K,X]=r.useState();return(0,eE.N)(()=>{b&&X(window.getComputedStyle(b).zIndex)},[b]),(0,eR.jsx)("div",{ref:M.setFloating,"data-radix-popper-content-wrapper":"",style:{...I,transform:H?I.transform:"translate(0, -200%)",minWidth:"max-content",zIndex:K,"--radix-popper-transform-origin":[B.transformOrigin?.x,B.transformOrigin?.y].join(" "),...B.hide?.referenceHidden&&{visibility:"hidden",pointerEvents:"none"}},dir:e.dir,children:(0,eR.jsx)(eV,{scope:n,placedSide:F,onArrowChange:k,arrowX:_,arrowY:z,shouldHideArrow:$,children:(0,eR.jsx)(eC.sG.div,{"data-side":F,"data-align":V,...y,ref:C,style:{...y.style,animation:H?void 0:"none"}})})})});e_.displayName=eF;var ez="PopperArrow",eG={top:"bottom",right:"left",bottom:"top",left:"right"},e$=r.forwardRef(function(e,t){let{__scopePopper:n,...r}=e,o=eW(ez,n),i=eG[o.placedSide];return(0,eR.jsx)("span",{ref:o.onArrowChange,style:{position:"absolute",left:o.arrowX,top:o.arrowY,[i]:0,transformOrigin:{top:"",right:"0 0",bottom:"center 0",left:"100% 0"}[o.placedSide],transform:{top:"translateY(100%)",right:"translateY(50%) rotate(90deg) translateX(-50%)",bottom:"rotate(180deg)",left:"translateY(50%) rotate(-90deg) translateX(50%)"}[o.placedSide],visibility:o.shouldHideArrow?"hidden":void 0},children:(0,eR.jsx)(ek,{...r,ref:t,style:{...r.style,display:"block"}})})});function eK(e){return null!==e}e$.displayName=ez;var eU=e=>({name:"transformOrigin",options:e,fn(t){let{placement:n,rects:r,middlewareData:o}=t,i=o.arrow?.centerOffset!==0,l=i?0:e.arrowWidth,a=i?0:e.arrowHeight,[s,u]=eX(n),c={start:"0%",center:"50%",end:"100%"}[u],d=(o.arrow?.x??0)+l/2,f=(o.arrow?.y??0)+a/2,p="",h="";return"bottom"===s?(p=i?c:`${d}px`,h=`${-a}px`):"top"===s?(p=i?c:`${d}px`,h=`${r.floating.height+a}px`):"right"===s?(p=`${-a}px`,h=i?c:`${f}px`):"left"===s&&(p=`${r.floating.width+a}px`,h=i?c:`${f}px`),{data:{x:p,y:h}}}});function eX(e){let[t,n="center"]=e.split("-");return[t,n]}var eq=eO,eY=eB,eZ=e_,eJ=e$},83721:(e,t,n)=>{n.d(t,{Z:()=>o});var r=n(43210);function o(e){let t=r.useRef({value:e,previous:e});return r.useMemo(()=>(t.current.value!==e&&(t.current.previous=t.current.value,t.current.value=e),t.current.previous),[e])}},97822:(e,t,n)=>{n.d(t,{UC:()=>eN,In:()=>eP,q7:()=>eM,VF:()=>eO,p4:()=>eI,ZL:()=>eL,bL:()=>eA,wn:()=>eB,PP:()=>eH,l9:()=>ej,WT:()=>eE,LM:()=>eD});var r=n(43210),o=n(51215);function i(e,[t,n]){return Math.min(n,Math.max(t,e))}var l=n(70569),a=n(9510),s=n(98599),u=n(11273),c=n(43),d=n(31355),f=n(1359),p=n(32547),h=n(96963),m=n(55509),g=n(25028),v=n(14163),w=n(8730),y=n(13495),x=n(65551),b=n(66156),S=n(83721),C=n(60687),R=Object.freeze({position:"absolute",border:0,width:1,height:1,padding:0,margin:-1,overflow:"hidden",clip:"rect(0, 0, 0, 0)",whiteSpace:"nowrap",wordWrap:"normal"});r.forwardRef((e,t)=>(0,C.jsx)(v.sG.span,{...e,ref:t,style:{...R,...e.style}})).displayName="VisuallyHidden";var k=n(63376),T=n(42247),A=[" ","Enter","ArrowUp","ArrowDown"],j=[" ","Enter"],E="Select",[P,L,N]=(0,a.N)(E),[D,M]=(0,u.A)(E,[N,m.Bk]),I=(0,m.Bk)(),[O,H]=D(E),[B,F]=D(E),V=e=>{let{__scopeSelect:t,children:n,open:o,defaultOpen:i,onOpenChange:l,value:a,defaultValue:s,onValueChange:u,dir:d,name:f,autoComplete:p,disabled:g,required:v,form:w}=e,y=I(t),[b,S]=r.useState(null),[R,k]=r.useState(null),[T,A]=r.useState(!1),j=(0,c.jH)(d),[L,N]=(0,x.i)({prop:o,defaultProp:i??!1,onChange:l,caller:E}),[D,M]=(0,x.i)({prop:a,defaultProp:s,onChange:u,caller:E}),H=r.useRef(null),F=!b||w||!!b.closest("form"),[V,W]=r.useState(new Set),_=Array.from(V).map(e=>e.props.value).join(";");return(0,C.jsx)(m.bL,{...y,children:(0,C.jsxs)(O,{required:v,scope:t,trigger:b,onTriggerChange:S,valueNode:R,onValueNodeChange:k,valueNodeHasChildren:T,onValueNodeHasChildrenChange:A,contentId:(0,h.B)(),value:D,onValueChange:M,open:L,onOpenChange:N,dir:j,triggerPointerDownPosRef:H,disabled:g,children:[(0,C.jsx)(P.Provider,{scope:t,children:(0,C.jsx)(B,{scope:e.__scopeSelect,onNativeOptionAdd:r.useCallback(e=>{W(t=>new Set(t).add(e))},[]),onNativeOptionRemove:r.useCallback(e=>{W(t=>{let n=new Set(t);return n.delete(e),n})},[]),children:n})}),F?(0,C.jsxs)(eC,{"aria-hidden":!0,required:v,tabIndex:-1,name:f,autoComplete:p,value:D,onChange:e=>M(e.target.value),disabled:g,form:w,children:[void 0===D?(0,C.jsx)("option",{value:""}):null,Array.from(V)]},_):null]})})};V.displayName=E;var W="SelectTrigger",_=r.forwardRef((e,t)=>{let{__scopeSelect:n,disabled:o=!1,...i}=e,a=I(n),u=H(W,n),c=u.disabled||o,d=(0,s.s)(t,u.onTriggerChange),f=L(n),p=r.useRef("touch"),[h,g,w]=ek(e=>{let t=f().filter(e=>!e.disabled),n=t.find(e=>e.value===u.value),r=eT(t,e,n);void 0!==r&&u.onValueChange(r.value)}),y=e=>{c||(u.onOpenChange(!0),w()),e&&(u.triggerPointerDownPosRef.current={x:Math.round(e.pageX),y:Math.round(e.pageY)})};return(0,C.jsx)(m.Mz,{asChild:!0,...a,children:(0,C.jsx)(v.sG.button,{type:"button",role:"combobox","aria-controls":u.contentId,"aria-expanded":u.open,"aria-required":u.required,"aria-autocomplete":"none",dir:u.dir,"data-state":u.open?"open":"closed",disabled:c,"data-disabled":c?"":void 0,"data-placeholder":eR(u.value)?"":void 0,...i,ref:d,onClick:(0,l.m)(i.onClick,e=>{e.currentTarget.focus(),"mouse"!==p.current&&y(e)}),onPointerDown:(0,l.m)(i.onPointerDown,e=>{p.current=e.pointerType;let t=e.target;t.hasPointerCapture(e.pointerId)&&t.releasePointerCapture(e.pointerId),0===e.button&&!1===e.ctrlKey&&"mouse"===e.pointerType&&(y(e),e.preventDefault())}),onKeyDown:(0,l.m)(i.onKeyDown,e=>{let t=""!==h.current;e.ctrlKey||e.altKey||e.metaKey||1!==e.key.length||g(e.key),(!t||" "!==e.key)&&A.includes(e.key)&&(y(),e.preventDefault())})})})});_.displayName=W;var z="SelectValue",G=r.forwardRef((e,t)=>{let{__scopeSelect:n,className:r,style:o,children:i,placeholder:l="",...a}=e,u=H(z,n),{onValueNodeHasChildrenChange:c}=u,d=void 0!==i,f=(0,s.s)(t,u.onValueNodeChange);return(0,b.N)(()=>{c(d)},[c,d]),(0,C.jsx)(v.sG.span,{...a,ref:f,style:{pointerEvents:"none"},children:eR(u.value)?(0,C.jsx)(C.Fragment,{children:l}):i})});G.displayName=z;var $=r.forwardRef((e,t)=>{let{__scopeSelect:n,children:r,...o}=e;return(0,C.jsx)(v.sG.span,{"aria-hidden":!0,...o,ref:t,children:r||"▼"})});$.displayName="SelectIcon";var K=e=>(0,C.jsx)(g.Z,{asChild:!0,...e});K.displayName="SelectPortal";var U="SelectContent",X=r.forwardRef((e,t)=>{let n=H(U,e.__scopeSelect),[i,l]=r.useState();return((0,b.N)(()=>{l(new DocumentFragment)},[]),n.open)?(0,C.jsx)(J,{...e,ref:t}):i?o.createPortal((0,C.jsx)(q,{scope:e.__scopeSelect,children:(0,C.jsx)(P.Slot,{scope:e.__scopeSelect,children:(0,C.jsx)("div",{children:e.children})})}),i):null});X.displayName=U;var[q,Y]=D(U),Z=(0,w.TL)("SelectContent.RemoveScroll"),J=r.forwardRef((e,t)=>{let{__scopeSelect:n,position:o="item-aligned",onCloseAutoFocus:i,onEscapeKeyDown:a,onPointerDownOutside:u,side:c,sideOffset:h,align:m,alignOffset:g,arrowPadding:v,collisionBoundary:w,collisionPadding:y,sticky:x,hideWhenDetached:b,avoidCollisions:S,...R}=e,A=H(U,n),[j,E]=r.useState(null),[P,N]=r.useState(null),D=(0,s.s)(t,e=>E(e)),[M,I]=r.useState(null),[O,B]=r.useState(null),F=L(n),[V,W]=r.useState(!1),_=r.useRef(!1);r.useEffect(()=>{if(j)return(0,k.Eq)(j)},[j]),(0,f.Oh)();let z=r.useCallback(e=>{let[t,...n]=F().map(e=>e.ref.current),[r]=n.slice(-1),o=document.activeElement;for(let n of e)if(n===o||(n?.scrollIntoView({block:"nearest"}),n===t&&P&&(P.scrollTop=0),n===r&&P&&(P.scrollTop=P.scrollHeight),n?.focus(),document.activeElement!==o))return},[F,P]),G=r.useCallback(()=>z([M,j]),[z,M,j]);r.useEffect(()=>{V&&G()},[V,G]);let{onOpenChange:$,triggerPointerDownPosRef:K}=A;r.useEffect(()=>{if(j){let e={x:0,y:0},t=t=>{e={x:Math.abs(Math.round(t.pageX)-(K.current?.x??0)),y:Math.abs(Math.round(t.pageY)-(K.current?.y??0))}},n=n=>{e.x<=10&&e.y<=10?n.preventDefault():j.contains(n.target)||$(!1),document.removeEventListener("pointermove",t),K.current=null};return null!==K.current&&(document.addEventListener("pointermove",t),document.addEventListener("pointerup",n,{capture:!0,once:!0})),()=>{document.removeEventListener("pointermove",t),document.removeEventListener("pointerup",n,{capture:!0})}}},[j,$,K]),r.useEffect(()=>{let e=()=>$(!1);return window.addEventListener("blur",e),window.addEventListener("resize",e),()=>{window.removeEventListener("blur",e),window.removeEventListener("resize",e)}},[$]);let[X,Y]=ek(e=>{let t=F().filter(e=>!e.disabled),n=t.find(e=>e.ref.current===document.activeElement),r=eT(t,e,n);r&&setTimeout(()=>r.ref.current.focus())}),J=r.useCallback((e,t,n)=>{let r=!_.current&&!n;(void 0!==A.value&&A.value===t||r)&&(I(e),r&&(_.current=!0))},[A.value]),et=r.useCallback(()=>j?.focus(),[j]),en=r.useCallback((e,t,n)=>{let r=!_.current&&!n;(void 0!==A.value&&A.value===t||r)&&B(e)},[A.value]),er="popper"===o?ee:Q,eo=er===ee?{side:c,sideOffset:h,align:m,alignOffset:g,arrowPadding:v,collisionBoundary:w,collisionPadding:y,sticky:x,hideWhenDetached:b,avoidCollisions:S}:{};return(0,C.jsx)(q,{scope:n,content:j,viewport:P,onViewportChange:N,itemRefCallback:J,selectedItem:M,onItemLeave:et,itemTextRefCallback:en,focusSelectedItem:G,selectedItemText:O,position:o,isPositioned:V,searchRef:X,children:(0,C.jsx)(T.A,{as:Z,allowPinchZoom:!0,children:(0,C.jsx)(p.n,{asChild:!0,trapped:A.open,onMountAutoFocus:e=>{e.preventDefault()},onUnmountAutoFocus:(0,l.m)(i,e=>{A.trigger?.focus({preventScroll:!0}),e.preventDefault()}),children:(0,C.jsx)(d.qW,{asChild:!0,disableOutsidePointerEvents:!0,onEscapeKeyDown:a,onPointerDownOutside:u,onFocusOutside:e=>e.preventDefault(),onDismiss:()=>A.onOpenChange(!1),children:(0,C.jsx)(er,{role:"listbox",id:A.contentId,"data-state":A.open?"open":"closed",dir:A.dir,onContextMenu:e=>e.preventDefault(),...R,...eo,onPlaced:()=>W(!0),ref:D,style:{display:"flex",flexDirection:"column",outline:"none",...R.style},onKeyDown:(0,l.m)(R.onKeyDown,e=>{let t=e.ctrlKey||e.altKey||e.metaKey;if("Tab"===e.key&&e.preventDefault(),t||1!==e.key.length||Y(e.key),["ArrowUp","ArrowDown","Home","End"].includes(e.key)){let t=F().filter(e=>!e.disabled).map(e=>e.ref.current);if(["ArrowUp","End"].includes(e.key)&&(t=t.slice().reverse()),["ArrowUp","ArrowDown"].includes(e.key)){let n=e.target,r=t.indexOf(n);t=t.slice(r+1)}setTimeout(()=>z(t)),e.preventDefault()}})})})})})})});J.displayName="SelectContentImpl";var Q=r.forwardRef((e,t)=>{let{__scopeSelect:n,onPlaced:o,...l}=e,a=H(U,n),u=Y(U,n),[c,d]=r.useState(null),[f,p]=r.useState(null),h=(0,s.s)(t,e=>p(e)),m=L(n),g=r.useRef(!1),w=r.useRef(!0),{viewport:y,selectedItem:x,selectedItemText:S,focusSelectedItem:R}=u,k=r.useCallback(()=>{if(a.trigger&&a.valueNode&&c&&f&&y&&x&&S){let e=a.trigger.getBoundingClientRect(),t=f.getBoundingClientRect(),n=a.valueNode.getBoundingClientRect(),r=S.getBoundingClientRect();if("rtl"!==a.dir){let o=r.left-t.left,l=n.left-o,a=e.left-l,s=e.width+a,u=Math.max(s,t.width),d=i(l,[10,Math.max(10,window.innerWidth-10-u)]);c.style.minWidth=s+"px",c.style.left=d+"px"}else{let o=t.right-r.right,l=window.innerWidth-n.right-o,a=window.innerWidth-e.right-l,s=e.width+a,u=Math.max(s,t.width),d=i(l,[10,Math.max(10,window.innerWidth-10-u)]);c.style.minWidth=s+"px",c.style.right=d+"px"}let l=m(),s=window.innerHeight-20,u=y.scrollHeight,d=window.getComputedStyle(f),p=parseInt(d.borderTopWidth,10),h=parseInt(d.paddingTop,10),v=parseInt(d.borderBottomWidth,10),w=p+h+u+parseInt(d.paddingBottom,10)+v,b=Math.min(5*x.offsetHeight,w),C=window.getComputedStyle(y),R=parseInt(C.paddingTop,10),k=parseInt(C.paddingBottom,10),T=e.top+e.height/2-10,A=x.offsetHeight/2,j=p+h+(x.offsetTop+A);if(j<=T){let e=l.length>0&&x===l[l.length-1].ref.current;c.style.bottom="0px";let t=Math.max(s-T,A+(e?k:0)+(f.clientHeight-y.offsetTop-y.offsetHeight)+v);c.style.height=j+t+"px"}else{let e=l.length>0&&x===l[0].ref.current;c.style.top="0px";let t=Math.max(T,p+y.offsetTop+(e?R:0)+A);c.style.height=t+(w-j)+"px",y.scrollTop=j-T+y.offsetTop}c.style.margin="10px 0",c.style.minHeight=b+"px",c.style.maxHeight=s+"px",o?.(),requestAnimationFrame(()=>g.current=!0)}},[m,a.trigger,a.valueNode,c,f,y,x,S,a.dir,o]);(0,b.N)(()=>k(),[k]);let[T,A]=r.useState();(0,b.N)(()=>{f&&A(window.getComputedStyle(f).zIndex)},[f]);let j=r.useCallback(e=>{e&&!0===w.current&&(k(),R?.(),w.current=!1)},[k,R]);return(0,C.jsx)(et,{scope:n,contentWrapper:c,shouldExpandOnScrollRef:g,onScrollButtonChange:j,children:(0,C.jsx)("div",{ref:d,style:{display:"flex",flexDirection:"column",position:"fixed",zIndex:T},children:(0,C.jsx)(v.sG.div,{...l,ref:h,style:{boxSizing:"border-box",maxHeight:"100%",...l.style}})})})});Q.displayName="SelectItemAlignedPosition";var ee=r.forwardRef((e,t)=>{let{__scopeSelect:n,align:r="start",collisionPadding:o=10,...i}=e,l=I(n);return(0,C.jsx)(m.UC,{...l,...i,ref:t,align:r,collisionPadding:o,style:{boxSizing:"border-box",...i.style,"--radix-select-content-transform-origin":"var(--radix-popper-transform-origin)","--radix-select-content-available-width":"var(--radix-popper-available-width)","--radix-select-content-available-height":"var(--radix-popper-available-height)","--radix-select-trigger-width":"var(--radix-popper-anchor-width)","--radix-select-trigger-height":"var(--radix-popper-anchor-height)"}})});ee.displayName="SelectPopperPosition";var[et,en]=D(U,{}),er="SelectViewport",eo=r.forwardRef((e,t)=>{let{__scopeSelect:n,nonce:o,...i}=e,a=Y(er,n),u=en(er,n),c=(0,s.s)(t,a.onViewportChange),d=r.useRef(0);return(0,C.jsxs)(C.Fragment,{children:[(0,C.jsx)("style",{dangerouslySetInnerHTML:{__html:"[data-radix-select-viewport]{scrollbar-width:none;-ms-overflow-style:none;-webkit-overflow-scrolling:touch;}[data-radix-select-viewport]::-webkit-scrollbar{display:none}"},nonce:o}),(0,C.jsx)(P.Slot,{scope:n,children:(0,C.jsx)(v.sG.div,{"data-radix-select-viewport":"",role:"presentation",...i,ref:c,style:{position:"relative",flex:1,overflow:"hidden auto",...i.style},onScroll:(0,l.m)(i.onScroll,e=>{let t=e.currentTarget,{contentWrapper:n,shouldExpandOnScrollRef:r}=u;if(r?.current&&n){let e=Math.abs(d.current-t.scrollTop);if(e>0){let r=window.innerHeight-20,o=Math.max(parseFloat(n.style.minHeight),parseFloat(n.style.height));if(o<r){let i=o+e,l=Math.min(r,i),a=i-l;n.style.height=l+"px","0px"===n.style.bottom&&(t.scrollTop=a>0?a:0,n.style.justifyContent="flex-end")}}}d.current=t.scrollTop})})})]})});eo.displayName=er;var ei="SelectGroup",[el,ea]=D(ei);r.forwardRef((e,t)=>{let{__scopeSelect:n,...r}=e,o=(0,h.B)();return(0,C.jsx)(el,{scope:n,id:o,children:(0,C.jsx)(v.sG.div,{role:"group","aria-labelledby":o,...r,ref:t})})}).displayName=ei;var es="SelectLabel";r.forwardRef((e,t)=>{let{__scopeSelect:n,...r}=e,o=ea(es,n);return(0,C.jsx)(v.sG.div,{id:o.id,...r,ref:t})}).displayName=es;var eu="SelectItem",[ec,ed]=D(eu),ef=r.forwardRef((e,t)=>{let{__scopeSelect:n,value:o,disabled:i=!1,textValue:a,...u}=e,c=H(eu,n),d=Y(eu,n),f=c.value===o,[p,m]=r.useState(a??""),[g,w]=r.useState(!1),y=(0,s.s)(t,e=>d.itemRefCallback?.(e,o,i)),x=(0,h.B)(),b=r.useRef("touch"),S=()=>{i||(c.onValueChange(o),c.onOpenChange(!1))};if(""===o)throw Error("A <Select.Item /> must have a value prop that is not an empty string. This is because the Select value can be set to an empty string to clear the selection and show the placeholder.");return(0,C.jsx)(ec,{scope:n,value:o,disabled:i,textId:x,isSelected:f,onItemTextChange:r.useCallback(e=>{m(t=>t||(e?.textContent??"").trim())},[]),children:(0,C.jsx)(P.ItemSlot,{scope:n,value:o,disabled:i,textValue:p,children:(0,C.jsx)(v.sG.div,{role:"option","aria-labelledby":x,"data-highlighted":g?"":void 0,"aria-selected":f&&g,"data-state":f?"checked":"unchecked","aria-disabled":i||void 0,"data-disabled":i?"":void 0,tabIndex:i?void 0:-1,...u,ref:y,onFocus:(0,l.m)(u.onFocus,()=>w(!0)),onBlur:(0,l.m)(u.onBlur,()=>w(!1)),onClick:(0,l.m)(u.onClick,()=>{"mouse"!==b.current&&S()}),onPointerUp:(0,l.m)(u.onPointerUp,()=>{"mouse"===b.current&&S()}),onPointerDown:(0,l.m)(u.onPointerDown,e=>{b.current=e.pointerType}),onPointerMove:(0,l.m)(u.onPointerMove,e=>{b.current=e.pointerType,i?d.onItemLeave?.():"mouse"===b.current&&e.currentTarget.focus({preventScroll:!0})}),onPointerLeave:(0,l.m)(u.onPointerLeave,e=>{e.currentTarget===document.activeElement&&d.onItemLeave?.()}),onKeyDown:(0,l.m)(u.onKeyDown,e=>{(d.searchRef?.current===""||" "!==e.key)&&(j.includes(e.key)&&S()," "===e.key&&e.preventDefault())})})})})});ef.displayName=eu;var ep="SelectItemText",eh=r.forwardRef((e,t)=>{let{__scopeSelect:n,className:i,style:l,...a}=e,u=H(ep,n),c=Y(ep,n),d=ed(ep,n),f=F(ep,n),[p,h]=r.useState(null),m=(0,s.s)(t,e=>h(e),d.onItemTextChange,e=>c.itemTextRefCallback?.(e,d.value,d.disabled)),g=p?.textContent,w=r.useMemo(()=>(0,C.jsx)("option",{value:d.value,disabled:d.disabled,children:g},d.value),[d.disabled,d.value,g]),{onNativeOptionAdd:y,onNativeOptionRemove:x}=f;return(0,b.N)(()=>(y(w),()=>x(w)),[y,x,w]),(0,C.jsxs)(C.Fragment,{children:[(0,C.jsx)(v.sG.span,{id:d.textId,...a,ref:m}),d.isSelected&&u.valueNode&&!u.valueNodeHasChildren?o.createPortal(a.children,u.valueNode):null]})});eh.displayName=ep;var em="SelectItemIndicator",eg=r.forwardRef((e,t)=>{let{__scopeSelect:n,...r}=e;return ed(em,n).isSelected?(0,C.jsx)(v.sG.span,{"aria-hidden":!0,...r,ref:t}):null});eg.displayName=em;var ev="SelectScrollUpButton",ew=r.forwardRef((e,t)=>{let n=Y(ev,e.__scopeSelect),o=en(ev,e.__scopeSelect),[i,l]=r.useState(!1),a=(0,s.s)(t,o.onScrollButtonChange);return(0,b.N)(()=>{if(n.viewport&&n.isPositioned){let e=function(){l(t.scrollTop>0)},t=n.viewport;return e(),t.addEventListener("scroll",e),()=>t.removeEventListener("scroll",e)}},[n.viewport,n.isPositioned]),i?(0,C.jsx)(eb,{...e,ref:a,onAutoScroll:()=>{let{viewport:e,selectedItem:t}=n;e&&t&&(e.scrollTop=e.scrollTop-t.offsetHeight)}}):null});ew.displayName=ev;var ey="SelectScrollDownButton",ex=r.forwardRef((e,t)=>{let n=Y(ey,e.__scopeSelect),o=en(ey,e.__scopeSelect),[i,l]=r.useState(!1),a=(0,s.s)(t,o.onScrollButtonChange);return(0,b.N)(()=>{if(n.viewport&&n.isPositioned){let e=function(){let e=t.scrollHeight-t.clientHeight;l(Math.ceil(t.scrollTop)<e)},t=n.viewport;return e(),t.addEventListener("scroll",e),()=>t.removeEventListener("scroll",e)}},[n.viewport,n.isPositioned]),i?(0,C.jsx)(eb,{...e,ref:a,onAutoScroll:()=>{let{viewport:e,selectedItem:t}=n;e&&t&&(e.scrollTop=e.scrollTop+t.offsetHeight)}}):null});ex.displayName=ey;var eb=r.forwardRef((e,t)=>{let{__scopeSelect:n,onAutoScroll:o,...i}=e,a=Y("SelectScrollButton",n),s=r.useRef(null),u=L(n),c=r.useCallback(()=>{null!==s.current&&(window.clearInterval(s.current),s.current=null)},[]);return r.useEffect(()=>()=>c(),[c]),(0,b.N)(()=>{let e=u().find(e=>e.ref.current===document.activeElement);e?.ref.current?.scrollIntoView({block:"nearest"})},[u]),(0,C.jsx)(v.sG.div,{"aria-hidden":!0,...i,ref:t,style:{flexShrink:0,...i.style},onPointerDown:(0,l.m)(i.onPointerDown,()=>{null===s.current&&(s.current=window.setInterval(o,50))}),onPointerMove:(0,l.m)(i.onPointerMove,()=>{a.onItemLeave?.(),null===s.current&&(s.current=window.setInterval(o,50))}),onPointerLeave:(0,l.m)(i.onPointerLeave,()=>{c()})})});r.forwardRef((e,t)=>{let{__scopeSelect:n,...r}=e;return(0,C.jsx)(v.sG.div,{"aria-hidden":!0,...r,ref:t})}).displayName="SelectSeparator";var eS="SelectArrow";r.forwardRef((e,t)=>{let{__scopeSelect:n,...r}=e,o=I(n),i=H(eS,n),l=Y(eS,n);return i.open&&"popper"===l.position?(0,C.jsx)(m.i3,{...o,...r,ref:t}):null}).displayName=eS;var eC=r.forwardRef(({__scopeSelect:e,value:t,...n},o)=>{let i=r.useRef(null),l=(0,s.s)(o,i),a=(0,S.Z)(t);return r.useEffect(()=>{let e=i.current;if(!e)return;let n=Object.getOwnPropertyDescriptor(window.HTMLSelectElement.prototype,"value").set;if(a!==t&&n){let r=new Event("change",{bubbles:!0});n.call(e,t),e.dispatchEvent(r)}},[a,t]),(0,C.jsx)(v.sG.select,{...n,style:{...R,...n.style},ref:l,defaultValue:t})});function eR(e){return""===e||void 0===e}function ek(e){let t=(0,y.c)(e),n=r.useRef(""),o=r.useRef(0),i=r.useCallback(e=>{let r=n.current+e;t(r),function e(t){n.current=t,window.clearTimeout(o.current),""!==t&&(o.current=window.setTimeout(()=>e(""),1e3))}(r)},[t]),l=r.useCallback(()=>{n.current="",window.clearTimeout(o.current)},[]);return r.useEffect(()=>()=>window.clearTimeout(o.current),[]),[n,i,l]}function eT(e,t,n){var r,o;let i=t.length>1&&Array.from(t).every(e=>e===t[0])?t[0]:t,l=n?e.indexOf(n):-1,a=(r=e,o=Math.max(l,0),r.map((e,t)=>r[(o+t)%r.length]));1===i.length&&(a=a.filter(e=>e!==n));let s=a.find(e=>e.textValue.toLowerCase().startsWith(i.toLowerCase()));return s!==n?s:void 0}eC.displayName="SelectBubbleInput";var eA=V,ej=_,eE=G,eP=$,eL=K,eN=X,eD=eo,eM=ef,eI=eh,eO=eg,eH=ew,eB=ex}};
{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/CMS/webapp-nextjs/src/app/debug-login/page.tsx"], "sourcesContent": ["'use client'\n\nimport { useState } from 'react'\nimport { useRouter } from 'next/navigation'\n\nexport default function DebugLoginPage() {\n  const [result, setResult] = useState('')\n  const [loading, setLoading] = useState(false)\n  const router = useRouter()\n\n  const testLogin = async () => {\n    setLoading(true)\n    setResult('Starting login test...\\n')\n    \n    try {\n      // Test 1: Basic fetch\n      setResult(prev => prev + 'Step 1: Testing basic fetch...\\n')\n      \n      const formData = new FormData()\n      formData.append('username', 'admin')\n      formData.append('password', 'admin')\n\n      setResult(prev => prev + 'Step 2: Sending login request...\\n')\n      \n      const response = await fetch('http://localhost:8001/api/auth/login', {\n        method: 'POST',\n        body: formData\n      })\n\n      setResult(prev => prev + `Step 3: Response status: ${response.status}\\n`)\n      \n      if (!response.ok) {\n        throw new Error(`HTTP error! status: ${response.status}`)\n      }\n\n      const data = await response.json()\n      setResult(prev => prev + `Step 4: Response data received\\n`)\n      setResult(prev => prev + `Data: ${JSON.stringify(data, null, 2)}\\n`)\n\n      // Test 2: Save token\n      setResult(prev => prev + 'Step 5: Saving token to localStorage...\\n')\n      localStorage.setItem('token', data.access_token)\n      \n      // Test 3: Verify token was saved\n      const savedToken = localStorage.getItem('token')\n      setResult(prev => prev + `Step 6: Token saved: ${savedToken ? 'YES' : 'NO'}\\n`)\n\n      // Test 4: Test router\n      setResult(prev => prev + 'Step 7: Testing router...\\n')\n      \n      if (data.role === 'owner') {\n        setResult(prev => prev + 'Step 8: Redirecting to /admin...\\n')\n        \n        // Try different redirect methods\n        setTimeout(() => {\n          setResult(prev => prev + 'Step 9: Using router.push...\\n')\n          router.push('/admin')\n        }, 1000)\n        \n        setTimeout(() => {\n          setResult(prev => prev + 'Step 10: Using window.location...\\n')\n          window.location.href = '/admin'\n        }, 3000)\n      }\n\n    } catch (error) {\n      setResult(prev => prev + `ERROR: ${error.message}\\n`)\n      console.error('Login test error:', error)\n    } finally {\n      setLoading(false)\n    }\n  }\n\n  const testRouter = () => {\n    setResult('Testing router directly...\\n')\n    try {\n      router.push('/admin')\n      setResult(prev => prev + 'Router.push called successfully\\n')\n    } catch (error) {\n      setResult(prev => prev + `Router error: ${error.message}\\n`)\n    }\n  }\n\n  const testWindowLocation = () => {\n    setResult('Testing window.location...\\n')\n    window.location.href = '/admin'\n  }\n\n  const clearStorage = () => {\n    localStorage.clear()\n    setResult('LocalStorage cleared\\n')\n  }\n\n  return (\n    <div style={{ padding: '20px', fontFamily: 'monospace' }}>\n      <h1>Debug Login Page</h1>\n      \n      <div style={{ marginBottom: '20px' }}>\n        <button \n          onClick={testLogin} \n          disabled={loading}\n          style={{ \n            padding: '10px 20px', \n            marginRight: '10px',\n            backgroundColor: '#007bff',\n            color: 'white',\n            border: 'none',\n            borderRadius: '5px',\n            cursor: loading ? 'not-allowed' : 'pointer'\n          }}\n        >\n          {loading ? 'Testing...' : 'Full Login Test'}\n        </button>\n        \n        <button \n          onClick={testRouter} \n          style={{ \n            padding: '10px 20px',\n            marginRight: '10px',\n            backgroundColor: '#28a745',\n            color: 'white',\n            border: 'none',\n            borderRadius: '5px',\n            cursor: 'pointer'\n          }}\n        >\n          Test Router Only\n        </button>\n        \n        <button \n          onClick={testWindowLocation} \n          style={{ \n            padding: '10px 20px',\n            marginRight: '10px',\n            backgroundColor: '#ffc107',\n            color: 'black',\n            border: 'none',\n            borderRadius: '5px',\n            cursor: 'pointer'\n          }}\n        >\n          Test Window.location\n        </button>\n        \n        <button \n          onClick={clearStorage} \n          style={{ \n            padding: '10px 20px',\n            backgroundColor: '#dc3545',\n            color: 'white',\n            border: 'none',\n            borderRadius: '5px',\n            cursor: 'pointer'\n          }}\n        >\n          Clear Storage\n        </button>\n      </div>\n\n      <div style={{ \n        backgroundColor: '#f8f9fa', \n        padding: '15px', \n        borderRadius: '5px',\n        whiteSpace: 'pre-wrap',\n        minHeight: '400px',\n        fontSize: '12px',\n        fontFamily: 'monospace',\n        border: '1px solid #ddd'\n      }}>\n        {result || 'Click a button to start testing...'}\n      </div>\n      \n      <div style={{ marginTop: '20px' }}>\n        <h3>Current localStorage:</h3>\n        <div style={{ \n          backgroundColor: '#f8f9fa', \n          padding: '10px', \n          borderRadius: '5px',\n          fontSize: '12px',\n          fontFamily: 'monospace'\n        }}>\n          {typeof window !== 'undefined' ? \n            Object.keys(localStorage).map(key => \n              `${key}: ${localStorage.getItem(key)?.substring(0, 50)}...`\n            ).join('\\n') || 'Empty'\n            : 'Not available (SSR)'\n          }\n        </div>\n      </div>\n    </div>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;;;AAHA;;;AAKe,SAAS;;IACtB,MAAM,CAAC,QAAQ,UAAU,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACrC,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACvC,MAAM,SAAS,CAAA,GAAA,qIAAA,CAAA,YAAS,AAAD;IAEvB,MAAM,YAAY;QAChB,WAAW;QACX,UAAU;QAEV,IAAI;YACF,sBAAsB;YACtB,UAAU,CAAA,OAAQ,OAAO;YAEzB,MAAM,WAAW,IAAI;YACrB,SAAS,MAAM,CAAC,YAAY;YAC5B,SAAS,MAAM,CAAC,YAAY;YAE5B,UAAU,CAAA,OAAQ,OAAO;YAEzB,MAAM,WAAW,MAAM,MAAM,wCAAwC;gBACnE,QAAQ;gBACR,MAAM;YACR;YAEA,UAAU,CAAA,OAAQ,OAAO,CAAC,yBAAyB,EAAE,SAAS,MAAM,CAAC,EAAE,CAAC;YAExE,IAAI,CAAC,SAAS,EAAE,EAAE;gBAChB,MAAM,IAAI,MAAM,CAAC,oBAAoB,EAAE,SAAS,MAAM,EAAE;YAC1D;YAEA,MAAM,OAAO,MAAM,SAAS,IAAI;YAChC,UAAU,CAAA,OAAQ,OAAO,CAAC,gCAAgC,CAAC;YAC3D,UAAU,CAAA,OAAQ,OAAO,CAAC,MAAM,EAAE,KAAK,SAAS,CAAC,MAAM,MAAM,GAAG,EAAE,CAAC;YAEnE,qBAAqB;YACrB,UAAU,CAAA,OAAQ,OAAO;YACzB,aAAa,OAAO,CAAC,SAAS,KAAK,YAAY;YAE/C,iCAAiC;YACjC,MAAM,aAAa,aAAa,OAAO,CAAC;YACxC,UAAU,CAAA,OAAQ,OAAO,CAAC,qBAAqB,EAAE,aAAa,QAAQ,KAAK,EAAE,CAAC;YAE9E,sBAAsB;YACtB,UAAU,CAAA,OAAQ,OAAO;YAEzB,IAAI,KAAK,IAAI,KAAK,SAAS;gBACzB,UAAU,CAAA,OAAQ,OAAO;gBAEzB,iCAAiC;gBACjC,WAAW;oBACT,UAAU,CAAA,OAAQ,OAAO;oBACzB,OAAO,IAAI,CAAC;gBACd,GAAG;gBAEH,WAAW;oBACT,UAAU,CAAA,OAAQ,OAAO;oBACzB,OAAO,QAAQ,CAAC,IAAI,GAAG;gBACzB,GAAG;YACL;QAEF,EAAE,OAAO,OAAO;YACd,UAAU,CAAA,OAAQ,OAAO,CAAC,OAAO,EAAE,MAAM,OAAO,CAAC,EAAE,CAAC;YACpD,QAAQ,KAAK,CAAC,qBAAqB;QACrC,SAAU;YACR,WAAW;QACb;IACF;IAEA,MAAM,aAAa;QACjB,UAAU;QACV,IAAI;YACF,OAAO,IAAI,CAAC;YACZ,UAAU,CAAA,OAAQ,OAAO;QAC3B,EAAE,OAAO,OAAO;YACd,UAAU,CAAA,OAAQ,OAAO,CAAC,cAAc,EAAE,MAAM,OAAO,CAAC,EAAE,CAAC;QAC7D;IACF;IAEA,MAAM,qBAAqB;QACzB,UAAU;QACV,OAAO,QAAQ,CAAC,IAAI,GAAG;IACzB;IAEA,MAAM,eAAe;QACnB,aAAa,KAAK;QAClB,UAAU;IACZ;IAEA,qBACE,6LAAC;QAAI,OAAO;YAAE,SAAS;YAAQ,YAAY;QAAY;;0BACrD,6LAAC;0BAAG;;;;;;0BAEJ,6LAAC;gBAAI,OAAO;oBAAE,cAAc;gBAAO;;kCACjC,6LAAC;wBACC,SAAS;wBACT,UAAU;wBACV,OAAO;4BACL,SAAS;4BACT,aAAa;4BACb,iBAAiB;4BACjB,OAAO;4BACP,QAAQ;4BACR,cAAc;4BACd,QAAQ,UAAU,gBAAgB;wBACpC;kCAEC,UAAU,eAAe;;;;;;kCAG5B,6LAAC;wBACC,SAAS;wBACT,OAAO;4BACL,SAAS;4BACT,aAAa;4BACb,iBAAiB;4BACjB,OAAO;4BACP,QAAQ;4BACR,cAAc;4BACd,QAAQ;wBACV;kCACD;;;;;;kCAID,6LAAC;wBACC,SAAS;wBACT,OAAO;4BACL,SAAS;4BACT,aAAa;4BACb,iBAAiB;4BACjB,OAAO;4BACP,QAAQ;4BACR,cAAc;4BACd,QAAQ;wBACV;kCACD;;;;;;kCAID,6LAAC;wBACC,SAAS;wBACT,OAAO;4BACL,SAAS;4BACT,iBAAiB;4BACjB,OAAO;4BACP,QAAQ;4BACR,cAAc;4BACd,QAAQ;wBACV;kCACD;;;;;;;;;;;;0BAKH,6LAAC;gBAAI,OAAO;oBACV,iBAAiB;oBACjB,SAAS;oBACT,cAAc;oBACd,YAAY;oBACZ,WAAW;oBACX,UAAU;oBACV,YAAY;oBACZ,QAAQ;gBACV;0BACG,UAAU;;;;;;0BAGb,6LAAC;gBAAI,OAAO;oBAAE,WAAW;gBAAO;;kCAC9B,6LAAC;kCAAG;;;;;;kCACJ,6LAAC;wBAAI,OAAO;4BACV,iBAAiB;4BACjB,SAAS;4BACT,cAAc;4BACd,UAAU;4BACV,YAAY;wBACd;kCACG,uCACC,OAAO,IAAI,CAAC,cAAc,GAAG,CAAC,CAAA,MAC5B,GAAG,IAAI,EAAE,EAAE,aAAa,OAAO,CAAC,MAAM,UAAU,GAAG,IAAI,GAAG,CAAC,EAC3D,IAAI,CAAC,SAAS;;;;;;;;;;;;;;;;;;AAO5B;GA1LwB;;QAGP,qIAAA,CAAA,YAAS;;;KAHF", "debugId": null}}]}
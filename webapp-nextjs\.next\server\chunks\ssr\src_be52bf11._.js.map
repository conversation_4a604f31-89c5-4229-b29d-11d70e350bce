{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/CMS/webapp-nextjs/src/components/ui/card.tsx"], "sourcesContent": ["import * as React from \"react\"\n\nimport { cn } from \"@/lib/utils\"\n\nfunction Card({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card\"\n      className={cn(\n        \"bg-card text-card-foreground flex flex-col gap-6 rounded-xl border py-6 shadow-sm\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction CardHeader({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-header\"\n      className={cn(\n        \"@container/card-header grid auto-rows-min grid-rows-[auto_auto] items-start gap-1.5 px-6 has-data-[slot=card-action]:grid-cols-[1fr_auto] [.border-b]:pb-6\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction CardTitle({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-title\"\n      className={cn(\"leading-none font-semibold\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction CardDescription({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-description\"\n      className={cn(\"text-muted-foreground text-sm\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction CardAction({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-action\"\n      className={cn(\n        \"col-start-2 row-span-2 row-start-1 self-start justify-self-end\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction CardContent({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-content\"\n      className={cn(\"px-6\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction CardFooter({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-footer\"\n      className={cn(\"flex items-center px-6 [.border-t]:pt-6\", className)}\n      {...props}\n    />\n  )\n}\n\nexport {\n  Card,\n  CardHeader,\n  CardFooter,\n  CardTitle,\n  CardAction,\n  CardDescription,\n  CardContent,\n}\n"], "names": [], "mappings": ";;;;;;;;;;AAEA;;;AAEA,SAAS,KAAK,EAAE,SAAS,EAAE,GAAG,OAAoC;IAChE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,qFACA;QAED,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,WAAW,EAAE,SAAS,EAAE,GAAG,OAAoC;IACtE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,8JACA;QAED,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,UAAU,EAAE,SAAS,EAAE,GAAG,OAAoC;IACrE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,8BAA8B;QAC3C,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,gBAAgB,EAAE,SAAS,EAAE,GAAG,OAAoC;IAC3E,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,iCAAiC;QAC9C,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,WAAW,EAAE,SAAS,EAAE,GAAG,OAAoC;IACtE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,kEACA;QAED,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,YAAY,EAAE,SAAS,EAAE,GAAG,OAAoC;IACvE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,QAAQ;QACrB,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,WAAW,EAAE,SAAS,EAAE,GAAG,OAAoC;IACtE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,2CAA2C;QACxD,GAAG,KAAK;;;;;;AAGf", "debugId": null}}, {"offset": {"line": 104, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/CMS/webapp-nextjs/src/components/ui/badge.tsx"], "sourcesContent": ["import * as React from \"react\"\nimport { Slot } from \"@radix-ui/react-slot\"\nimport { cva, type VariantProps } from \"class-variance-authority\"\n\nimport { cn } from \"@/lib/utils\"\n\nconst badgeVariants = cva(\n  \"inline-flex items-center justify-center rounded-md border px-2 py-0.5 text-xs font-medium w-fit whitespace-nowrap shrink-0 [&>svg]:size-3 gap-1 [&>svg]:pointer-events-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive transition-[color,box-shadow] overflow-hidden\",\n  {\n    variants: {\n      variant: {\n        default:\n          \"border-transparent bg-primary text-primary-foreground [a&]:hover:bg-primary/90\",\n        secondary:\n          \"border-transparent bg-secondary text-secondary-foreground [a&]:hover:bg-secondary/90\",\n        destructive:\n          \"border-transparent bg-destructive text-white [a&]:hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60\",\n        outline:\n          \"text-foreground [a&]:hover:bg-accent [a&]:hover:text-accent-foreground\",\n      },\n    },\n    defaultVariants: {\n      variant: \"default\",\n    },\n  }\n)\n\nfunction Badge({\n  className,\n  variant,\n  asChild = false,\n  ...props\n}: React.ComponentProps<\"span\"> &\n  VariantProps<typeof badgeVariants> & { asChild?: boolean }) {\n  const Comp = asChild ? Slot : \"span\"\n\n  return (\n    <Comp\n      data-slot=\"badge\"\n      className={cn(badgeVariants({ variant }), className)}\n      {...props}\n    />\n  )\n}\n\nexport { Badge, badgeVariants }\n"], "names": [], "mappings": ";;;;;AACA;AACA;AAEA;;;;;AAEA,MAAM,gBAAgB,CAAA,GAAA,gKAAA,CAAA,MAAG,AAAD,EACtB,kZACA;IACE,UAAU;QACR,SAAS;YACP,SACE;YACF,WACE;YACF,aACE;YACF,SACE;QACJ;IACF;IACA,iBAAiB;QACf,SAAS;IACX;AACF;AAGF,SAAS,MAAM,EACb,SAAS,EACT,OAAO,EACP,UAAU,KAAK,EACf,GAAG,OAEuD;IAC1D,MAAM,OAAO,UAAU,gKAAA,CAAA,OAAI,GAAG;IAE9B,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,cAAc;YAAE;QAAQ,IAAI;QACzC,GAAG,KAAK;;;;;;AAGf", "debugId": null}}, {"offset": {"line": 150, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/CMS/webapp-nextjs/src/components/ui/input.tsx"], "sourcesContent": ["import * as React from \"react\"\n\nimport { cn } from \"@/lib/utils\"\n\nfunction Input({ className, type, ...props }: React.ComponentProps<\"input\">) {\n  return (\n    <input\n      type={type}\n      data-slot=\"input\"\n      className={cn(\n        \"file:text-foreground placeholder:text-muted-foreground selection:bg-primary selection:text-primary-foreground dark:bg-input/30 border-input flex h-9 w-full min-w-0 rounded-md border bg-transparent px-3 py-1 text-base shadow-xs transition-[color,box-shadow] outline-none file:inline-flex file:h-7 file:border-0 file:bg-transparent file:text-sm file:font-medium disabled:pointer-events-none disabled:cursor-not-allowed disabled:opacity-50 md:text-sm\",\n        \"focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px]\",\n        \"aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nexport { Input }\n"], "names": [], "mappings": ";;;;AAEA;;;AAEA,SAAS,MAAM,EAAE,SAAS,EAAE,IAAI,EAAE,GAAG,OAAsC;IACzE,qBACE,8OAAC;QACC,MAAM;QACN,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,mcACA,iFACA,0GACA;QAED,GAAG,KAAK;;;;;;AAGf", "debugId": null}}, {"offset": {"line": 176, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/CMS/webapp-nextjs/src/components/ui/table.tsx"], "sourcesContent": ["\"use client\"\n\nimport * as React from \"react\"\n\nimport { cn } from \"@/lib/utils\"\n\nfunction Table({ className, ...props }: React.ComponentProps<\"table\">) {\n  return (\n    <div\n      data-slot=\"table-container\"\n      className=\"relative w-full overflow-x-auto\"\n    >\n      <table\n        data-slot=\"table\"\n        className={cn(\"w-full caption-bottom text-sm border-collapse\", className)}\n        {...props}\n      />\n    </div>\n  )\n}\n\nfunction TableHeader({ className, ...props }: React.ComponentProps<\"thead\">) {\n  return (\n    <thead\n      data-slot=\"table-header\"\n      className={cn(\"[&_tr]:border-b\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction TableBody({ className, ...props }: React.ComponentProps<\"tbody\">) {\n  return (\n    <tbody\n      data-slot=\"table-body\"\n      className={cn(\"[&_tr:last-child]:border-0\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction TableFooter({ className, ...props }: React.ComponentProps<\"tfoot\">) {\n  return (\n    <tfoot\n      data-slot=\"table-footer\"\n      className={cn(\n        \"bg-muted/50 border-t font-medium [&>tr]:last:border-b-0\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction TableRow({ className, ...props }: React.ComponentProps<\"tr\">) {\n  return (\n    <tr\n      data-slot=\"table-row\"\n      className={cn(\n        \"hover:bg-muted/50 data-[state=selected]:bg-muted border-b transition-colors\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction TableHead({ className, ...props }: React.ComponentProps<\"th\">) {\n  return (\n    <th\n      data-slot=\"table-head\"\n      className={cn(\n        \"text-foreground h-10 px-2 text-left align-middle font-medium whitespace-nowrap [&:has([role=checkbox])]:pr-0 [&>[role=checkbox]]:translate-y-[2px]\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction TableCell({ className, ...props }: React.ComponentProps<\"td\">) {\n  return (\n    <td\n      data-slot=\"table-cell\"\n      className={cn(\n        \"p-2 align-middle whitespace-nowrap [&:has([role=checkbox])]:pr-0 [&>[role=checkbox]]:translate-y-[2px]\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction TableCaption({\n  className,\n  ...props\n}: React.ComponentProps<\"caption\">) {\n  return (\n    <caption\n      data-slot=\"table-caption\"\n      className={cn(\"text-muted-foreground mt-4 text-sm\", className)}\n      {...props}\n    />\n  )\n}\n\nexport {\n  Table,\n  TableHeader,\n  TableBody,\n  TableFooter,\n  TableHead,\n  TableRow,\n  TableCell,\n  TableCaption,\n}\n"], "names": [], "mappings": ";;;;;;;;;;;AAIA;AAJA;;;AAMA,SAAS,MAAM,EAAE,SAAS,EAAE,GAAG,OAAsC;IACnE,qBACE,8OAAC;QACC,aAAU;QACV,WAAU;kBAEV,cAAA,8OAAC;YACC,aAAU;YACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,iDAAiD;YAC9D,GAAG,KAAK;;;;;;;;;;;AAIjB;AAEA,SAAS,YAAY,EAAE,SAAS,EAAE,GAAG,OAAsC;IACzE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,mBAAmB;QAChC,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,UAAU,EAAE,SAAS,EAAE,GAAG,OAAsC;IACvE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,8BAA8B;QAC3C,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,YAAY,EAAE,SAAS,EAAE,GAAG,OAAsC;IACzE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,2DACA;QAED,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,SAAS,EAAE,SAAS,EAAE,GAAG,OAAmC;IACnE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,+EACA;QAED,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,UAAU,EAAE,SAAS,EAAE,GAAG,OAAmC;IACpE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,sJACA;QAED,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,UAAU,EAAE,SAAS,EAAE,GAAG,OAAmC;IACpE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,0GACA;QAED,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,aAAa,EACpB,SAAS,EACT,GAAG,OAC6B;IAChC,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,sCAAsC;QACnD,GAAG,KAAK;;;;;;AAGf", "debugId": null}}, {"offset": {"line": 294, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/CMS/webapp-nextjs/src/components/ui/alert.tsx"], "sourcesContent": ["import * as React from \"react\"\nimport { cva, type VariantProps } from \"class-variance-authority\"\n\nimport { cn } from \"@/lib/utils\"\n\nconst alertVariants = cva(\n  \"relative w-full rounded-lg border p-4 [&>svg~*]:pl-7 [&>svg+div]:translate-y-[-3px] [&>svg]:absolute [&>svg]:left-4 [&>svg]:top-4 [&>svg]:text-foreground\",\n  {\n    variants: {\n      variant: {\n        default: \"bg-background text-foreground\",\n        destructive:\n          \"border-destructive/50 text-destructive dark:border-destructive [&>svg]:text-destructive\",\n      },\n    },\n    defaultVariants: {\n      variant: \"default\",\n    },\n  }\n)\n\nconst Alert = React.forwardRef<\n  HTMLDivElement,\n  React.HTMLAttributes<HTMLDivElement> & VariantProps<typeof alertVariants>\n>(({ className, variant, ...props }, ref) => (\n  <div\n    ref={ref}\n    role=\"alert\"\n    className={cn(alertVariants({ variant }), className)}\n    {...props}\n  />\n))\nAlert.displayName = \"Alert\"\n\nconst AlertTitle = React.forwardRef<\n  HTMLParagraphElement,\n  React.HTMLAttributes<HTMLHeadingElement>\n>(({ className, ...props }, ref) => (\n  <h5\n    ref={ref}\n    className={cn(\"mb-1 font-medium leading-none tracking-tight\", className)}\n    {...props}\n  />\n))\nAlertTitle.displayName = \"AlertTitle\"\n\nconst AlertDescription = React.forwardRef<\n  HTMLParagraphElement,\n  React.HTMLAttributes<HTMLParagraphElement>\n>(({ className, ...props }, ref) => (\n  <div\n    ref={ref}\n    className={cn(\"text-sm [&_p]:leading-relaxed\", className)}\n    {...props}\n  />\n))\nAlertDescription.displayName = \"AlertDescription\"\n\nexport { Alert, AlertTitle, AlertDescription }\n"], "names": [], "mappings": ";;;;;;AAAA;AACA;AAEA;;;;;AAEA,MAAM,gBAAgB,CAAA,GAAA,gKAAA,CAAA,MAAG,AAAD,EACtB,6JACA;IACE,UAAU;QACR,SAAS;YACP,SAAS;YACT,aACE;QACJ;IACF;IACA,iBAAiB;QACf,SAAS;IACX;AACF;AAGF,MAAM,sBAAQ,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAG3B,CAAC,EAAE,SAAS,EAAE,OAAO,EAAE,GAAG,OAAO,EAAE,oBACnC,8OAAC;QACC,KAAK;QACL,MAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,cAAc;YAAE;QAAQ,IAAI;QACzC,GAAG,KAAK;;;;;;AAGb,MAAM,WAAW,GAAG;AAEpB,MAAM,2BAAa,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAGhC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,gDAAgD;QAC7D,GAAG,KAAK;;;;;;AAGb,WAAW,WAAW,GAAG;AAEzB,MAAM,iCAAmB,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAGtC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,iCAAiC;QAC9C,GAAG,KAAK;;;;;;AAGb,iBAAiB,WAAW,GAAG", "debugId": null}}, {"offset": {"line": 358, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/CMS/webapp-nextjs/src/utils/bobineUtils.ts"], "sourcesContent": ["/**\n * Utility per la gestione degli stati delle bobine e dei cavi\n * Implementa le stesse regole della webapp originale\n */\n\n// Stati del cavo\nexport const CABLE_STATES = {\n  DA_INSTALLARE: 'Da installare',\n  IN_CORSO: 'In corso',\n  INSTALLATO: 'Installato',\n  SPARE: 'SPARE'\n} as const\n\nexport type CableState = typeof CABLE_STATES[keyof typeof CABLE_STATES]\n\n// Stati della bobina\nexport const REEL_STATES = {\n  DISPONIBILE: 'Disponibile',\n  IN_USO: 'In uso',\n  TERMINATA: 'Terminata',\n  OVER: 'Over'\n} as const\n\nexport type ReelState = typeof REEL_STATES[keyof typeof REEL_STATES]\n\n/**\n * Verifica se un cavo è già installato\n * Implementa la stessa logica della webapp originale\n * @param cavo - Oggetto cavo\n * @returns True se il cavo è già installato, false altrimenti\n */\nexport const isCableInstalled = (cavo: any): boolean => {\n  return cavo.stato_installazione === CABLE_STATES.INSTALLATO ||\n         (cavo.metratura_reale && parseFloat(cavo.metratura_reale.toString()) > 0)\n}\n\n/**\n * Verifica se un cavo è in stato SPARE\n * @param cavo - Oggetto cavo\n * @returns True se il cavo è in stato SPARE, false altrimenti\n */\nexport const isCableSpare = (cavo: any): boolean => {\n  return cavo.modificato_manualmente === 3 || cavo.stato_installazione === CABLE_STATES.SPARE\n}\n\n/**\n * Verifica la compatibilità tra cavo e bobina\n * Implementa la stessa logica della webapp originale\n * @param cavo - Oggetto cavo\n * @param bobina - Oggetto bobina\n * @returns True se compatibili, false altrimenti\n */\nexport const isCompatible = (cavo: any, bobina: any): boolean => {\n  return cavo.tipologia === bobina.tipologia &&\n         String(cavo.sezione) === String(bobina.sezione)\n}\n\n/**\n * Determina lo stato di una bobina in base ai metri residui e totali\n * @param metriResidui - Metri residui\n * @param metriTotali - Metri totali\n * @returns Stato della bobina\n */\nexport const determineReelState = (metriResidui: number, metriTotali: number): ReelState => {\n  if (metriResidui < 0) {\n    return REEL_STATES.OVER\n  }\n\n  if (metriResidui === 0) {\n    return REEL_STATES.TERMINATA\n  }\n\n  if (metriResidui < metriTotali) {\n    return REEL_STATES.IN_USO\n  }\n\n  return REEL_STATES.DISPONIBILE\n}\n\n/**\n * Verifica se una bobina può essere modificata in base al suo stato\n * @param statoBobina - Stato della bobina\n * @returns True se la bobina può essere modificata, false altrimenti\n */\nexport const canModifyReel = (statoBobina: string): boolean => {\n  // Una bobina può essere modificata se:\n  // 1. È in stato DISPONIBILE\n  // 2. È in stato IN_USO\n  // 3. Non è in stato TERMINATA o OVER\n  return statoBobina === REEL_STATES.DISPONIBILE || \n         statoBobina === REEL_STATES.IN_USO\n}\n\n/**\n * Ottiene il colore associato a uno stato della bobina per i badge\n * @param stato - Stato della bobina\n * @returns Classi CSS per il colore del badge\n */\nexport const getReelStateColor = (stato: string): string => {\n  switch (stato) {\n    case REEL_STATES.DISPONIBILE:\n      return 'bg-green-100 text-green-800'\n    case REEL_STATES.IN_USO:\n      return 'bg-yellow-100 text-yellow-800'\n    case REEL_STATES.TERMINATA:\n      return 'bg-red-100 text-red-800'\n    case REEL_STATES.OVER:\n      return 'bg-red-500 text-white'\n    default:\n      return 'bg-gray-100 text-gray-800'\n  }\n}\n\n/**\n * Ottiene il colore di sfondo per le righe della tabella in base allo stato\n * @param stato - Stato della bobina\n * @returns Classi CSS per il colore di sfondo\n */\nexport const getReelRowColor = (stato: string): string => {\n  switch (stato) {\n    case REEL_STATES.DISPONIBILE:\n      return 'hover:bg-green-50'\n    case REEL_STATES.IN_USO:\n      return 'hover:bg-yellow-50'\n    case REEL_STATES.TERMINATA:\n      return 'hover:bg-red-50'\n    case REEL_STATES.OVER:\n      return 'hover:bg-red-100'\n    default:\n      return 'hover:bg-gray-50'\n  }\n}\n\n/**\n * Verifica se una bobina è utilizzabile per nuove installazioni\n * @param statoBobina - Stato della bobina\n * @param metriResidui - Metri residui\n * @returns True se la bobina è utilizzabile\n */\nexport const isReelUsable = (statoBobina: string, metriResidui: number): boolean => {\n  return (statoBobina === REEL_STATES.DISPONIBILE || statoBobina === REEL_STATES.IN_USO) &&\n         metriResidui > 0\n}\n\n/**\n * Verifica se una bobina può accettare nuovi cavi senza andare OVER\n * Una bobina OVER non può accettare nessun nuovo cavo\n * @param statoBobina - Stato della bobina\n * @returns True se la bobina può accettare nuovi cavi\n */\nexport const canReelAcceptNewCables = (statoBobina: string): boolean => {\n  // Una bobina OVER o TERMINATA non può accettare nuovi cavi\n  return statoBobina !== REEL_STATES.OVER && statoBobina !== REEL_STATES.TERMINATA\n}\n\n/**\n * Verifica se l'aggiunta di metri causerebbe lo stato OVER\n * @param metriResidui - Metri residui attuali\n * @param metriDaAggiungere - Metri da sottrarre (posare)\n * @returns True se l'operazione causerebbe stato OVER\n */\nexport const wouldCauseOverState = (metriResidui: number, metriDaAggiungere: number): boolean => {\n  return (metriResidui - metriDaAggiungere) < 0\n}\n\n/**\n * Verifica se una bobina può accettare una specifica quantità di metri\n * Implementa la logica OVER corretta: una bobina OVER non può accettare nessun nuovo cavo\n * @param statoBobina - Stato attuale della bobina\n * @param metriResidui - Metri residui attuali\n * @param metriRichiesti - Metri richiesti per il nuovo cavo\n * @returns Object con risultato e messaggio di errore se applicabile\n */\nexport const canReelAcceptMeters = (\n  statoBobina: string,\n  metriResidui: number,\n  metriRichiesti: number\n): { canAccept: boolean; reason?: string } => {\n  // Controllo 1: Bobina già OVER - interdetta da qualsiasi operazione\n  if (statoBobina === REEL_STATES.OVER) {\n    return {\n      canAccept: false,\n      reason: 'La bobina è in stato OVER e non può accettare nuovi cavi'\n    }\n  }\n\n  // Controllo 2: Bobina terminata\n  if (statoBobina === REEL_STATES.TERMINATA) {\n    return {\n      canAccept: false,\n      reason: 'La bobina è terminata e non può accettare nuovi cavi'\n    }\n  }\n\n  // Controllo 3: Metri residui insufficienti (senza force_over)\n  if (metriResidui <= 0) {\n    return {\n      canAccept: false,\n      reason: 'La bobina non ha metri residui disponibili'\n    }\n  }\n\n  // Controllo 4: L'operazione causerebbe OVER\n  if (wouldCauseOverState(metriResidui, metriRichiesti)) {\n    return {\n      canAccept: false,\n      reason: `Metri insufficienti: disponibili ${metriResidui}m, richiesti ${metriRichiesti}m. L'operazione causerebbe stato OVER.`\n    }\n  }\n\n  return { canAccept: true }\n}\n\n/**\n * Valida se una lista di cavi può essere aggiunta a una bobina\n * Implementa la logica OVER: blocca se la bobina è già OVER o se l'operazione causerebbe OVER\n * @param statoBobina - Stato attuale della bobina\n * @param metriResidui - Metri residui attuali\n * @param caviConMetri - Array di oggetti con metri richiesti per ogni cavo\n * @returns Object con risultato e dettagli degli errori\n */\nexport const validateMultipleCablesForReel = (\n  statoBobina: string,\n  metriResidui: number,\n  caviConMetri: Array<{ id: string; metri: number }>\n): {\n  isValid: boolean;\n  errors: Array<{ cavoId: string; error: string }>;\n  totalMetriRichiesti: number;\n  metriResiduiDopo: number;\n} => {\n  const errors: Array<{ cavoId: string; error: string }> = []\n  const totalMetriRichiesti = caviConMetri.reduce((sum, cavo) => sum + cavo.metri, 0)\n  const metriResiduiDopo = metriResidui - totalMetriRichiesti\n\n  // Controllo globale: bobina già OVER\n  if (statoBobina === REEL_STATES.OVER) {\n    caviConMetri.forEach(cavo => {\n      errors.push({\n        cavoId: cavo.id,\n        error: 'La bobina è in stato OVER e non può accettare nuovi cavi'\n      })\n    })\n    return {\n      isValid: false,\n      errors,\n      totalMetriRichiesti,\n      metriResiduiDopo\n    }\n  }\n\n  // Controllo globale: bobina terminata\n  if (statoBobina === REEL_STATES.TERMINATA) {\n    caviConMetri.forEach(cavo => {\n      errors.push({\n        cavoId: cavo.id,\n        error: 'La bobina è terminata e non può accettare nuovi cavi'\n      })\n    })\n    return {\n      isValid: false,\n      errors,\n      totalMetriRichiesti,\n      metriResiduiDopo\n    }\n  }\n\n  // Controllo: l'operazione totale causerebbe OVER\n  if (metriResiduiDopo < 0) {\n    errors.push({\n      cavoId: 'TOTALE',\n      error: `Operazione bloccata: metri totali richiesti (${totalMetriRichiesti}m) superano i metri disponibili (${metriResidui}m). Questo causerebbe stato OVER.`\n    })\n  }\n\n  // Validazione individuale di ogni cavo\n  let metriResiduiSimulati = metriResidui\n  for (const cavo of caviConMetri) {\n    if (cavo.metri <= 0) {\n      errors.push({\n        cavoId: cavo.id,\n        error: 'I metri devono essere maggiori di zero'\n      })\n      continue\n    }\n\n    // Simula l'aggiunta progressiva per verificare quando si raggiunge OVER\n    if (metriResiduiSimulati - cavo.metri < 0 && errors.length === 0) {\n      errors.push({\n        cavoId: cavo.id,\n        error: `Questo cavo causerebbe stato OVER (disponibili: ${metriResiduiSimulati}m, richiesti: ${cavo.metri}m)`\n      })\n    }\n\n    metriResiduiSimulati -= cavo.metri\n  }\n\n  return {\n    isValid: errors.length === 0,\n    errors,\n    totalMetriRichiesti,\n    metriResiduiDopo\n  }\n}\n\n/**\n * Calcola la percentuale di utilizzo di una bobina\n * @param metriResidui - Metri residui\n * @param metriTotali - Metri totali\n * @returns Percentuale di utilizzo (0-100)\n */\nexport const calculateReelUsagePercentage = (metriResidui: number, metriTotali: number): number => {\n  if (metriTotali <= 0) return 0\n  const utilizzo = ((metriTotali - metriResidui) / metriTotali) * 100\n  return Math.max(0, Math.min(100, utilizzo))\n}\n\n/**\n * Formatta i metri per la visualizzazione\n * @param metri - Metri da formattare\n * @returns Stringa formattata con unità\n */\nexport const formatMeters = (metri: number): string => {\n  return `${metri.toFixed(1)}m`\n}\n\n/**\n * Ottiene una descrizione testuale dello stato della bobina\n * @param stato - Stato della bobina\n * @returns Descrizione dello stato\n */\nexport const getReelStateDescription = (stato: string): string => {\n  switch (stato) {\n    case REEL_STATES.DISPONIBILE:\n      return 'Bobina disponibile per nuove installazioni'\n    case REEL_STATES.IN_USO:\n      return 'Bobina parzialmente utilizzata'\n    case REEL_STATES.TERMINATA:\n      return 'Bobina completamente esaurita'\n    case REEL_STATES.OVER:\n      return 'Bobina sovra-utilizzata (metri negativi)'\n    default:\n      return 'Stato non definito'\n  }\n}\n"], "names": [], "mappings": "AAAA;;;CAGC,GAED,iBAAiB;;;;;;;;;;;;;;;;;;;;AACV,MAAM,eAAe;IAC1B,eAAe;IACf,UAAU;IACV,YAAY;IACZ,OAAO;AACT;AAKO,MAAM,cAAc;IACzB,aAAa;IACb,QAAQ;IACR,WAAW;IACX,MAAM;AACR;AAUO,MAAM,mBAAmB,CAAC;IAC/B,OAAO,KAAK,mBAAmB,KAAK,aAAa,UAAU,IACnD,KAAK,eAAe,IAAI,WAAW,KAAK,eAAe,CAAC,QAAQ,MAAM;AAChF;AAOO,MAAM,eAAe,CAAC;IAC3B,OAAO,KAAK,sBAAsB,KAAK,KAAK,KAAK,mBAAmB,KAAK,aAAa,KAAK;AAC7F;AASO,MAAM,eAAe,CAAC,MAAW;IACtC,OAAO,KAAK,SAAS,KAAK,OAAO,SAAS,IACnC,OAAO,KAAK,OAAO,MAAM,OAAO,OAAO,OAAO;AACvD;AAQO,MAAM,qBAAqB,CAAC,cAAsB;IACvD,IAAI,eAAe,GAAG;QACpB,OAAO,YAAY,IAAI;IACzB;IAEA,IAAI,iBAAiB,GAAG;QACtB,OAAO,YAAY,SAAS;IAC9B;IAEA,IAAI,eAAe,aAAa;QAC9B,OAAO,YAAY,MAAM;IAC3B;IAEA,OAAO,YAAY,WAAW;AAChC;AAOO,MAAM,gBAAgB,CAAC;IAC5B,uCAAuC;IACvC,4BAA4B;IAC5B,uBAAuB;IACvB,qCAAqC;IACrC,OAAO,gBAAgB,YAAY,WAAW,IACvC,gBAAgB,YAAY,MAAM;AAC3C;AAOO,MAAM,oBAAoB,CAAC;IAChC,OAAQ;QACN,KAAK,YAAY,WAAW;YAC1B,OAAO;QACT,KAAK,YAAY,MAAM;YACrB,OAAO;QACT,KAAK,YAAY,SAAS;YACxB,OAAO;QACT,KAAK,YAAY,IAAI;YACnB,OAAO;QACT;YACE,OAAO;IACX;AACF;AAOO,MAAM,kBAAkB,CAAC;IAC9B,OAAQ;QACN,KAAK,YAAY,WAAW;YAC1B,OAAO;QACT,KAAK,YAAY,MAAM;YACrB,OAAO;QACT,KAAK,YAAY,SAAS;YACxB,OAAO;QACT,KAAK,YAAY,IAAI;YACnB,OAAO;QACT;YACE,OAAO;IACX;AACF;AAQO,MAAM,eAAe,CAAC,aAAqB;IAChD,OAAO,CAAC,gBAAgB,YAAY,WAAW,IAAI,gBAAgB,YAAY,MAAM,KAC9E,eAAe;AACxB;AAQO,MAAM,yBAAyB,CAAC;IACrC,2DAA2D;IAC3D,OAAO,gBAAgB,YAAY,IAAI,IAAI,gBAAgB,YAAY,SAAS;AAClF;AAQO,MAAM,sBAAsB,CAAC,cAAsB;IACxD,OAAO,AAAC,eAAe,oBAAqB;AAC9C;AAUO,MAAM,sBAAsB,CACjC,aACA,cACA;IAEA,oEAAoE;IACpE,IAAI,gBAAgB,YAAY,IAAI,EAAE;QACpC,OAAO;YACL,WAAW;YACX,QAAQ;QACV;IACF;IAEA,gCAAgC;IAChC,IAAI,gBAAgB,YAAY,SAAS,EAAE;QACzC,OAAO;YACL,WAAW;YACX,QAAQ;QACV;IACF;IAEA,8DAA8D;IAC9D,IAAI,gBAAgB,GAAG;QACrB,OAAO;YACL,WAAW;YACX,QAAQ;QACV;IACF;IAEA,4CAA4C;IAC5C,IAAI,oBAAoB,cAAc,iBAAiB;QACrD,OAAO;YACL,WAAW;YACX,QAAQ,CAAC,iCAAiC,EAAE,aAAa,aAAa,EAAE,eAAe,sCAAsC,CAAC;QAChI;IACF;IAEA,OAAO;QAAE,WAAW;IAAK;AAC3B;AAUO,MAAM,gCAAgC,CAC3C,aACA,cACA;IAOA,MAAM,SAAmD,EAAE;IAC3D,MAAM,sBAAsB,aAAa,MAAM,CAAC,CAAC,KAAK,OAAS,MAAM,KAAK,KAAK,EAAE;IACjF,MAAM,mBAAmB,eAAe;IAExC,qCAAqC;IACrC,IAAI,gBAAgB,YAAY,IAAI,EAAE;QACpC,aAAa,OAAO,CAAC,CAAA;YACnB,OAAO,IAAI,CAAC;gBACV,QAAQ,KAAK,EAAE;gBACf,OAAO;YACT;QACF;QACA,OAAO;YACL,SAAS;YACT;YACA;YACA;QACF;IACF;IAEA,sCAAsC;IACtC,IAAI,gBAAgB,YAAY,SAAS,EAAE;QACzC,aAAa,OAAO,CAAC,CAAA;YACnB,OAAO,IAAI,CAAC;gBACV,QAAQ,KAAK,EAAE;gBACf,OAAO;YACT;QACF;QACA,OAAO;YACL,SAAS;YACT;YACA;YACA;QACF;IACF;IAEA,iDAAiD;IACjD,IAAI,mBAAmB,GAAG;QACxB,OAAO,IAAI,CAAC;YACV,QAAQ;YACR,OAAO,CAAC,6CAA6C,EAAE,oBAAoB,iCAAiC,EAAE,aAAa,iCAAiC,CAAC;QAC/J;IACF;IAEA,uCAAuC;IACvC,IAAI,uBAAuB;IAC3B,KAAK,MAAM,QAAQ,aAAc;QAC/B,IAAI,KAAK,KAAK,IAAI,GAAG;YACnB,OAAO,IAAI,CAAC;gBACV,QAAQ,KAAK,EAAE;gBACf,OAAO;YACT;YACA;QACF;QAEA,wEAAwE;QACxE,IAAI,uBAAuB,KAAK,KAAK,GAAG,KAAK,OAAO,MAAM,KAAK,GAAG;YAChE,OAAO,IAAI,CAAC;gBACV,QAAQ,KAAK,EAAE;gBACf,OAAO,CAAC,gDAAgD,EAAE,qBAAqB,cAAc,EAAE,KAAK,KAAK,CAAC,EAAE,CAAC;YAC/G;QACF;QAEA,wBAAwB,KAAK,KAAK;IACpC;IAEA,OAAO;QACL,SAAS,OAAO,MAAM,KAAK;QAC3B;QACA;QACA;IACF;AACF;AAQO,MAAM,+BAA+B,CAAC,cAAsB;IACjE,IAAI,eAAe,GAAG,OAAO;IAC7B,MAAM,WAAW,AAAC,CAAC,cAAc,YAAY,IAAI,cAAe;IAChE,OAAO,KAAK,GAAG,CAAC,GAAG,KAAK,GAAG,CAAC,KAAK;AACnC;AAOO,MAAM,eAAe,CAAC;IAC3B,OAAO,GAAG,MAAM,OAAO,CAAC,GAAG,CAAC,CAAC;AAC/B;AAOO,MAAM,0BAA0B,CAAC;IACtC,OAAQ;QACN,KAAK,YAAY,WAAW;YAC1B,OAAO;QACT,KAAK,YAAY,MAAM;YACrB,OAAO;QACT,KAAK,YAAY,SAAS;YACxB,OAAO;QACT,KAAK,YAAY,IAAI;YACnB,OAAO;QACT;YACE,OAAO;IACX;AACF", "debugId": null}}, {"offset": {"line": 587, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/CMS/webapp-nextjs/src/components/ui/dialog.tsx"], "sourcesContent": ["\"use client\"\n\nimport * as React from \"react\"\nimport * as DialogPrimitive from \"@radix-ui/react-dialog\"\nimport { XIcon } from \"lucide-react\"\n\nimport { cn } from \"@/lib/utils\"\n\nfunction Dialog({\n  ...props\n}: React.ComponentProps<typeof DialogPrimitive.Root>) {\n  return <DialogPrimitive.Root data-slot=\"dialog\" {...props} />\n}\n\nfunction DialogTrigger({\n  ...props\n}: React.ComponentProps<typeof DialogPrimitive.Trigger>) {\n  return <DialogPrimitive.Trigger data-slot=\"dialog-trigger\" {...props} />\n}\n\nfunction DialogPortal({\n  ...props\n}: React.ComponentProps<typeof DialogPrimitive.Portal>) {\n  return <DialogPrimitive.Portal data-slot=\"dialog-portal\" {...props} />\n}\n\nfunction DialogClose({\n  ...props\n}: React.ComponentProps<typeof DialogPrimitive.Close>) {\n  return <DialogPrimitive.Close data-slot=\"dialog-close\" {...props} />\n}\n\nfunction DialogOverlay({\n  className,\n  ...props\n}: React.ComponentProps<typeof DialogPrimitive.Overlay>) {\n  return (\n    <DialogPrimitive.Overlay\n      data-slot=\"dialog-overlay\"\n      className={cn(\n        \"data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 fixed inset-0 z-50 bg-black/50\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction DialogContent({\n  className,\n  children,\n  showCloseButton = true,\n  ...props\n}: React.ComponentProps<typeof DialogPrimitive.Content> & {\n  showCloseButton?: boolean\n}) {\n  return (\n    <DialogPortal data-slot=\"dialog-portal\">\n      <DialogOverlay />\n      <DialogPrimitive.Content\n        data-slot=\"dialog-content\"\n        className={cn(\n          \"bg-background data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 fixed top-[50%] left-[50%] z-50 grid w-full max-w-[calc(100%-2rem)] translate-x-[-50%] translate-y-[-50%] gap-4 rounded-lg border p-6 shadow-lg duration-200 sm:max-w-lg\",\n          className\n        )}\n        {...props}\n      >\n        {children}\n        {showCloseButton && (\n          <DialogPrimitive.Close\n            data-slot=\"dialog-close\"\n            className=\"ring-offset-background focus:ring-ring data-[state=open]:bg-accent data-[state=open]:text-muted-foreground absolute top-4 right-4 rounded-xs opacity-70 transition-opacity hover:opacity-100 focus:ring-2 focus:ring-offset-2 focus:outline-hidden disabled:pointer-events-none [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4\"\n          >\n            <XIcon />\n            <span className=\"sr-only\">Close</span>\n          </DialogPrimitive.Close>\n        )}\n      </DialogPrimitive.Content>\n    </DialogPortal>\n  )\n}\n\nfunction DialogHeader({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"dialog-header\"\n      className={cn(\"flex flex-col gap-2 text-center sm:text-left\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction DialogFooter({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"dialog-footer\"\n      className={cn(\n        \"flex flex-col-reverse gap-2 sm:flex-row sm:justify-end\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction DialogTitle({\n  className,\n  ...props\n}: React.ComponentProps<typeof DialogPrimitive.Title>) {\n  return (\n    <DialogPrimitive.Title\n      data-slot=\"dialog-title\"\n      className={cn(\"text-lg leading-none font-semibold\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction DialogDescription({\n  className,\n  ...props\n}: React.ComponentProps<typeof DialogPrimitive.Description>) {\n  return (\n    <DialogPrimitive.Description\n      data-slot=\"dialog-description\"\n      className={cn(\"text-muted-foreground text-sm\", className)}\n      {...props}\n    />\n  )\n}\n\nexport {\n  Dialog,\n  DialogClose,\n  DialogContent,\n  DialogDescription,\n  DialogFooter,\n  DialogHeader,\n  DialogOverlay,\n  DialogPortal,\n  DialogTitle,\n  DialogTrigger,\n}\n"], "names": [], "mappings": ";;;;;;;;;;;;;AAGA;AACA;AAEA;AANA;;;;;AAQA,SAAS,OAAO,EACd,GAAG,OAC+C;IAClD,qBAAO,8OAAC,kKAAA,CAAA,OAAoB;QAAC,aAAU;QAAU,GAAG,KAAK;;;;;;AAC3D;AAEA,SAAS,cAAc,EACrB,GAAG,OACkD;IACrD,qBAAO,8OAAC,kKAAA,CAAA,UAAuB;QAAC,aAAU;QAAkB,GAAG,KAAK;;;;;;AACtE;AAEA,SAAS,aAAa,EACpB,GAAG,OACiD;IACpD,qBAAO,8OAAC,kKAAA,CAAA,SAAsB;QAAC,aAAU;QAAiB,GAAG,KAAK;;;;;;AACpE;AAEA,SAAS,YAAY,EACnB,GAAG,OACgD;IACnD,qBAAO,8OAAC,kKAAA,CAAA,QAAqB;QAAC,aAAU;QAAgB,GAAG,KAAK;;;;;;AAClE;AAEA,SAAS,cAAc,EACrB,SAAS,EACT,GAAG,OACkD;IACrD,qBACE,8OAAC,kKAAA,CAAA,UAAuB;QACtB,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,0JACA;QAED,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,cAAc,EACrB,SAAS,EACT,QAAQ,EACR,kBAAkB,IAAI,EACtB,GAAG,OAGJ;IACC,qBACE,8OAAC;QAAa,aAAU;;0BACtB,8OAAC;;;;;0BACD,8OAAC,kKAAA,CAAA,UAAuB;gBACtB,aAAU;gBACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,+WACA;gBAED,GAAG,KAAK;;oBAER;oBACA,iCACC,8OAAC,kKAAA,CAAA,QAAqB;wBACpB,aAAU;wBACV,WAAU;;0CAEV,8OAAC,gMAAA,CAAA,QAAK;;;;;0CACN,8OAAC;gCAAK,WAAU;0CAAU;;;;;;;;;;;;;;;;;;;;;;;;AAMtC;AAEA,SAAS,aAAa,EAAE,SAAS,EAAE,GAAG,OAAoC;IACxE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,gDAAgD;QAC7D,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,aAAa,EAAE,SAAS,EAAE,GAAG,OAAoC;IACxE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,0DACA;QAED,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,YAAY,EACnB,SAAS,EACT,GAAG,OACgD;IACnD,qBACE,8OAAC,kKAAA,CAAA,QAAqB;QACpB,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,sCAAsC;QACnD,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,kBAAkB,EACzB,SAAS,EACT,GAAG,OACsD;IACzD,qBACE,8OAAC,kKAAA,CAAA,cAA2B;QAC1B,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,iCAAiC;QAC9C,GAAG,KAAK;;;;;;AAGf", "debugId": null}}, {"offset": {"line": 761, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/CMS/webapp-nextjs/src/components/ui/label.tsx"], "sourcesContent": ["\"use client\"\n\nimport * as React from \"react\"\nimport * as LabelPrimitive from \"@radix-ui/react-label\"\n\nimport { cn } from \"@/lib/utils\"\n\nfunction Label({\n  className,\n  ...props\n}: React.ComponentProps<typeof LabelPrimitive.Root>) {\n  return (\n    <LabelPrimitive.Root\n      data-slot=\"label\"\n      className={cn(\n        \"flex items-center gap-2 text-sm leading-none font-medium select-none group-data-[disabled=true]:pointer-events-none group-data-[disabled=true]:opacity-50 peer-disabled:cursor-not-allowed peer-disabled:opacity-50\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nexport { Label }\n"], "names": [], "mappings": ";;;;AAGA;AAEA;AALA;;;;AAOA,SAAS,MAAM,EACb,SAAS,EACT,GAAG,OAC8C;IACjD,qBACE,8OAAC,iKAAA,CAAA,OAAmB;QAClB,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,uNACA;QAED,GAAG,KAAK;;;;;;AAGf", "debugId": null}}, {"offset": {"line": 789, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/CMS/webapp-nextjs/src/components/bobine/CreaBobinaDialog.tsx"], "sourcesContent": ["'use client'\n\nimport { useState, useEffect } from 'react'\nimport {\n  <PERSON><PERSON>,\n  DialogContent,\n  DialogDescription,\n  DialogFooter,\n  DialogHeader,\n  DialogTitle,\n} from '@/components/ui/dialog'\nimport { Button } from '@/components/ui/button'\nimport { Input } from '@/components/ui/input'\nimport { Label } from '@/components/ui/label'\nimport {\n  Select,\n  SelectContent,\n  SelectItem,\n  SelectTrigger,\n  SelectValue,\n} from '@/components/ui/select'\nimport { Alert, AlertDescription } from '@/components/ui/alert'\nimport { Loader2, AlertCircle, Plus } from 'lucide-react'\nimport { parcoCaviApi } from '@/lib/api'\n\ninterface CreaBobinaDialogProps {\n  open: boolean\n  onClose: () => void\n  cantiereId: number\n  onSuccess: (message: string) => void\n  onError: (message: string) => void\n}\n\ninterface FormData {\n  numero_bobina: string\n  utility: string\n  tipologia: string\n  n_conduttori: string\n  sezione: string\n  metri_totali: string\n  ubicazione_bobina: string\n  fornitore: string\n  n_DDT: string\n  data_DDT: string\n  configurazione: string\n}\n\nconst initialFormData: FormData = {\n  numero_bobina: '',\n  utility: '',\n  tipologia: '',\n  n_conduttori: '0',\n  sezione: '',\n  metri_totali: '',\n  ubicazione_bobina: 'TBD',\n  fornitore: 'TBD',\n  n_DDT: 'TBD',\n  data_DDT: '',\n  configurazione: 's'\n}\n\nexport default function CreaBobinaDialog({\n  open,\n  onClose,\n  cantiereId,\n  onSuccess,\n  onError\n}: CreaBobinaDialogProps) {\n  const [formData, setFormData] = useState<FormData>(initialFormData)\n  const [loading, setLoading] = useState(false)\n  const [error, setError] = useState('')\n  const [nextBobinaNumber, setNextBobinaNumber] = useState('1')\n  const [loadingConfig, setLoadingConfig] = useState(false)\n  const [isFirstInsertion, setIsFirstInsertion] = useState(true)\n  const [configurazioneFixed, setConfigurazioneFixed] = useState('')\n  const [showConfigSelection, setShowConfigSelection] = useState(false)\n\n  // Reset form quando si apre il dialog\n  useEffect(() => {\n    if (open && cantiereId && cantiereId > 0) {\n      setFormData(initialFormData)\n      setError('')\n      checkFirstInsertion()\n    }\n  }, [open, cantiereId])\n\n  // Verifica se è il primo inserimento e carica la configurazione\n  const checkFirstInsertion = async () => {\n    if (!cantiereId || cantiereId <= 0) return\n\n    try {\n      setLoadingConfig(true)\n      const response = await parcoCaviApi.isFirstBobinaInsertion(cantiereId)\n\n      setIsFirstInsertion(response.is_first_insertion)\n\n      if (response.is_first_insertion) {\n        // Primo inserimento: mostra selezione configurazione\n        setShowConfigSelection(true)\n        setConfigurazioneFixed('')\n      } else {\n        // Non è il primo inserimento: usa configurazione esistente\n        setConfigurazioneFixed(response.configurazione)\n        setFormData(prev => ({\n          ...prev,\n          configurazione: response.configurazione\n        }))\n        setShowConfigSelection(false)\n\n        // Carica il prossimo numero se configurazione è standard\n        if (response.configurazione === 's') {\n          await loadNextBobinaNumber()\n        }\n      }\n    } catch (error) {\n      // In caso di errore, assume primo inserimento\n      setIsFirstInsertion(true)\n      setShowConfigSelection(true)\n    } finally {\n      setLoadingConfig(false)\n    }\n  }\n\n  // Funzione per caricare il prossimo numero bobina (numerazione globale)\n  const loadNextBobinaNumber = async () => {\n    if (!cantiereId || cantiereId <= 0) return\n\n    try {\n      // Per la numerazione globale, recupera TUTTE le bobine di TUTTI i cantieri\n      // e trova il numero massimo globale\n      const bobine = await parcoCaviApi.getBobine(cantiereId)\n\n      if (bobine && bobine.length > 0) {\n        // Filtra solo le bobine con numero_bobina numerico\n        const numericBobine = bobine.filter(b =>\n          b.numero_bobina && /^\\d+$/.test(b.numero_bobina)\n        )\n\n        if (numericBobine.length > 0) {\n          // Trova il numero massimo tra le bobine esistenti\n          const maxNumber = Math.max(...numericBobine.map(b => parseInt(b.numero_bobina, 10)))\n          const nextNumber = String(maxNumber + 1)\n          setNextBobinaNumber(nextNumber)\n\n          // Se la configurazione è standard, imposta automaticamente il numero\n          setFormData(prev => ({\n            ...prev,\n            numero_bobina: nextNumber\n          }))\n        } else {\n          setNextBobinaNumber('1')\n          setFormData(prev => ({\n            ...prev,\n            numero_bobina: '1'\n          }))\n        }\n      } else {\n        setNextBobinaNumber('1')\n        setFormData(prev => ({\n          ...prev,\n          numero_bobina: '1'\n        }))\n      }\n    } catch (error) {\n      setNextBobinaNumber('1')\n      setFormData(prev => ({\n        ...prev,\n        numero_bobina: '1'\n      }))\n    }\n  }\n\n  const handleInputChange = (field: keyof FormData, value: string) => {\n    setFormData(prev => ({\n      ...prev,\n      [field]: value\n    }))\n    setError('')\n  }\n\n  // Gestisce la selezione della configurazione (solo al primo inserimento)\n  const handleConfigSelection = async (config: string) => {\n    setConfigurazioneFixed(config)\n    setFormData(prev => ({\n      ...prev,\n      configurazione: config\n    }))\n    setShowConfigSelection(false)\n\n    // Se configurazione standard, carica il prossimo numero\n    if (config === 's') {\n      await loadNextBobinaNumber()\n    } else {\n      // Configurazione manuale: svuota il campo\n      setFormData(prev => ({\n        ...prev,\n        numero_bobina: ''\n      }))\n    }\n  }\n\n  const validateForm = (): string | null => {\n    if (!formData.numero_bobina.trim()) {\n      return 'Il numero bobina è obbligatorio'\n    }\n\n    // Validazione per configurazione manuale\n    if (formData.configurazione === 'm') {\n      const numeroInput = formData.numero_bobina.trim()\n\n      // Verifica caratteri non consentiti\n      if (/[\\s\\\\/:*?\"<>|]/.test(numeroInput)) {\n        return 'Il numero bobina non può contenere spazi o caratteri speciali come \\\\ / : * ? \" < > |'\n      }\n    }\n\n    if (!formData.utility.trim()) {\n      return 'La utility è obbligatoria'\n    }\n    if (!formData.tipologia.trim()) {\n      return 'La tipologia è obbligatoria'\n    }\n    if (!formData.sezione.trim()) {\n      return 'La formazione è obbligatoria'\n    }\n    if (!formData.metri_totali.trim()) {\n      return 'I metri totali sono obbligatori'\n    }\n\n    const metri = parseFloat(formData.metri_totali)\n    if (isNaN(metri) || metri <= 0) {\n      return 'I metri totali devono essere un numero positivo'\n    }\n\n    return null\n  }\n\n  const handleSave = async () => {\n    const validationError = validateForm()\n    if (validationError) {\n      setError(validationError)\n      return\n    }\n\n    try {\n      setLoading(true)\n      setError('')\n\n      if (!cantiereId || cantiereId <= 0) {\n        throw new Error('Cantiere non selezionato')\n      }\n\n      // Prepara i dati per l'API\n      const bobinaData = {\n        numero_bobina: formData.numero_bobina.trim(),\n        utility: formData.utility.trim().toUpperCase(),\n        tipologia: formData.tipologia.trim().toUpperCase(),\n        n_conduttori: '0', // Campo disponibile, sempre impostato a '0'\n        sezione: formData.sezione.trim(),\n        metri_totali: parseFloat(formData.metri_totali),\n        ubicazione_bobina: formData.ubicazione_bobina.trim() || 'TBD',\n        fornitore: formData.fornitore.trim() || 'TBD',\n        n_DDT: formData.n_DDT.trim() || 'TBD',\n        data_DDT: formData.data_DDT || null,\n        configurazione: formData.configurazione\n      }\n\n      await parcoCaviApi.createBobina(cantiereId, bobinaData)\n\n      onSuccess(`Bobina ${formData.numero_bobina} creata con successo`)\n      onClose()\n    } catch (error: any) {\n      const errorDetail = error.response?.data?.detail || error.message || 'Errore durante la creazione della bobina'\n\n      // Gestione specifica per ID bobina duplicato\n      if (errorDetail.includes('già presente nel cantiere') || errorDetail.includes('già esistente')) {\n        setError(`⚠️ Bobina con numero ${formData.numero_bobina} già esistente. Scegli un numero diverso.`)\n      } else {\n        onError(errorDetail)\n      }\n    } finally {\n      setLoading(false)\n    }\n  }\n\n  const handleClose = () => {\n    if (!loading) {\n      setFormData(initialFormData)\n      setError('')\n      onClose()\n    }\n  }\n\n  return (\n    <Dialog open={open} onOpenChange={handleClose}>\n      <DialogContent\n        className=\"max-h-[95vh] overflow-y-auto\"\n        style={{\n          width: '1000px !important',\n          maxWidth: '95vw !important',\n          minWidth: '1000px'\n        }}\n      >\n        <DialogHeader>\n          <DialogTitle className=\"flex items-center gap-2\">\n            <Plus className=\"h-5 w-5\" />\n            Crea Nuova Bobina\n          </DialogTitle>\n          <DialogDescription>\n          </DialogDescription>\n        </DialogHeader>\n\n        <div className=\"grid gap-4 py-4\">\n          {/* Caricamento configurazione */}\n          {loadingConfig && (\n            <div className=\"flex items-center justify-center py-4\">\n              <Loader2 className=\"h-6 w-6 animate-spin mr-2\" />\n              <span>Verifica configurazione...</span>\n            </div>\n          )}\n\n          {/* Selezione configurazione (solo primo inserimento) */}\n          {showConfigSelection && !loadingConfig && (\n            <div className=\"border rounded-lg p-4 bg-blue-50\">\n              <h4 className=\"font-medium mb-3\">Seleziona configurazione per questo cantiere</h4>\n              <p className=\"text-sm text-gray-600 mb-4\">\n                Questa scelta determinerà come verranno numerati tutti i futuri inserimenti di bobine in questo cantiere.\n              </p>\n              <div className=\"grid grid-cols-1 gap-3\">\n                <Button\n                  variant=\"outline\"\n                  className=\"justify-start h-auto p-4\"\n                  onClick={() => handleConfigSelection('s')}\n                >\n                  <div className=\"text-left\">\n                    <div className=\"font-medium\">Standard (s) - Numerazione automatica</div>\n                    <div className=\"text-sm text-gray-600\">I numeri bobina vengono generati automaticamente: 1, 2, 3...</div>\n                  </div>\n                </Button>\n                <Button\n                  variant=\"outline\"\n                  className=\"justify-start h-auto p-4\"\n                  onClick={() => handleConfigSelection('m')}\n                >\n                  <div className=\"text-left\">\n                    <div className=\"font-medium\">Manuale (m) - Inserimento manuale</div>\n                    <div className=\"text-sm text-gray-600\">Puoi inserire numeri personalizzati: A123, TEST01, ecc.</div>\n                  </div>\n                </Button>\n              </div>\n            </div>\n          )}\n\n          {/* Layout a due colonne per i campi */}\n          <div className=\"grid grid-cols-2 gap-6\">\n            {/* Colonna sinistra */}\n            <div className=\"space-y-4\">\n              {/* Numero Bobina */}\n              {!showConfigSelection && !loadingConfig && (\n                <div className=\"grid grid-cols-4 items-center gap-4\">\n                  <Label htmlFor=\"numero_bobina\" className=\"text-right\">\n                    Bobina *\n                  </Label>\n                  <Input\n                    id=\"numero_bobina\"\n                    value={formData.numero_bobina}\n                    onChange={(e) => handleInputChange('numero_bobina', e.target.value)}\n                    placeholder={formData.configurazione === 's' ? 'Generato automaticamente' : 'Es: A123, TEST01'}\n                    disabled={loading || formData.configurazione === 's'}\n                    className={`col-span-2 ${formData.configurazione === 's' ? 'bg-gray-50' : ''}`}\n                  />\n                </div>\n              )}\n\n              {/* Utility */}\n              <div className=\"grid grid-cols-4 items-center gap-4\">\n                <Label htmlFor=\"utility\" className=\"text-right\">\n                  Utility *\n                </Label>\n                <Input\n                  id=\"utility\"\n                  value={formData.utility}\n                  onChange={(e) => handleInputChange('utility', e.target.value)}\n                  className=\"col-span-2\"\n                  placeholder=\"Es: ENEL, TIM, OPEN FIBER\"\n                  disabled={loading}\n                />\n              </div>\n\n              {/* Tipologia */}\n              <div className=\"grid grid-cols-4 items-center gap-4\">\n                <Label htmlFor=\"tipologia\" className=\"text-right\">\n                  Tipologia *\n                </Label>\n                <Input\n                  id=\"tipologia\"\n                  value={formData.tipologia}\n                  onChange={(e) => handleInputChange('tipologia', e.target.value)}\n                  className=\"col-span-2\"\n                  placeholder=\"Es: FO, RAME\"\n                  disabled={loading}\n                />\n              </div>\n\n              {/* Formazione */}\n              <div className=\"grid grid-cols-4 items-center gap-4\">\n                <Label htmlFor=\"sezione\" className=\"text-right\">\n                  Formazione *\n                </Label>\n                <Input\n                  id=\"sezione\"\n                  value={formData.sezione}\n                  onChange={(e) => handleInputChange('sezione', e.target.value)}\n                  className=\"col-span-2\"\n                  placeholder=\"Es: 9/125, 50/125, 1.5\"\n                  disabled={loading}\n                />\n              </div>\n\n              {/* Metri Totali */}\n              <div className=\"grid grid-cols-4 items-center gap-4\">\n                <Label htmlFor=\"metri_totali\" className=\"text-right\">\n                  Metri Totali *\n                </Label>\n                <Input\n                  id=\"metri_totali\"\n                  type=\"number\"\n                  step=\"0.1\"\n                  min=\"0\"\n                  value={formData.metri_totali}\n                  onChange={(e) => handleInputChange('metri_totali', e.target.value)}\n                  className=\"col-span-2\"\n                  placeholder=\"Es: 1000\"\n                  disabled={loading}\n                />\n              </div>\n            </div>\n\n            {/* Colonna destra */}\n            <div className=\"space-y-4\">\n              {/* Ubicazione */}\n              <div className=\"grid grid-cols-4 items-center gap-4\">\n                <Label htmlFor=\"ubicazione_bobina\" className=\"text-right\">\n                  Ubicazione\n                </Label>\n                <Input\n                  id=\"ubicazione_bobina\"\n                  value={formData.ubicazione_bobina}\n                  onChange={(e) => handleInputChange('ubicazione_bobina', e.target.value)}\n                  className=\"col-span-2\"\n                  placeholder=\"Es: Magazzino A, Cantiere\"\n                  disabled={loading}\n                />\n              </div>\n\n              {/* Fornitore */}\n              <div className=\"grid grid-cols-4 items-center gap-4\">\n                <Label htmlFor=\"fornitore\" className=\"text-right\">\n                  Fornitore\n                </Label>\n                <Input\n                  id=\"fornitore\"\n                  value={formData.fornitore}\n                  onChange={(e) => handleInputChange('fornitore', e.target.value)}\n                  className=\"col-span-2\"\n                  placeholder=\"Es: Prysmian, Nexans\"\n                  disabled={loading}\n                />\n              </div>\n\n              {/* Numero DDT */}\n              <div className=\"grid grid-cols-4 items-center gap-4\">\n                <Label htmlFor=\"n_DDT\" className=\"text-right\">\n                  N° DDT\n                </Label>\n                <Input\n                  id=\"n_DDT\"\n                  value={formData.n_DDT}\n                  onChange={(e) => handleInputChange('n_DDT', e.target.value)}\n                  className=\"col-span-2\"\n                  placeholder=\"Es: DDT001\"\n                  disabled={loading}\n                />\n              </div>\n\n              {/* Data DDT */}\n              <div className=\"grid grid-cols-4 items-center gap-4\">\n                <Label htmlFor=\"data_DDT\" className=\"text-right\">\n                  Data DDT\n                </Label>\n                <Input\n                  id=\"data_DDT\"\n                  type=\"date\"\n                  value={formData.data_DDT}\n                  onChange={(e) => handleInputChange('data_DDT', e.target.value)}\n                  className=\"col-span-2\"\n                  disabled={loading}\n                />\n              </div>\n            </div>\n          </div>\n\n          {/* Errori */}\n          {error && (\n            <Alert variant=\"destructive\">\n              <AlertCircle className=\"h-4 w-4\" />\n              <AlertDescription>{error}</AlertDescription>\n            </Alert>\n          )}\n        </div>\n\n        <DialogFooter>\n          <Button variant=\"outline\" onClick={handleClose} disabled={loading || showConfigSelection}>\n            Annulla\n          </Button>\n          <Button\n            onClick={handleSave}\n            disabled={loading || showConfigSelection || loadingConfig}\n          >\n            {loading && <Loader2 className=\"mr-2 h-4 w-4 animate-spin\" />}\n            {loading ? 'Creando...' : 'Crea Bobina'}\n          </Button>\n        </DialogFooter>\n      </DialogContent>\n    </Dialog>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAQA;AACA;AACA;AAQA;AACA;AAAA;AAAA;AACA;AAvBA;;;;;;;;;;AA+CA,MAAM,kBAA4B;IAChC,eAAe;IACf,SAAS;IACT,WAAW;IACX,cAAc;IACd,SAAS;IACT,cAAc;IACd,mBAAmB;IACnB,WAAW;IACX,OAAO;IACP,UAAU;IACV,gBAAgB;AAClB;AAEe,SAAS,iBAAiB,EACvC,IAAI,EACJ,OAAO,EACP,UAAU,EACV,SAAS,EACT,OAAO,EACe;IACtB,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAY;IACnD,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACvC,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACnC,MAAM,CAAC,kBAAkB,oBAAoB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACzD,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACnD,MAAM,CAAC,kBAAkB,oBAAoB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACzD,MAAM,CAAC,qBAAqB,uBAAuB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC/D,MAAM,CAAC,qBAAqB,uBAAuB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAE/D,sCAAsC;IACtC,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,IAAI,QAAQ,cAAc,aAAa,GAAG;YACxC,YAAY;YACZ,SAAS;YACT;QACF;IACF,GAAG;QAAC;QAAM;KAAW;IAErB,gEAAgE;IAChE,MAAM,sBAAsB;QAC1B,IAAI,CAAC,cAAc,cAAc,GAAG;QAEpC,IAAI;YACF,iBAAiB;YACjB,MAAM,WAAW,MAAM,iHAAA,CAAA,eAAY,CAAC,sBAAsB,CAAC;YAE3D,oBAAoB,SAAS,kBAAkB;YAE/C,IAAI,SAAS,kBAAkB,EAAE;gBAC/B,qDAAqD;gBACrD,uBAAuB;gBACvB,uBAAuB;YACzB,OAAO;gBACL,2DAA2D;gBAC3D,uBAAuB,SAAS,cAAc;gBAC9C,YAAY,CAAA,OAAQ,CAAC;wBACnB,GAAG,IAAI;wBACP,gBAAgB,SAAS,cAAc;oBACzC,CAAC;gBACD,uBAAuB;gBAEvB,yDAAyD;gBACzD,IAAI,SAAS,cAAc,KAAK,KAAK;oBACnC,MAAM;gBACR;YACF;QACF,EAAE,OAAO,OAAO;YACd,8CAA8C;YAC9C,oBAAoB;YACpB,uBAAuB;QACzB,SAAU;YACR,iBAAiB;QACnB;IACF;IAEA,wEAAwE;IACxE,MAAM,uBAAuB;QAC3B,IAAI,CAAC,cAAc,cAAc,GAAG;QAEpC,IAAI;YACF,2EAA2E;YAC3E,oCAAoC;YACpC,MAAM,SAAS,MAAM,iHAAA,CAAA,eAAY,CAAC,SAAS,CAAC;YAE5C,IAAI,UAAU,OAAO,MAAM,GAAG,GAAG;gBAC/B,mDAAmD;gBACnD,MAAM,gBAAgB,OAAO,MAAM,CAAC,CAAA,IAClC,EAAE,aAAa,IAAI,QAAQ,IAAI,CAAC,EAAE,aAAa;gBAGjD,IAAI,cAAc,MAAM,GAAG,GAAG;oBAC5B,kDAAkD;oBAClD,MAAM,YAAY,KAAK,GAAG,IAAI,cAAc,GAAG,CAAC,CAAA,IAAK,SAAS,EAAE,aAAa,EAAE;oBAC/E,MAAM,aAAa,OAAO,YAAY;oBACtC,oBAAoB;oBAEpB,qEAAqE;oBACrE,YAAY,CAAA,OAAQ,CAAC;4BACnB,GAAG,IAAI;4BACP,eAAe;wBACjB,CAAC;gBACH,OAAO;oBACL,oBAAoB;oBACpB,YAAY,CAAA,OAAQ,CAAC;4BACnB,GAAG,IAAI;4BACP,eAAe;wBACjB,CAAC;gBACH;YACF,OAAO;gBACL,oBAAoB;gBACpB,YAAY,CAAA,OAAQ,CAAC;wBACnB,GAAG,IAAI;wBACP,eAAe;oBACjB,CAAC;YACH;QACF,EAAE,OAAO,OAAO;YACd,oBAAoB;YACpB,YAAY,CAAA,OAAQ,CAAC;oBACnB,GAAG,IAAI;oBACP,eAAe;gBACjB,CAAC;QACH;IACF;IAEA,MAAM,oBAAoB,CAAC,OAAuB;QAChD,YAAY,CAAA,OAAQ,CAAC;gBACnB,GAAG,IAAI;gBACP,CAAC,MAAM,EAAE;YACX,CAAC;QACD,SAAS;IACX;IAEA,yEAAyE;IACzE,MAAM,wBAAwB,OAAO;QACnC,uBAAuB;QACvB,YAAY,CAAA,OAAQ,CAAC;gBACnB,GAAG,IAAI;gBACP,gBAAgB;YAClB,CAAC;QACD,uBAAuB;QAEvB,wDAAwD;QACxD,IAAI,WAAW,KAAK;YAClB,MAAM;QACR,OAAO;YACL,0CAA0C;YAC1C,YAAY,CAAA,OAAQ,CAAC;oBACnB,GAAG,IAAI;oBACP,eAAe;gBACjB,CAAC;QACH;IACF;IAEA,MAAM,eAAe;QACnB,IAAI,CAAC,SAAS,aAAa,CAAC,IAAI,IAAI;YAClC,OAAO;QACT;QAEA,yCAAyC;QACzC,IAAI,SAAS,cAAc,KAAK,KAAK;YACnC,MAAM,cAAc,SAAS,aAAa,CAAC,IAAI;YAE/C,oCAAoC;YACpC,IAAI,iBAAiB,IAAI,CAAC,cAAc;gBACtC,OAAO;YACT;QACF;QAEA,IAAI,CAAC,SAAS,OAAO,CAAC,IAAI,IAAI;YAC5B,OAAO;QACT;QACA,IAAI,CAAC,SAAS,SAAS,CAAC,IAAI,IAAI;YAC9B,OAAO;QACT;QACA,IAAI,CAAC,SAAS,OAAO,CAAC,IAAI,IAAI;YAC5B,OAAO;QACT;QACA,IAAI,CAAC,SAAS,YAAY,CAAC,IAAI,IAAI;YACjC,OAAO;QACT;QAEA,MAAM,QAAQ,WAAW,SAAS,YAAY;QAC9C,IAAI,MAAM,UAAU,SAAS,GAAG;YAC9B,OAAO;QACT;QAEA,OAAO;IACT;IAEA,MAAM,aAAa;QACjB,MAAM,kBAAkB;QACxB,IAAI,iBAAiB;YACnB,SAAS;YACT;QACF;QAEA,IAAI;YACF,WAAW;YACX,SAAS;YAET,IAAI,CAAC,cAAc,cAAc,GAAG;gBAClC,MAAM,IAAI,MAAM;YAClB;YAEA,2BAA2B;YAC3B,MAAM,aAAa;gBACjB,eAAe,SAAS,aAAa,CAAC,IAAI;gBAC1C,SAAS,SAAS,OAAO,CAAC,IAAI,GAAG,WAAW;gBAC5C,WAAW,SAAS,SAAS,CAAC,IAAI,GAAG,WAAW;gBAChD,cAAc;gBACd,SAAS,SAAS,OAAO,CAAC,IAAI;gBAC9B,cAAc,WAAW,SAAS,YAAY;gBAC9C,mBAAmB,SAAS,iBAAiB,CAAC,IAAI,MAAM;gBACxD,WAAW,SAAS,SAAS,CAAC,IAAI,MAAM;gBACxC,OAAO,SAAS,KAAK,CAAC,IAAI,MAAM;gBAChC,UAAU,SAAS,QAAQ,IAAI;gBAC/B,gBAAgB,SAAS,cAAc;YACzC;YAEA,MAAM,iHAAA,CAAA,eAAY,CAAC,YAAY,CAAC,YAAY;YAE5C,UAAU,CAAC,OAAO,EAAE,SAAS,aAAa,CAAC,oBAAoB,CAAC;YAChE;QACF,EAAE,OAAO,OAAY;YACnB,MAAM,cAAc,MAAM,QAAQ,EAAE,MAAM,UAAU,MAAM,OAAO,IAAI;YAErE,6CAA6C;YAC7C,IAAI,YAAY,QAAQ,CAAC,gCAAgC,YAAY,QAAQ,CAAC,kBAAkB;gBAC9F,SAAS,CAAC,qBAAqB,EAAE,SAAS,aAAa,CAAC,yCAAyC,CAAC;YACpG,OAAO;gBACL,QAAQ;YACV;QACF,SAAU;YACR,WAAW;QACb;IACF;IAEA,MAAM,cAAc;QAClB,IAAI,CAAC,SAAS;YACZ,YAAY;YACZ,SAAS;YACT;QACF;IACF;IAEA,qBACE,8OAAC,kIAAA,CAAA,SAAM;QAAC,MAAM;QAAM,cAAc;kBAChC,cAAA,8OAAC,kIAAA,CAAA,gBAAa;YACZ,WAAU;YACV,OAAO;gBACL,OAAO;gBACP,UAAU;gBACV,UAAU;YACZ;;8BAEA,8OAAC,kIAAA,CAAA,eAAY;;sCACX,8OAAC,kIAAA,CAAA,cAAW;4BAAC,WAAU;;8CACrB,8OAAC,kMAAA,CAAA,OAAI;oCAAC,WAAU;;;;;;gCAAY;;;;;;;sCAG9B,8OAAC,kIAAA,CAAA,oBAAiB;;;;;;;;;;;8BAIpB,8OAAC;oBAAI,WAAU;;wBAEZ,+BACC,8OAAC;4BAAI,WAAU;;8CACb,8OAAC,iNAAA,CAAA,UAAO;oCAAC,WAAU;;;;;;8CACnB,8OAAC;8CAAK;;;;;;;;;;;;wBAKT,uBAAuB,CAAC,+BACvB,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAG,WAAU;8CAAmB;;;;;;8CACjC,8OAAC;oCAAE,WAAU;8CAA6B;;;;;;8CAG1C,8OAAC;oCAAI,WAAU;;sDACb,8OAAC,kIAAA,CAAA,SAAM;4CACL,SAAQ;4CACR,WAAU;4CACV,SAAS,IAAM,sBAAsB;sDAErC,cAAA,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDAAI,WAAU;kEAAc;;;;;;kEAC7B,8OAAC;wDAAI,WAAU;kEAAwB;;;;;;;;;;;;;;;;;sDAG3C,8OAAC,kIAAA,CAAA,SAAM;4CACL,SAAQ;4CACR,WAAU;4CACV,SAAS,IAAM,sBAAsB;sDAErC,cAAA,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDAAI,WAAU;kEAAc;;;;;;kEAC7B,8OAAC;wDAAI,WAAU;kEAAwB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;sCAQjD,8OAAC;4BAAI,WAAU;;8CAEb,8OAAC;oCAAI,WAAU;;wCAEZ,CAAC,uBAAuB,CAAC,+BACxB,8OAAC;4CAAI,WAAU;;8DACb,8OAAC,iIAAA,CAAA,QAAK;oDAAC,SAAQ;oDAAgB,WAAU;8DAAa;;;;;;8DAGtD,8OAAC,iIAAA,CAAA,QAAK;oDACJ,IAAG;oDACH,OAAO,SAAS,aAAa;oDAC7B,UAAU,CAAC,IAAM,kBAAkB,iBAAiB,EAAE,MAAM,CAAC,KAAK;oDAClE,aAAa,SAAS,cAAc,KAAK,MAAM,6BAA6B;oDAC5E,UAAU,WAAW,SAAS,cAAc,KAAK;oDACjD,WAAW,CAAC,WAAW,EAAE,SAAS,cAAc,KAAK,MAAM,eAAe,IAAI;;;;;;;;;;;;sDAMpF,8OAAC;4CAAI,WAAU;;8DACb,8OAAC,iIAAA,CAAA,QAAK;oDAAC,SAAQ;oDAAU,WAAU;8DAAa;;;;;;8DAGhD,8OAAC,iIAAA,CAAA,QAAK;oDACJ,IAAG;oDACH,OAAO,SAAS,OAAO;oDACvB,UAAU,CAAC,IAAM,kBAAkB,WAAW,EAAE,MAAM,CAAC,KAAK;oDAC5D,WAAU;oDACV,aAAY;oDACZ,UAAU;;;;;;;;;;;;sDAKd,8OAAC;4CAAI,WAAU;;8DACb,8OAAC,iIAAA,CAAA,QAAK;oDAAC,SAAQ;oDAAY,WAAU;8DAAa;;;;;;8DAGlD,8OAAC,iIAAA,CAAA,QAAK;oDACJ,IAAG;oDACH,OAAO,SAAS,SAAS;oDACzB,UAAU,CAAC,IAAM,kBAAkB,aAAa,EAAE,MAAM,CAAC,KAAK;oDAC9D,WAAU;oDACV,aAAY;oDACZ,UAAU;;;;;;;;;;;;sDAKd,8OAAC;4CAAI,WAAU;;8DACb,8OAAC,iIAAA,CAAA,QAAK;oDAAC,SAAQ;oDAAU,WAAU;8DAAa;;;;;;8DAGhD,8OAAC,iIAAA,CAAA,QAAK;oDACJ,IAAG;oDACH,OAAO,SAAS,OAAO;oDACvB,UAAU,CAAC,IAAM,kBAAkB,WAAW,EAAE,MAAM,CAAC,KAAK;oDAC5D,WAAU;oDACV,aAAY;oDACZ,UAAU;;;;;;;;;;;;sDAKd,8OAAC;4CAAI,WAAU;;8DACb,8OAAC,iIAAA,CAAA,QAAK;oDAAC,SAAQ;oDAAe,WAAU;8DAAa;;;;;;8DAGrD,8OAAC,iIAAA,CAAA,QAAK;oDACJ,IAAG;oDACH,MAAK;oDACL,MAAK;oDACL,KAAI;oDACJ,OAAO,SAAS,YAAY;oDAC5B,UAAU,CAAC,IAAM,kBAAkB,gBAAgB,EAAE,MAAM,CAAC,KAAK;oDACjE,WAAU;oDACV,aAAY;oDACZ,UAAU;;;;;;;;;;;;;;;;;;8CAMhB,8OAAC;oCAAI,WAAU;;sDAEb,8OAAC;4CAAI,WAAU;;8DACb,8OAAC,iIAAA,CAAA,QAAK;oDAAC,SAAQ;oDAAoB,WAAU;8DAAa;;;;;;8DAG1D,8OAAC,iIAAA,CAAA,QAAK;oDACJ,IAAG;oDACH,OAAO,SAAS,iBAAiB;oDACjC,UAAU,CAAC,IAAM,kBAAkB,qBAAqB,EAAE,MAAM,CAAC,KAAK;oDACtE,WAAU;oDACV,aAAY;oDACZ,UAAU;;;;;;;;;;;;sDAKd,8OAAC;4CAAI,WAAU;;8DACb,8OAAC,iIAAA,CAAA,QAAK;oDAAC,SAAQ;oDAAY,WAAU;8DAAa;;;;;;8DAGlD,8OAAC,iIAAA,CAAA,QAAK;oDACJ,IAAG;oDACH,OAAO,SAAS,SAAS;oDACzB,UAAU,CAAC,IAAM,kBAAkB,aAAa,EAAE,MAAM,CAAC,KAAK;oDAC9D,WAAU;oDACV,aAAY;oDACZ,UAAU;;;;;;;;;;;;sDAKd,8OAAC;4CAAI,WAAU;;8DACb,8OAAC,iIAAA,CAAA,QAAK;oDAAC,SAAQ;oDAAQ,WAAU;8DAAa;;;;;;8DAG9C,8OAAC,iIAAA,CAAA,QAAK;oDACJ,IAAG;oDACH,OAAO,SAAS,KAAK;oDACrB,UAAU,CAAC,IAAM,kBAAkB,SAAS,EAAE,MAAM,CAAC,KAAK;oDAC1D,WAAU;oDACV,aAAY;oDACZ,UAAU;;;;;;;;;;;;sDAKd,8OAAC;4CAAI,WAAU;;8DACb,8OAAC,iIAAA,CAAA,QAAK;oDAAC,SAAQ;oDAAW,WAAU;8DAAa;;;;;;8DAGjD,8OAAC,iIAAA,CAAA,QAAK;oDACJ,IAAG;oDACH,MAAK;oDACL,OAAO,SAAS,QAAQ;oDACxB,UAAU,CAAC,IAAM,kBAAkB,YAAY,EAAE,MAAM,CAAC,KAAK;oDAC7D,WAAU;oDACV,UAAU;;;;;;;;;;;;;;;;;;;;;;;;wBAOjB,uBACC,8OAAC,iIAAA,CAAA,QAAK;4BAAC,SAAQ;;8CACb,8OAAC,oNAAA,CAAA,cAAW;oCAAC,WAAU;;;;;;8CACvB,8OAAC,iIAAA,CAAA,mBAAgB;8CAAE;;;;;;;;;;;;;;;;;;8BAKzB,8OAAC,kIAAA,CAAA,eAAY;;sCACX,8OAAC,kIAAA,CAAA,SAAM;4BAAC,SAAQ;4BAAU,SAAS;4BAAa,UAAU,WAAW;sCAAqB;;;;;;sCAG1F,8OAAC,kIAAA,CAAA,SAAM;4BACL,SAAS;4BACT,UAAU,WAAW,uBAAuB;;gCAE3C,yBAAW,8OAAC,iNAAA,CAAA,UAAO;oCAAC,WAAU;;;;;;gCAC9B,UAAU,eAAe;;;;;;;;;;;;;;;;;;;;;;;;AAMtC", "debugId": null}}, {"offset": {"line": 1576, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/CMS/webapp-nextjs/src/components/ui/select.tsx"], "sourcesContent": ["\"use client\"\n\nimport * as React from \"react\"\nimport * as SelectPrimitive from \"@radix-ui/react-select\"\nimport { Check<PERSON><PERSON>, ChevronDownIcon, ChevronUpIcon } from \"lucide-react\"\n\nimport { cn } from \"@/lib/utils\"\n\nfunction Select({\n  ...props\n}: React.ComponentProps<typeof SelectPrimitive.Root>) {\n  return <SelectPrimitive.Root data-slot=\"select\" {...props} />\n}\n\nfunction SelectGroup({\n  ...props\n}: React.ComponentProps<typeof SelectPrimitive.Group>) {\n  return <SelectPrimitive.Group data-slot=\"select-group\" {...props} />\n}\n\nfunction SelectValue({\n  ...props\n}: React.ComponentProps<typeof SelectPrimitive.Value>) {\n  return <SelectPrimitive.Value data-slot=\"select-value\" {...props} />\n}\n\nfunction SelectTrigger({\n  className,\n  size = \"default\",\n  children,\n  ...props\n}: React.ComponentProps<typeof SelectPrimitive.Trigger> & {\n  size?: \"sm\" | \"default\"\n}) {\n  return (\n    <SelectPrimitive.Trigger\n      data-slot=\"select-trigger\"\n      data-size={size}\n      className={cn(\n        \"border-input data-[placeholder]:text-muted-foreground [&_svg:not([class*='text-'])]:text-muted-foreground focus-visible:border-ring focus-visible:ring-ring/50 aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive dark:bg-input/30 dark:hover:bg-input/50 flex w-fit items-center justify-between gap-2 rounded-md border bg-transparent px-3 py-2 text-sm whitespace-nowrap shadow-xs transition-[color,box-shadow] outline-none focus-visible:ring-[3px] disabled:cursor-not-allowed disabled:opacity-50 data-[size=default]:h-9 data-[size=sm]:h-8 *:data-[slot=select-value]:line-clamp-1 *:data-[slot=select-value]:flex *:data-[slot=select-value]:items-center *:data-[slot=select-value]:gap-2 [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4\",\n        className\n      )}\n      {...props}\n    >\n      {children}\n      <SelectPrimitive.Icon asChild>\n        <ChevronDownIcon className=\"size-4 opacity-50\" />\n      </SelectPrimitive.Icon>\n    </SelectPrimitive.Trigger>\n  )\n}\n\nfunction SelectContent({\n  className,\n  children,\n  position = \"popper\",\n  ...props\n}: React.ComponentProps<typeof SelectPrimitive.Content>) {\n  return (\n    <SelectPrimitive.Portal>\n      <SelectPrimitive.Content\n        data-slot=\"select-content\"\n        className={cn(\n          \"bg-popover text-popover-foreground data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2 relative z-50 max-h-(--radix-select-content-available-height) min-w-[8rem] origin-(--radix-select-content-transform-origin) overflow-x-hidden overflow-y-auto rounded-md border shadow-md\",\n          position === \"popper\" &&\n            \"data-[side=bottom]:translate-y-1 data-[side=left]:-translate-x-1 data-[side=right]:translate-x-1 data-[side=top]:-translate-y-1\",\n          className\n        )}\n        position={position}\n        {...props}\n      >\n        <SelectScrollUpButton />\n        <SelectPrimitive.Viewport\n          className={cn(\n            \"p-1\",\n            position === \"popper\" &&\n              \"h-[var(--radix-select-trigger-height)] w-full min-w-[var(--radix-select-trigger-width)] scroll-my-1\"\n          )}\n        >\n          {children}\n        </SelectPrimitive.Viewport>\n        <SelectScrollDownButton />\n      </SelectPrimitive.Content>\n    </SelectPrimitive.Portal>\n  )\n}\n\nfunction SelectLabel({\n  className,\n  ...props\n}: React.ComponentProps<typeof SelectPrimitive.Label>) {\n  return (\n    <SelectPrimitive.Label\n      data-slot=\"select-label\"\n      className={cn(\"text-muted-foreground px-2 py-1.5 text-xs\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction SelectItem({\n  className,\n  children,\n  ...props\n}: React.ComponentProps<typeof SelectPrimitive.Item>) {\n  return (\n    <SelectPrimitive.Item\n      data-slot=\"select-item\"\n      className={cn(\n        \"focus:bg-accent focus:text-accent-foreground [&_svg:not([class*='text-'])]:text-muted-foreground relative flex w-full cursor-default items-center gap-2 rounded-sm py-1.5 pr-8 pl-2 text-sm outline-hidden select-none data-[disabled]:pointer-events-none data-[disabled]:opacity-50 [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4 *:[span]:last:flex *:[span]:last:items-center *:[span]:last:gap-2\",\n        className\n      )}\n      {...props}\n    >\n      <span className=\"absolute right-2 flex size-3.5 items-center justify-center\">\n        <SelectPrimitive.ItemIndicator>\n          <CheckIcon className=\"size-4\" />\n        </SelectPrimitive.ItemIndicator>\n      </span>\n      <SelectPrimitive.ItemText>{children}</SelectPrimitive.ItemText>\n    </SelectPrimitive.Item>\n  )\n}\n\nfunction SelectSeparator({\n  className,\n  ...props\n}: React.ComponentProps<typeof SelectPrimitive.Separator>) {\n  return (\n    <SelectPrimitive.Separator\n      data-slot=\"select-separator\"\n      className={cn(\"bg-border pointer-events-none -mx-1 my-1 h-px\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction SelectScrollUpButton({\n  className,\n  ...props\n}: React.ComponentProps<typeof SelectPrimitive.ScrollUpButton>) {\n  return (\n    <SelectPrimitive.ScrollUpButton\n      data-slot=\"select-scroll-up-button\"\n      className={cn(\n        \"flex cursor-default items-center justify-center py-1\",\n        className\n      )}\n      {...props}\n    >\n      <ChevronUpIcon className=\"size-4\" />\n    </SelectPrimitive.ScrollUpButton>\n  )\n}\n\nfunction SelectScrollDownButton({\n  className,\n  ...props\n}: React.ComponentProps<typeof SelectPrimitive.ScrollDownButton>) {\n  return (\n    <SelectPrimitive.ScrollDownButton\n      data-slot=\"select-scroll-down-button\"\n      className={cn(\n        \"flex cursor-default items-center justify-center py-1\",\n        className\n      )}\n      {...props}\n    >\n      <ChevronDownIcon className=\"size-4\" />\n    </SelectPrimitive.ScrollDownButton>\n  )\n}\n\nexport {\n  Select,\n  SelectContent,\n  SelectGroup,\n  SelectItem,\n  SelectLabel,\n  SelectScrollDownButton,\n  SelectScrollUpButton,\n  SelectSeparator,\n  SelectTrigger,\n  SelectValue,\n}\n"], "names": [], "mappings": ";;;;;;;;;;;;;AAGA;AACA;AAAA;AAAA;AAEA;AANA;;;;;AAQA,SAAS,OAAO,EACd,GAAG,OAC+C;IAClD,qBAAO,8OAAC,kKAAA,CAAA,OAAoB;QAAC,aAAU;QAAU,GAAG,KAAK;;;;;;AAC3D;AAEA,SAAS,YAAY,EACnB,GAAG,OACgD;IACnD,qBAAO,8OAAC,kKAAA,CAAA,QAAqB;QAAC,aAAU;QAAgB,GAAG,KAAK;;;;;;AAClE;AAEA,SAAS,YAAY,EACnB,GAAG,OACgD;IACnD,qBAAO,8OAAC,kKAAA,CAAA,QAAqB;QAAC,aAAU;QAAgB,GAAG,KAAK;;;;;;AAClE;AAEA,SAAS,cAAc,EACrB,SAAS,EACT,OAAO,SAAS,EAChB,QAAQ,EACR,GAAG,OAGJ;IACC,qBACE,8OAAC,kKAAA,CAAA,UAAuB;QACtB,aAAU;QACV,aAAW;QACX,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,gzBACA;QAED,GAAG,KAAK;;YAER;0BACD,8OAAC,kKAAA,CAAA,OAAoB;gBAAC,OAAO;0BAC3B,cAAA,8OAAC,wNAAA,CAAA,kBAAe;oBAAC,WAAU;;;;;;;;;;;;;;;;;AAInC;AAEA,SAAS,cAAc,EACrB,SAAS,EACT,QAAQ,EACR,WAAW,QAAQ,EACnB,GAAG,OACkD;IACrD,qBACE,8OAAC,kKAAA,CAAA,SAAsB;kBACrB,cAAA,8OAAC,kKAAA,CAAA,UAAuB;YACtB,aAAU;YACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,ijBACA,aAAa,YACX,mIACF;YAEF,UAAU;YACT,GAAG,KAAK;;8BAET,8OAAC;;;;;8BACD,8OAAC,kKAAA,CAAA,WAAwB;oBACvB,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,OACA,aAAa,YACX;8BAGH;;;;;;8BAEH,8OAAC;;;;;;;;;;;;;;;;AAIT;AAEA,SAAS,YAAY,EACnB,SAAS,EACT,GAAG,OACgD;IACnD,qBACE,8OAAC,kKAAA,CAAA,QAAqB;QACpB,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,6CAA6C;QAC1D,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,WAAW,EAClB,SAAS,EACT,QAAQ,EACR,GAAG,OAC+C;IAClD,qBACE,8OAAC,kKAAA,CAAA,OAAoB;QACnB,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,6aACA;QAED,GAAG,KAAK;;0BAET,8OAAC;gBAAK,WAAU;0BACd,cAAA,8OAAC,kKAAA,CAAA,gBAA6B;8BAC5B,cAAA,8OAAC,wMAAA,CAAA,YAAS;wBAAC,WAAU;;;;;;;;;;;;;;;;0BAGzB,8OAAC,kKAAA,CAAA,WAAwB;0BAAE;;;;;;;;;;;;AAGjC;AAEA,SAAS,gBAAgB,EACvB,SAAS,EACT,GAAG,OACoD;IACvD,qBACE,8OAAC,kKAAA,CAAA,YAAyB;QACxB,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,iDAAiD;QAC9D,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,qBAAqB,EAC5B,SAAS,EACT,GAAG,OACyD;IAC5D,qBACE,8OAAC,kKAAA,CAAA,iBAA8B;QAC7B,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,wDACA;QAED,GAAG,KAAK;kBAET,cAAA,8OAAC,oNAAA,CAAA,gBAAa;YAAC,WAAU;;;;;;;;;;;AAG/B;AAEA,SAAS,uBAAuB,EAC9B,SAAS,EACT,GAAG,OAC2D;IAC9D,qBACE,8OAAC,kKAAA,CAAA,mBAAgC;QAC/B,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,wDACA;QAED,GAAG,KAAK;kBAET,cAAA,8OAAC,wNAAA,CAAA,kBAAe;YAAC,WAAU;;;;;;;;;;;AAGjC", "debugId": null}}, {"offset": {"line": 1801, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/CMS/webapp-nextjs/src/components/bobine/ModificaBobinaDialog.tsx"], "sourcesContent": ["'use client'\n\nimport { useState, useEffect } from 'react'\nimport {\n  <PERSON><PERSON>,\n  <PERSON>alogContent,\n  DialogDescription,\n  <PERSON>alogFooter,\n  DialogHeader,\n  DialogTitle,\n} from '@/components/ui/dialog'\nimport { Button } from '@/components/ui/button'\nimport { Input } from '@/components/ui/input'\nimport { Label } from '@/components/ui/label'\nimport {\n  Select,\n  SelectContent,\n  SelectItem,\n  SelectTrigger,\n  SelectValue,\n} from '@/components/ui/select'\nimport { Alert, AlertDescription } from '@/components/ui/alert'\nimport { Loader2, AlertCircle, Package, Info } from 'lucide-react'\nimport { parcoCaviApi } from '@/lib/api'\nimport { useAuth } from '@/contexts/AuthContext'\n\ninterface Bobina {\n  id_bobina: string\n  numero_bobina: string\n  utility: string\n  tipologia: string\n  sezione: string // formazione nel sistema\n  metri_totali: number\n  metri_residui: number\n  stato_bobina: string\n  ubicazione_bobina?: string\n  fornitore?: string\n  n_DDT?: string\n  data_DDT?: string\n  configurazione: string\n}\n\ninterface FormData {\n  numero_bobina: string\n  utility: string\n  tipologia: string\n  sezione: string\n  metri_totali: string\n  metri_residui: string\n  stato_bobina: string\n  ubicazione_bobina: string\n  fornitore: string\n  n_DDT: string\n  data_DDT: string\n  configurazione: string\n}\n\ninterface FormErrors {\n  [key: string]: string\n}\n\ninterface FormWarnings {\n  [key: string]: string\n}\n\ninterface ModificaBobinaDialogProps {\n  open: boolean\n  onClose: () => void\n  bobina: Bobina | null\n  onSuccess: (message: string) => void\n  onError: (message: string) => void\n}\n\nexport default function ModificaBobinaDialog({\n  open,\n  onClose,\n  bobina,\n  onSuccess,\n  onError\n}: ModificaBobinaDialogProps) {\n  const { cantiere } = useAuth()\n  \n  // Stati per il form\n  const [formData, setFormData] = useState<FormData>({\n    numero_bobina: '',\n    utility: '',\n    tipologia: '',\n    sezione: '',\n    metri_totali: '',\n    metri_residui: '',\n    stato_bobina: '',\n    ubicazione_bobina: '',\n    fornitore: '',\n    n_DDT: '',\n    data_DDT: '',\n    configurazione: ''\n  })\n  const [formErrors, setFormErrors] = useState<FormErrors>({})\n  const [formWarnings, setFormWarnings] = useState<FormWarnings>({})\n  const [saving, setSaving] = useState(false)\n\n  // Carica dati bobina quando si apre il dialog\n  useEffect(() => {\n    if (open && bobina) {\n      setFormData({\n        numero_bobina: bobina.numero_bobina || '',\n        utility: bobina.utility || '',\n        tipologia: bobina.tipologia || '',\n        sezione: bobina.sezione || '',\n        metri_totali: bobina.metri_totali?.toString() || '',\n        metri_residui: bobina.metri_residui?.toString() || '',\n        stato_bobina: bobina.stato_bobina || '',\n        ubicazione_bobina: bobina.ubicazione_bobina || '',\n        fornitore: bobina.fornitore || '',\n        n_DDT: bobina.n_DDT || '',\n        data_DDT: bobina.data_DDT || '',\n        configurazione: bobina.configurazione || ''\n      })\n      setFormErrors({})\n      setFormWarnings({})\n    }\n  }, [open, bobina])\n\n  // Validazione real-time\n  useEffect(() => {\n    validateForm()\n  }, [formData])\n\n  const validateForm = () => {\n    const errors: FormErrors = {}\n    const warnings: FormWarnings = {}\n\n    // Validazioni obbligatorie\n    if (!formData.utility.trim()) {\n      errors.utility = 'Utility è obbligatoria'\n    }\n    if (!formData.tipologia.trim()) {\n      errors.tipologia = 'Tipologia è obbligatoria'\n    }\n    if (!formData.sezione.trim()) {\n      errors.sezione = 'Formazione è obbligatoria'\n    }\n    if (!formData.metri_totali.trim()) {\n      errors.metri_totali = 'Metri totali sono obbligatori'\n    } else {\n      const metri = parseFloat(formData.metri_totali)\n      if (isNaN(metri) || metri <= 0) {\n        errors.metri_totali = 'Inserire un valore numerico valido maggiore di 0'\n      }\n    }\n\n    // Validazione data DDT\n    if (formData.data_DDT && !/^\\d{4}-\\d{2}-\\d{2}$/.test(formData.data_DDT)) {\n      errors.data_DDT = 'Formato data non valido (YYYY-MM-DD)'\n    }\n\n    setFormErrors(errors)\n    setFormWarnings(warnings)\n  }\n\n  const handleFormChange = (field: string, value: string) => {\n    setFormData(prev => {\n      const newData = { ...prev, [field]: value }\n      \n      // Se cambiano i metri totali, ricalcola i metri residui\n      if (field === 'metri_totali' && bobina) {\n        const nuoviMetriTotali = parseFloat(value) || 0\n        const metriInstallati = bobina.metri_totali - bobina.metri_residui\n        const nuoviMetriResidui = Math.max(0, nuoviMetriTotali - metriInstallati)\n        newData.metri_residui = nuoviMetriResidui.toString()\n      }\n      \n      return newData\n    })\n  }\n\n  // Verifica se la bobina può essere modificata\n  const canModifyBobina = () => {\n    if (!bobina) return false\n    \n    // Bobine OVER possono modificare solo alcuni campi\n    if (bobina.stato_bobina === 'Over') {\n      return true // Può modificare fornitore, ubicazione, DDT\n    }\n    \n    // Bobine disponibili possono modificare tutto\n    return bobina.stato_bobina === 'Disponibile'\n  }\n\n  // Verifica se un campo specifico può essere modificato\n  const canModifyField = (field: string) => {\n    if (!bobina) return false\n    \n    // Campi sempre modificabili\n    const alwaysModifiable = ['fornitore', 'ubicazione_bobina', 'n_DDT', 'data_DDT']\n    if (alwaysModifiable.includes(field)) {\n      return true\n    }\n    \n    // Altri campi solo se bobina è disponibile\n    return bobina.stato_bobina === 'Disponibile'\n  }\n\n  const handleSave = async () => {\n    if (!bobina || !cantiere) return\n\n    if (Object.keys(formErrors).length > 0) {\n      return\n    }\n\n    if (!canModifyBobina()) {\n      onError('La bobina non può essere modificata nel suo stato attuale')\n      return\n    }\n\n    try {\n      setSaving(true)\n\n      // Prepara i dati per l'API\n      const updateData = {\n        utility: formData.utility,\n        tipologia: formData.tipologia,\n        sezione: formData.sezione, // sezione nel DB = formazione nel sistema\n        metri_totali: parseFloat(formData.metri_totali),\n        ubicazione_bobina: formData.ubicazione_bobina,\n        fornitore: formData.fornitore,\n        n_DDT: formData.n_DDT,\n        data_DDT: formData.data_DDT || null\n      }\n\n      // Aggiorna bobina tramite API\n      await parcoCaviApi.updateBobina(cantiere.id_cantiere, bobina.id_bobina, updateData)\n\n      onSuccess(`Bobina ${bobina.numero_bobina} aggiornata con successo`)\n      onClose()\n    } catch (error: any) {\n      const errorMessage = error.response?.data?.detail || error.message || 'Errore durante la modifica della bobina'\n      onError(errorMessage)\n    } finally {\n      setSaving(false)\n    }\n  }\n\n  const handleClose = () => {\n    if (!saving) {\n      setFormData({\n        numero_bobina: '',\n        utility: '',\n        tipologia: '',\n        sezione: '',\n        metri_totali: '',\n        metri_residui: '',\n        stato_bobina: '',\n        ubicazione_bobina: '',\n        fornitore: '',\n        n_DDT: '',\n        data_DDT: '',\n        configurazione: ''\n      })\n      setFormErrors({})\n      setFormWarnings({})\n      onClose()\n    }\n  }\n\n  if (!bobina) return null\n\n  return (\n    <Dialog open={open} onOpenChange={handleClose}>\n      <DialogContent className=\"max-w-2xl max-h-[90vh] overflow-y-auto\">\n        <DialogHeader>\n          <DialogTitle className=\"flex items-center gap-2\">\n            <Package className=\"h-5 w-5\" />\n            Modifica Bobina\n          </DialogTitle>\n          <DialogDescription>\n            Modifica i dati della bobina {bobina.numero_bobina}\n          </DialogDescription>\n        </DialogHeader>\n\n        <div className=\"space-y-4\">\n          {/* Alert condizioni modifica */}\n          <Alert className=\"border-blue-200 bg-blue-50\">\n            <Info className=\"h-4 w-4 text-blue-600\" />\n            <AlertDescription className=\"text-blue-800\">\n              <div className=\"font-semibold mb-1\">Condizioni per la modifica:</div>\n              <ul className=\"list-disc list-inside text-sm space-y-1\">\n                <li>La bobina deve essere nello stato \"Disponibile\"</li>\n                <li>La bobina non deve essere associata a nessun cavo</li>\n                <li>Se modifichi i metri totali, i metri residui verranno aggiornati automaticamente</li>\n              </ul>\n              <div className=\"mt-2 text-sm\">\n                <strong>Stato attuale:</strong> {bobina.stato_bobina}\n              </div>\n            </AlertDescription>\n          </Alert>\n\n          {/* Warning se ci sono avvisi */}\n          {Object.keys(formWarnings).length > 0 && (\n            <Alert className=\"border-amber-200 bg-amber-50\">\n              <AlertCircle className=\"h-4 w-4 text-amber-600\" />\n              <AlertDescription className=\"text-amber-800\">\n                <div className=\"font-semibold\">Attenzione:</div>\n                <ul className=\"list-disc list-inside text-sm\">\n                  {Object.values(formWarnings).map((warning, index) => (\n                    <li key={index}>{warning}</li>\n                  ))}\n                </ul>\n              </AlertDescription>\n            </Alert>\n          )}\n\n          {/* Form a due colonne */}\n          <div className=\"grid grid-cols-1 md:grid-cols-2 gap-4\">\n            {/* ID Bobina (non modificabile) */}\n            <div className=\"space-y-2\">\n              <Label htmlFor=\"numero_bobina\">ID Bobina</Label>\n              <Input\n                id=\"numero_bobina\"\n                value={formData.numero_bobina}\n                disabled={true}\n                className=\"bg-gray-100 font-semibold\"\n              />\n            </div>\n\n            {/* Utility */}\n            <div className=\"space-y-2\">\n              <Label htmlFor=\"utility\">Utility *</Label>\n              <Input\n                id=\"utility\"\n                value={formData.utility}\n                onChange={(e) => handleFormChange('utility', e.target.value)}\n                disabled={saving || !canModifyField('utility')}\n                className={formErrors.utility ? 'border-red-500' : ''}\n              />\n              {formErrors.utility && (\n                <p className=\"text-sm text-red-600\">{formErrors.utility}</p>\n              )}\n            </div>\n\n            {/* Tipologia */}\n            <div className=\"space-y-2\">\n              <Label htmlFor=\"tipologia\">Tipologia *</Label>\n              <Input\n                id=\"tipologia\"\n                value={formData.tipologia}\n                onChange={(e) => handleFormChange('tipologia', e.target.value)}\n                disabled={saving || !canModifyField('tipologia')}\n                className={formErrors.tipologia ? 'border-red-500' : ''}\n              />\n              {formErrors.tipologia && (\n                <p className=\"text-sm text-red-600\">{formErrors.tipologia}</p>\n              )}\n            </div>\n\n            {/* Formazione */}\n            <div className=\"space-y-2\">\n              <Label htmlFor=\"sezione\">Formazione *</Label>\n              <Input\n                id=\"sezione\"\n                value={formData.sezione}\n                onChange={(e) => handleFormChange('sezione', e.target.value)}\n                disabled={saving || !canModifyField('sezione')}\n                className={formErrors.sezione ? 'border-red-500' : ''}\n              />\n              {formErrors.sezione && (\n                <p className=\"text-sm text-red-600\">{formErrors.sezione}</p>\n              )}\n            </div>\n\n            {/* Metri Totali */}\n            <div className=\"space-y-2\">\n              <Label htmlFor=\"metri_totali\">Metri Totali *</Label>\n              <Input\n                id=\"metri_totali\"\n                type=\"number\"\n                value={formData.metri_totali}\n                onChange={(e) => handleFormChange('metri_totali', e.target.value)}\n                disabled={saving || !canModifyField('metri_totali')}\n                className={formErrors.metri_totali ? 'border-red-500' : ''}\n                step=\"0.1\"\n                min=\"0\"\n              />\n              {formErrors.metri_totali && (\n                <p className=\"text-sm text-red-600\">{formErrors.metri_totali}</p>\n              )}\n            </div>\n\n            {/* Metri Residui (non modificabile) */}\n            <div className=\"space-y-2\">\n              <Label htmlFor=\"metri_residui\">Metri Residui</Label>\n              <Input\n                id=\"metri_residui\"\n                type=\"number\"\n                value={formData.metri_residui}\n                disabled={true}\n                className=\"bg-gray-100\"\n              />\n              <p className=\"text-sm text-gray-500\">I metri residui non possono essere modificati direttamente</p>\n            </div>\n\n            {/* Stato Bobina (non modificabile) */}\n            <div className=\"space-y-2\">\n              <Label htmlFor=\"stato_bobina\">Stato Bobina</Label>\n              <Select value={formData.stato_bobina} disabled={true}>\n                <SelectTrigger className=\"bg-gray-100\">\n                  <SelectValue />\n                </SelectTrigger>\n                <SelectContent>\n                  <SelectItem value=\"Disponibile\">Disponibile</SelectItem>\n                  <SelectItem value=\"In uso\">In uso</SelectItem>\n                  <SelectItem value=\"Terminata\">Terminata</SelectItem>\n                  <SelectItem value=\"Danneggiata\">Danneggiata</SelectItem>\n                  <SelectItem value=\"Over\">Over</SelectItem>\n                </SelectContent>\n              </Select>\n            </div>\n\n            {/* Ubicazione Bobina */}\n            <div className=\"space-y-2\">\n              <Label htmlFor=\"ubicazione_bobina\">Ubicazione Bobina</Label>\n              <Input\n                id=\"ubicazione_bobina\"\n                value={formData.ubicazione_bobina}\n                onChange={(e) => handleFormChange('ubicazione_bobina', e.target.value)}\n                disabled={saving}\n              />\n            </div>\n\n            {/* Fornitore */}\n            <div className=\"space-y-2\">\n              <Label htmlFor=\"fornitore\">Fornitore</Label>\n              <Input\n                id=\"fornitore\"\n                value={formData.fornitore}\n                onChange={(e) => handleFormChange('fornitore', e.target.value)}\n                disabled={saving}\n              />\n            </div>\n\n            {/* Numero DDT */}\n            <div className=\"space-y-2\">\n              <Label htmlFor=\"n_DDT\">Numero DDT</Label>\n              <Input\n                id=\"n_DDT\"\n                value={formData.n_DDT}\n                onChange={(e) => handleFormChange('n_DDT', e.target.value)}\n                disabled={saving}\n              />\n            </div>\n\n            {/* Data DDT */}\n            <div className=\"space-y-2\">\n              <Label htmlFor=\"data_DDT\">Data DDT (YYYY-MM-DD)</Label>\n              <Input\n                id=\"data_DDT\"\n                type=\"date\"\n                value={formData.data_DDT}\n                onChange={(e) => handleFormChange('data_DDT', e.target.value)}\n                disabled={saving}\n                className={formErrors.data_DDT ? 'border-red-500' : ''}\n              />\n              {formErrors.data_DDT && (\n                <p className=\"text-sm text-red-600\">{formErrors.data_DDT}</p>\n              )}\n            </div>\n\n            {/* Modalità Numerazione (non modificabile) */}\n            <div className=\"space-y-2 md:col-span-2\">\n              <Label htmlFor=\"configurazione\">Modalità Numerazione</Label>\n              <Input\n                id=\"configurazione\"\n                value={formData.configurazione === 's' ? 'Automatica' : 'Manuale'}\n                disabled={true}\n                className=\"bg-gray-100\"\n              />\n              <p className=\"text-sm text-gray-500\">\n                {formData.configurazione === 's'\n                  ? 'Numerazione progressiva automatica (1, 2, 3, ...)'\n                  : 'Inserimento manuale dell\\'ID bobina (es. A123, SPEC01, ...)'}\n              </p>\n            </div>\n          </div>\n        </div>\n\n        <DialogFooter>\n          <Button variant=\"outline\" onClick={handleClose} disabled={saving}>\n            Annulla\n          </Button>\n          <Button \n            onClick={handleSave} \n            disabled={saving || Object.keys(formErrors).length > 0 || !canModifyBobina()}\n            className=\"bg-mariner-600 hover:bg-mariner-700\"\n          >\n            {saving && <Loader2 className=\"mr-2 h-4 w-4 animate-spin\" />}\n            Salva\n          </Button>\n        </DialogFooter>\n      </DialogContent>\n    </Dialog>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAQA;AACA;AACA;AACA;AAOA;AACA;AAAA;AAAA;AAAA;AACA;AACA;AAxBA;;;;;;;;;;;;AAyEe,SAAS,qBAAqB,EAC3C,IAAI,EACJ,OAAO,EACP,MAAM,EACN,SAAS,EACT,OAAO,EACmB;IAC1B,MAAM,EAAE,QAAQ,EAAE,GAAG,CAAA,GAAA,+HAAA,CAAA,UAAO,AAAD;IAE3B,oBAAoB;IACpB,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAY;QACjD,eAAe;QACf,SAAS;QACT,WAAW;QACX,SAAS;QACT,cAAc;QACd,eAAe;QACf,cAAc;QACd,mBAAmB;QACnB,WAAW;QACX,OAAO;QACP,UAAU;QACV,gBAAgB;IAClB;IACA,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAc,CAAC;IAC1D,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAgB,CAAC;IAChE,MAAM,CAAC,QAAQ,UAAU,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAErC,8CAA8C;IAC9C,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,IAAI,QAAQ,QAAQ;YAClB,YAAY;gBACV,eAAe,OAAO,aAAa,IAAI;gBACvC,SAAS,OAAO,OAAO,IAAI;gBAC3B,WAAW,OAAO,SAAS,IAAI;gBAC/B,SAAS,OAAO,OAAO,IAAI;gBAC3B,cAAc,OAAO,YAAY,EAAE,cAAc;gBACjD,eAAe,OAAO,aAAa,EAAE,cAAc;gBACnD,cAAc,OAAO,YAAY,IAAI;gBACrC,mBAAmB,OAAO,iBAAiB,IAAI;gBAC/C,WAAW,OAAO,SAAS,IAAI;gBAC/B,OAAO,OAAO,KAAK,IAAI;gBACvB,UAAU,OAAO,QAAQ,IAAI;gBAC7B,gBAAgB,OAAO,cAAc,IAAI;YAC3C;YACA,cAAc,CAAC;YACf,gBAAgB,CAAC;QACnB;IACF,GAAG;QAAC;QAAM;KAAO;IAEjB,wBAAwB;IACxB,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR;IACF,GAAG;QAAC;KAAS;IAEb,MAAM,eAAe;QACnB,MAAM,SAAqB,CAAC;QAC5B,MAAM,WAAyB,CAAC;QAEhC,2BAA2B;QAC3B,IAAI,CAAC,SAAS,OAAO,CAAC,IAAI,IAAI;YAC5B,OAAO,OAAO,GAAG;QACnB;QACA,IAAI,CAAC,SAAS,SAAS,CAAC,IAAI,IAAI;YAC9B,OAAO,SAAS,GAAG;QACrB;QACA,IAAI,CAAC,SAAS,OAAO,CAAC,IAAI,IAAI;YAC5B,OAAO,OAAO,GAAG;QACnB;QACA,IAAI,CAAC,SAAS,YAAY,CAAC,IAAI,IAAI;YACjC,OAAO,YAAY,GAAG;QACxB,OAAO;YACL,MAAM,QAAQ,WAAW,SAAS,YAAY;YAC9C,IAAI,MAAM,UAAU,SAAS,GAAG;gBAC9B,OAAO,YAAY,GAAG;YACxB;QACF;QAEA,uBAAuB;QACvB,IAAI,SAAS,QAAQ,IAAI,CAAC,sBAAsB,IAAI,CAAC,SAAS,QAAQ,GAAG;YACvE,OAAO,QAAQ,GAAG;QACpB;QAEA,cAAc;QACd,gBAAgB;IAClB;IAEA,MAAM,mBAAmB,CAAC,OAAe;QACvC,YAAY,CAAA;YACV,MAAM,UAAU;gBAAE,GAAG,IAAI;gBAAE,CAAC,MAAM,EAAE;YAAM;YAE1C,wDAAwD;YACxD,IAAI,UAAU,kBAAkB,QAAQ;gBACtC,MAAM,mBAAmB,WAAW,UAAU;gBAC9C,MAAM,kBAAkB,OAAO,YAAY,GAAG,OAAO,aAAa;gBAClE,MAAM,oBAAoB,KAAK,GAAG,CAAC,GAAG,mBAAmB;gBACzD,QAAQ,aAAa,GAAG,kBAAkB,QAAQ;YACpD;YAEA,OAAO;QACT;IACF;IAEA,8CAA8C;IAC9C,MAAM,kBAAkB;QACtB,IAAI,CAAC,QAAQ,OAAO;QAEpB,mDAAmD;QACnD,IAAI,OAAO,YAAY,KAAK,QAAQ;YAClC,OAAO,KAAK,4CAA4C;;QAC1D;QAEA,8CAA8C;QAC9C,OAAO,OAAO,YAAY,KAAK;IACjC;IAEA,uDAAuD;IACvD,MAAM,iBAAiB,CAAC;QACtB,IAAI,CAAC,QAAQ,OAAO;QAEpB,4BAA4B;QAC5B,MAAM,mBAAmB;YAAC;YAAa;YAAqB;YAAS;SAAW;QAChF,IAAI,iBAAiB,QAAQ,CAAC,QAAQ;YACpC,OAAO;QACT;QAEA,2CAA2C;QAC3C,OAAO,OAAO,YAAY,KAAK;IACjC;IAEA,MAAM,aAAa;QACjB,IAAI,CAAC,UAAU,CAAC,UAAU;QAE1B,IAAI,OAAO,IAAI,CAAC,YAAY,MAAM,GAAG,GAAG;YACtC;QACF;QAEA,IAAI,CAAC,mBAAmB;YACtB,QAAQ;YACR;QACF;QAEA,IAAI;YACF,UAAU;YAEV,2BAA2B;YAC3B,MAAM,aAAa;gBACjB,SAAS,SAAS,OAAO;gBACzB,WAAW,SAAS,SAAS;gBAC7B,SAAS,SAAS,OAAO;gBACzB,cAAc,WAAW,SAAS,YAAY;gBAC9C,mBAAmB,SAAS,iBAAiB;gBAC7C,WAAW,SAAS,SAAS;gBAC7B,OAAO,SAAS,KAAK;gBACrB,UAAU,SAAS,QAAQ,IAAI;YACjC;YAEA,8BAA8B;YAC9B,MAAM,iHAAA,CAAA,eAAY,CAAC,YAAY,CAAC,SAAS,WAAW,EAAE,OAAO,SAAS,EAAE;YAExE,UAAU,CAAC,OAAO,EAAE,OAAO,aAAa,CAAC,wBAAwB,CAAC;YAClE;QACF,EAAE,OAAO,OAAY;YACnB,MAAM,eAAe,MAAM,QAAQ,EAAE,MAAM,UAAU,MAAM,OAAO,IAAI;YACtE,QAAQ;QACV,SAAU;YACR,UAAU;QACZ;IACF;IAEA,MAAM,cAAc;QAClB,IAAI,CAAC,QAAQ;YACX,YAAY;gBACV,eAAe;gBACf,SAAS;gBACT,WAAW;gBACX,SAAS;gBACT,cAAc;gBACd,eAAe;gBACf,cAAc;gBACd,mBAAmB;gBACnB,WAAW;gBACX,OAAO;gBACP,UAAU;gBACV,gBAAgB;YAClB;YACA,cAAc,CAAC;YACf,gBAAgB,CAAC;YACjB;QACF;IACF;IAEA,IAAI,CAAC,QAAQ,OAAO;IAEpB,qBACE,8OAAC,kIAAA,CAAA,SAAM;QAAC,MAAM;QAAM,cAAc;kBAChC,cAAA,8OAAC,kIAAA,CAAA,gBAAa;YAAC,WAAU;;8BACvB,8OAAC,kIAAA,CAAA,eAAY;;sCACX,8OAAC,kIAAA,CAAA,cAAW;4BAAC,WAAU;;8CACrB,8OAAC,wMAAA,CAAA,UAAO;oCAAC,WAAU;;;;;;gCAAY;;;;;;;sCAGjC,8OAAC,kIAAA,CAAA,oBAAiB;;gCAAC;gCACa,OAAO,aAAa;;;;;;;;;;;;;8BAItD,8OAAC;oBAAI,WAAU;;sCAEb,8OAAC,iIAAA,CAAA,QAAK;4BAAC,WAAU;;8CACf,8OAAC,kMAAA,CAAA,OAAI;oCAAC,WAAU;;;;;;8CAChB,8OAAC,iIAAA,CAAA,mBAAgB;oCAAC,WAAU;;sDAC1B,8OAAC;4CAAI,WAAU;sDAAqB;;;;;;sDACpC,8OAAC;4CAAG,WAAU;;8DACZ,8OAAC;8DAAG;;;;;;8DACJ,8OAAC;8DAAG;;;;;;8DACJ,8OAAC;8DAAG;;;;;;;;;;;;sDAEN,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;8DAAO;;;;;;gDAAuB;gDAAE,OAAO,YAAY;;;;;;;;;;;;;;;;;;;wBAMzD,OAAO,IAAI,CAAC,cAAc,MAAM,GAAG,mBAClC,8OAAC,iIAAA,CAAA,QAAK;4BAAC,WAAU;;8CACf,8OAAC,oNAAA,CAAA,cAAW;oCAAC,WAAU;;;;;;8CACvB,8OAAC,iIAAA,CAAA,mBAAgB;oCAAC,WAAU;;sDAC1B,8OAAC;4CAAI,WAAU;sDAAgB;;;;;;sDAC/B,8OAAC;4CAAG,WAAU;sDACX,OAAO,MAAM,CAAC,cAAc,GAAG,CAAC,CAAC,SAAS,sBACzC,8OAAC;8DAAgB;mDAAR;;;;;;;;;;;;;;;;;;;;;;sCAQnB,8OAAC;4BAAI,WAAU;;8CAEb,8OAAC;oCAAI,WAAU;;sDACb,8OAAC,iIAAA,CAAA,QAAK;4CAAC,SAAQ;sDAAgB;;;;;;sDAC/B,8OAAC,iIAAA,CAAA,QAAK;4CACJ,IAAG;4CACH,OAAO,SAAS,aAAa;4CAC7B,UAAU;4CACV,WAAU;;;;;;;;;;;;8CAKd,8OAAC;oCAAI,WAAU;;sDACb,8OAAC,iIAAA,CAAA,QAAK;4CAAC,SAAQ;sDAAU;;;;;;sDACzB,8OAAC,iIAAA,CAAA,QAAK;4CACJ,IAAG;4CACH,OAAO,SAAS,OAAO;4CACvB,UAAU,CAAC,IAAM,iBAAiB,WAAW,EAAE,MAAM,CAAC,KAAK;4CAC3D,UAAU,UAAU,CAAC,eAAe;4CACpC,WAAW,WAAW,OAAO,GAAG,mBAAmB;;;;;;wCAEpD,WAAW,OAAO,kBACjB,8OAAC;4CAAE,WAAU;sDAAwB,WAAW,OAAO;;;;;;;;;;;;8CAK3D,8OAAC;oCAAI,WAAU;;sDACb,8OAAC,iIAAA,CAAA,QAAK;4CAAC,SAAQ;sDAAY;;;;;;sDAC3B,8OAAC,iIAAA,CAAA,QAAK;4CACJ,IAAG;4CACH,OAAO,SAAS,SAAS;4CACzB,UAAU,CAAC,IAAM,iBAAiB,aAAa,EAAE,MAAM,CAAC,KAAK;4CAC7D,UAAU,UAAU,CAAC,eAAe;4CACpC,WAAW,WAAW,SAAS,GAAG,mBAAmB;;;;;;wCAEtD,WAAW,SAAS,kBACnB,8OAAC;4CAAE,WAAU;sDAAwB,WAAW,SAAS;;;;;;;;;;;;8CAK7D,8OAAC;oCAAI,WAAU;;sDACb,8OAAC,iIAAA,CAAA,QAAK;4CAAC,SAAQ;sDAAU;;;;;;sDACzB,8OAAC,iIAAA,CAAA,QAAK;4CACJ,IAAG;4CACH,OAAO,SAAS,OAAO;4CACvB,UAAU,CAAC,IAAM,iBAAiB,WAAW,EAAE,MAAM,CAAC,KAAK;4CAC3D,UAAU,UAAU,CAAC,eAAe;4CACpC,WAAW,WAAW,OAAO,GAAG,mBAAmB;;;;;;wCAEpD,WAAW,OAAO,kBACjB,8OAAC;4CAAE,WAAU;sDAAwB,WAAW,OAAO;;;;;;;;;;;;8CAK3D,8OAAC;oCAAI,WAAU;;sDACb,8OAAC,iIAAA,CAAA,QAAK;4CAAC,SAAQ;sDAAe;;;;;;sDAC9B,8OAAC,iIAAA,CAAA,QAAK;4CACJ,IAAG;4CACH,MAAK;4CACL,OAAO,SAAS,YAAY;4CAC5B,UAAU,CAAC,IAAM,iBAAiB,gBAAgB,EAAE,MAAM,CAAC,KAAK;4CAChE,UAAU,UAAU,CAAC,eAAe;4CACpC,WAAW,WAAW,YAAY,GAAG,mBAAmB;4CACxD,MAAK;4CACL,KAAI;;;;;;wCAEL,WAAW,YAAY,kBACtB,8OAAC;4CAAE,WAAU;sDAAwB,WAAW,YAAY;;;;;;;;;;;;8CAKhE,8OAAC;oCAAI,WAAU;;sDACb,8OAAC,iIAAA,CAAA,QAAK;4CAAC,SAAQ;sDAAgB;;;;;;sDAC/B,8OAAC,iIAAA,CAAA,QAAK;4CACJ,IAAG;4CACH,MAAK;4CACL,OAAO,SAAS,aAAa;4CAC7B,UAAU;4CACV,WAAU;;;;;;sDAEZ,8OAAC;4CAAE,WAAU;sDAAwB;;;;;;;;;;;;8CAIvC,8OAAC;oCAAI,WAAU;;sDACb,8OAAC,iIAAA,CAAA,QAAK;4CAAC,SAAQ;sDAAe;;;;;;sDAC9B,8OAAC,kIAAA,CAAA,SAAM;4CAAC,OAAO,SAAS,YAAY;4CAAE,UAAU;;8DAC9C,8OAAC,kIAAA,CAAA,gBAAa;oDAAC,WAAU;8DACvB,cAAA,8OAAC,kIAAA,CAAA,cAAW;;;;;;;;;;8DAEd,8OAAC,kIAAA,CAAA,gBAAa;;sEACZ,8OAAC,kIAAA,CAAA,aAAU;4DAAC,OAAM;sEAAc;;;;;;sEAChC,8OAAC,kIAAA,CAAA,aAAU;4DAAC,OAAM;sEAAS;;;;;;sEAC3B,8OAAC,kIAAA,CAAA,aAAU;4DAAC,OAAM;sEAAY;;;;;;sEAC9B,8OAAC,kIAAA,CAAA,aAAU;4DAAC,OAAM;sEAAc;;;;;;sEAChC,8OAAC,kIAAA,CAAA,aAAU;4DAAC,OAAM;sEAAO;;;;;;;;;;;;;;;;;;;;;;;;8CAM/B,8OAAC;oCAAI,WAAU;;sDACb,8OAAC,iIAAA,CAAA,QAAK;4CAAC,SAAQ;sDAAoB;;;;;;sDACnC,8OAAC,iIAAA,CAAA,QAAK;4CACJ,IAAG;4CACH,OAAO,SAAS,iBAAiB;4CACjC,UAAU,CAAC,IAAM,iBAAiB,qBAAqB,EAAE,MAAM,CAAC,KAAK;4CACrE,UAAU;;;;;;;;;;;;8CAKd,8OAAC;oCAAI,WAAU;;sDACb,8OAAC,iIAAA,CAAA,QAAK;4CAAC,SAAQ;sDAAY;;;;;;sDAC3B,8OAAC,iIAAA,CAAA,QAAK;4CACJ,IAAG;4CACH,OAAO,SAAS,SAAS;4CACzB,UAAU,CAAC,IAAM,iBAAiB,aAAa,EAAE,MAAM,CAAC,KAAK;4CAC7D,UAAU;;;;;;;;;;;;8CAKd,8OAAC;oCAAI,WAAU;;sDACb,8OAAC,iIAAA,CAAA,QAAK;4CAAC,SAAQ;sDAAQ;;;;;;sDACvB,8OAAC,iIAAA,CAAA,QAAK;4CACJ,IAAG;4CACH,OAAO,SAAS,KAAK;4CACrB,UAAU,CAAC,IAAM,iBAAiB,SAAS,EAAE,MAAM,CAAC,KAAK;4CACzD,UAAU;;;;;;;;;;;;8CAKd,8OAAC;oCAAI,WAAU;;sDACb,8OAAC,iIAAA,CAAA,QAAK;4CAAC,SAAQ;sDAAW;;;;;;sDAC1B,8OAAC,iIAAA,CAAA,QAAK;4CACJ,IAAG;4CACH,MAAK;4CACL,OAAO,SAAS,QAAQ;4CACxB,UAAU,CAAC,IAAM,iBAAiB,YAAY,EAAE,MAAM,CAAC,KAAK;4CAC5D,UAAU;4CACV,WAAW,WAAW,QAAQ,GAAG,mBAAmB;;;;;;wCAErD,WAAW,QAAQ,kBAClB,8OAAC;4CAAE,WAAU;sDAAwB,WAAW,QAAQ;;;;;;;;;;;;8CAK5D,8OAAC;oCAAI,WAAU;;sDACb,8OAAC,iIAAA,CAAA,QAAK;4CAAC,SAAQ;sDAAiB;;;;;;sDAChC,8OAAC,iIAAA,CAAA,QAAK;4CACJ,IAAG;4CACH,OAAO,SAAS,cAAc,KAAK,MAAM,eAAe;4CACxD,UAAU;4CACV,WAAU;;;;;;sDAEZ,8OAAC;4CAAE,WAAU;sDACV,SAAS,cAAc,KAAK,MACzB,sDACA;;;;;;;;;;;;;;;;;;;;;;;;8BAMZ,8OAAC,kIAAA,CAAA,eAAY;;sCACX,8OAAC,kIAAA,CAAA,SAAM;4BAAC,SAAQ;4BAAU,SAAS;4BAAa,UAAU;sCAAQ;;;;;;sCAGlE,8OAAC,kIAAA,CAAA,SAAM;4BACL,SAAS;4BACT,UAAU,UAAU,OAAO,IAAI,CAAC,YAAY,MAAM,GAAG,KAAK,CAAC;4BAC3D,WAAU;;gCAET,wBAAU,8OAAC,iNAAA,CAAA,UAAO;oCAAC,WAAU;;;;;;gCAA+B;;;;;;;;;;;;;;;;;;;;;;;;AAOzE", "debugId": null}}, {"offset": {"line": 2698, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/CMS/webapp-nextjs/src/components/bobine/EliminaBobinaDialog.tsx"], "sourcesContent": ["'use client'\n\nimport { useState } from 'react'\nimport {\n  <PERSON><PERSON>,\n  DialogContent,\n  DialogDescription,\n  DialogFooter,\n  DialogHeader,\n  DialogTitle,\n} from '@/components/ui/dialog'\nimport { Button } from '@/components/ui/button'\nimport { Alert, AlertDescription } from '@/components/ui/alert'\nimport { <PERSON>ader2, <PERSON><PERSON><PERSON><PERSON>gle, Trash2 } from 'lucide-react'\nimport { parcoCaviApi } from '@/lib/api'\n\nimport { ParcoCavo } from '@/types'\nimport { canModifyReel, getReelStateDescription, REEL_STATES } from '@/utils/bobineUtils'\n\ninterface EliminaBobinaDialogProps {\n  open: boolean\n  onClose: () => void\n  bobina: ParcoCavo | null\n  cantiereId: number\n  onSuccess: (message: string) => void\n  onError: (message: string) => void\n}\n\nexport default function EliminaBobinaDialog({\n  open,\n  onClose,\n  bobina,\n  cantiereId,\n  onSuccess,\n  onError\n}: EliminaBobinaDialogProps) {\n  const [loading, setLoading] = useState(false)\n\n  const handleDelete = async () => {\n    if (!bobina) return\n\n    try {\n      setLoading(true)\n\n      if (!cantiereId || cantiereId <= 0) {\n        throw new Error('Cantiere non selezionato')\n      }\n\n      // Estrai il numero bobina dall'ID (formato C{cantiere}_B{numero})\n      const bobinaNumero = bobina.id_bobina.split('_B')[1]\n      \n      const response = await parcoCaviApi.deleteBobina(cantiereId, bobinaNumero)\n\n      let message = `Bobina ${bobina.numero_bobina} eliminata con successo`\n      \n      // Se è l'ultima bobina, aggiungi informazione aggiuntiva\n      if (response.data?.is_last_bobina) {\n        message += '. Era l\\'ultima bobina del cantiere.'\n      }\n\n      onSuccess(message)\n      onClose()\n    } catch (error: any) {\n      const errorMessage = error.response?.data?.detail || error.message || 'Errore durante l\\'eliminazione della bobina'\n      onError(errorMessage)\n    } finally {\n      setLoading(false)\n    }\n  }\n\n  const handleClose = () => {\n    if (!loading) {\n      onClose()\n    }\n  }\n\n  if (!bobina) return null\n\n  // Verifica se la bobina può essere eliminata\n  const isOverState = bobina.stato_bobina === REEL_STATES.OVER\n  const canDelete = !isOverState &&\n                   canModifyReel(bobina.stato_bobina) &&\n                   bobina.metri_residui === bobina.metri_totali\n\n  // Determina il tipo di avviso da mostrare\n  const getWarningInfo = () => {\n    // Controllo prioritario per bobine OVER\n    if (isOverState) {\n      return {\n        type: 'error' as const,\n        title: 'Bobina OVER - Eliminazione bloccata',\n        message: 'Le bobine in stato OVER non possono essere eliminate per preservare la tracciabilità del sistema. Contattare l\\'amministratore per la gestione di bobine OVER.'\n      }\n    }\n\n    if (!canModifyReel(bobina.stato_bobina)) {\n      return {\n        type: 'error' as const,\n        title: 'Eliminazione non consentita',\n        message: `La bobina è in stato \"${bobina.stato_bobina}\" e non può essere eliminata. ${getReelStateDescription(bobina.stato_bobina)}`\n      }\n    }\n\n    if (bobina.metri_residui !== bobina.metri_totali) {\n      return {\n        type: 'error' as const,\n        title: 'Bobina in uso',\n        message: `La bobina ha ${bobina.metri_residui}m residui su ${bobina.metri_totali}m totali. Solo le bobine completamente disponibili possono essere eliminate.`\n      }\n    }\n\n    return {\n      type: 'warning' as const,\n      title: 'Conferma eliminazione',\n      message: 'Questa operazione è irreversibile. La bobina verrà rimossa definitivamente dal parco cavi.'\n    }\n  }\n\n  const warningInfo = getWarningInfo()\n\n  return (\n    <Dialog open={open} onOpenChange={handleClose}>\n      <DialogContent className=\"max-w-md\">\n        <DialogHeader>\n          <DialogTitle className=\"flex items-center gap-2\">\n            <Trash2 className=\"h-5 w-5 text-red-600\" />\n            Elimina Bobina\n          </DialogTitle>\n          <DialogDescription>\n            Stai per eliminare la bobina {bobina.numero_bobina}\n          </DialogDescription>\n        </DialogHeader>\n\n        <div className=\"py-4\">\n          {/* Informazioni bobina */}\n          <div className=\"bg-slate-50 p-4 rounded-lg mb-4\">\n            <h4 className=\"font-medium mb-2\">Dettagli bobina:</h4>\n            <div className=\"text-sm space-y-1\">\n              <div><strong>Bobina:</strong> {bobina.numero_bobina}</div>\n              <div><strong>Utility:</strong> {bobina.utility}</div>\n              <div><strong>Tipologia:</strong> {bobina.tipologia}</div>\n              <div><strong>Sezione:</strong> {bobina.sezione}</div>\n              <div><strong>Stato:</strong> {bobina.stato_bobina}</div>\n              <div><strong>Metri:</strong> {bobina.metri_residui}m / {bobina.metri_totali}m</div>\n              {bobina.ubicazione_bobina && (\n                <div><strong>Ubicazione:</strong> {bobina.ubicazione_bobina}</div>\n              )}\n            </div>\n          </div>\n\n          {/* Avviso */}\n          <Alert variant={warningInfo.type === 'error' ? 'destructive' : 'default'}>\n            <AlertTriangle className=\"h-4 w-4\" />\n            <div>\n              <div className=\"font-medium\">{warningInfo.title}</div>\n              <AlertDescription className=\"mt-1\">\n                {warningInfo.message}\n              </AlertDescription>\n            </div>\n          </Alert>\n        </div>\n\n        <DialogFooter>\n          <Button variant=\"outline\" onClick={handleClose} disabled={loading}>\n            Annulla\n          </Button>\n          <Button \n            variant=\"destructive\" \n            onClick={handleDelete} \n            disabled={loading || !canDelete}\n          >\n            {loading && <Loader2 className=\"mr-2 h-4 w-4 animate-spin\" />}\n            {loading ? 'Eliminando...' : 'Elimina Bobina'}\n          </Button>\n        </DialogFooter>\n      </DialogContent>\n    </Dialog>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAQA;AACA;AACA;AAAA;AAAA;AACA;AAGA;AAjBA;;;;;;;;;AA4Be,SAAS,oBAAoB,EAC1C,IAAI,EACJ,OAAO,EACP,MAAM,EACN,UAAU,EACV,SAAS,EACT,OAAO,EACkB;IACzB,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAEvC,MAAM,eAAe;QACnB,IAAI,CAAC,QAAQ;QAEb,IAAI;YACF,WAAW;YAEX,IAAI,CAAC,cAAc,cAAc,GAAG;gBAClC,MAAM,IAAI,MAAM;YAClB;YAEA,kEAAkE;YAClE,MAAM,eAAe,OAAO,SAAS,CAAC,KAAK,CAAC,KAAK,CAAC,EAAE;YAEpD,MAAM,WAAW,MAAM,iHAAA,CAAA,eAAY,CAAC,YAAY,CAAC,YAAY;YAE7D,IAAI,UAAU,CAAC,OAAO,EAAE,OAAO,aAAa,CAAC,uBAAuB,CAAC;YAErE,yDAAyD;YACzD,IAAI,SAAS,IAAI,EAAE,gBAAgB;gBACjC,WAAW;YACb;YAEA,UAAU;YACV;QACF,EAAE,OAAO,OAAY;YACnB,MAAM,eAAe,MAAM,QAAQ,EAAE,MAAM,UAAU,MAAM,OAAO,IAAI;YACtE,QAAQ;QACV,SAAU;YACR,WAAW;QACb;IACF;IAEA,MAAM,cAAc;QAClB,IAAI,CAAC,SAAS;YACZ;QACF;IACF;IAEA,IAAI,CAAC,QAAQ,OAAO;IAEpB,6CAA6C;IAC7C,MAAM,cAAc,OAAO,YAAY,KAAK,2HAAA,CAAA,cAAW,CAAC,IAAI;IAC5D,MAAM,YAAY,CAAC,eACF,CAAA,GAAA,2HAAA,CAAA,gBAAa,AAAD,EAAE,OAAO,YAAY,KACjC,OAAO,aAAa,KAAK,OAAO,YAAY;IAE7D,0CAA0C;IAC1C,MAAM,iBAAiB;QACrB,wCAAwC;QACxC,IAAI,aAAa;YACf,OAAO;gBACL,MAAM;gBACN,OAAO;gBACP,SAAS;YACX;QACF;QAEA,IAAI,CAAC,CAAA,GAAA,2HAAA,CAAA,gBAAa,AAAD,EAAE,OAAO,YAAY,GAAG;YACvC,OAAO;gBACL,MAAM;gBACN,OAAO;gBACP,SAAS,CAAC,sBAAsB,EAAE,OAAO,YAAY,CAAC,8BAA8B,EAAE,CAAA,GAAA,2HAAA,CAAA,0BAAuB,AAAD,EAAE,OAAO,YAAY,GAAG;YACtI;QACF;QAEA,IAAI,OAAO,aAAa,KAAK,OAAO,YAAY,EAAE;YAChD,OAAO;gBACL,MAAM;gBACN,OAAO;gBACP,SAAS,CAAC,aAAa,EAAE,OAAO,aAAa,CAAC,aAAa,EAAE,OAAO,YAAY,CAAC,4EAA4E,CAAC;YAChK;QACF;QAEA,OAAO;YACL,MAAM;YACN,OAAO;YACP,SAAS;QACX;IACF;IAEA,MAAM,cAAc;IAEpB,qBACE,8OAAC,kIAAA,CAAA,SAAM;QAAC,MAAM;QAAM,cAAc;kBAChC,cAAA,8OAAC,kIAAA,CAAA,gBAAa;YAAC,WAAU;;8BACvB,8OAAC,kIAAA,CAAA,eAAY;;sCACX,8OAAC,kIAAA,CAAA,cAAW;4BAAC,WAAU;;8CACrB,8OAAC,0MAAA,CAAA,SAAM;oCAAC,WAAU;;;;;;gCAAyB;;;;;;;sCAG7C,8OAAC,kIAAA,CAAA,oBAAiB;;gCAAC;gCACa,OAAO,aAAa;;;;;;;;;;;;;8BAItD,8OAAC;oBAAI,WAAU;;sCAEb,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAG,WAAU;8CAAmB;;;;;;8CACjC,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;;8DAAI,8OAAC;8DAAO;;;;;;gDAAgB;gDAAE,OAAO,aAAa;;;;;;;sDACnD,8OAAC;;8DAAI,8OAAC;8DAAO;;;;;;gDAAiB;gDAAE,OAAO,OAAO;;;;;;;sDAC9C,8OAAC;;8DAAI,8OAAC;8DAAO;;;;;;gDAAmB;gDAAE,OAAO,SAAS;;;;;;;sDAClD,8OAAC;;8DAAI,8OAAC;8DAAO;;;;;;gDAAiB;gDAAE,OAAO,OAAO;;;;;;;sDAC9C,8OAAC;;8DAAI,8OAAC;8DAAO;;;;;;gDAAe;gDAAE,OAAO,YAAY;;;;;;;sDACjD,8OAAC;;8DAAI,8OAAC;8DAAO;;;;;;gDAAe;gDAAE,OAAO,aAAa;gDAAC;gDAAK,OAAO,YAAY;gDAAC;;;;;;;wCAC3E,OAAO,iBAAiB,kBACvB,8OAAC;;8DAAI,8OAAC;8DAAO;;;;;;gDAAoB;gDAAE,OAAO,iBAAiB;;;;;;;;;;;;;;;;;;;sCAMjE,8OAAC,iIAAA,CAAA,QAAK;4BAAC,SAAS,YAAY,IAAI,KAAK,UAAU,gBAAgB;;8CAC7D,8OAAC,wNAAA,CAAA,gBAAa;oCAAC,WAAU;;;;;;8CACzB,8OAAC;;sDACC,8OAAC;4CAAI,WAAU;sDAAe,YAAY,KAAK;;;;;;sDAC/C,8OAAC,iIAAA,CAAA,mBAAgB;4CAAC,WAAU;sDACzB,YAAY,OAAO;;;;;;;;;;;;;;;;;;;;;;;;8BAM5B,8OAAC,kIAAA,CAAA,eAAY;;sCACX,8OAAC,kIAAA,CAAA,SAAM;4BAAC,SAAQ;4BAAU,SAAS;4BAAa,UAAU;sCAAS;;;;;;sCAGnE,8OAAC,kIAAA,CAAA,SAAM;4BACL,SAAQ;4BACR,SAAS;4BACT,UAAU,WAAW,CAAC;;gCAErB,yBAAW,8OAAC,iNAAA,CAAA,UAAO;oCAAC,WAAU;;;;;;gCAC9B,UAAU,kBAAkB;;;;;;;;;;;;;;;;;;;;;;;;AAMzC", "debugId": null}}, {"offset": {"line": 3079, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/CMS/webapp-nextjs/src/components/ui/tabs.tsx"], "sourcesContent": ["\"use client\"\n\nimport * as React from \"react\"\nimport * as TabsPrimitive from \"@radix-ui/react-tabs\"\n\nimport { cn } from \"@/lib/utils\"\n\nconst Tabs = TabsPrimitive.Root\n\nconst TabsList = React.forwardRef<\n  React.ElementRef<typeof TabsPrimitive.List>,\n  React.ComponentPropsWithoutRef<typeof TabsPrimitive.List>\n>(({ className, ...props }, ref) => (\n  <TabsPrimitive.List\n    ref={ref}\n    className={cn(\n      \"inline-flex h-10 items-center justify-center rounded-md bg-muted p-1 text-muted-foreground\",\n      className\n    )}\n    {...props}\n  />\n))\nTabsList.displayName = TabsPrimitive.List.displayName\n\nconst TabsTrigger = React.forwardRef<\n  React.ElementRef<typeof TabsPrimitive.Trigger>,\n  React.ComponentPropsWithoutRef<typeof TabsPrimitive.Trigger>\n>(({ className, ...props }, ref) => (\n  <TabsPrimitive.Trigger\n    ref={ref}\n    className={cn(\n      \"inline-flex items-center justify-center whitespace-nowrap rounded-sm px-3 py-1.5 text-sm font-medium ring-offset-background transition-all focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 data-[state=active]:bg-background data-[state=active]:text-foreground data-[state=active]:shadow-sm\",\n      className\n    )}\n    {...props}\n  />\n))\nTabsTrigger.displayName = TabsPrimitive.Trigger.displayName\n\nconst TabsContent = React.forwardRef<\n  React.ElementRef<typeof TabsPrimitive.Content>,\n  React.ComponentPropsWithoutRef<typeof TabsPrimitive.Content>\n>(({ className, ...props }, ref) => (\n  <TabsPrimitive.Content\n    ref={ref}\n    className={cn(\n      \"mt-2 ring-offset-background focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2\",\n      className\n    )}\n    {...props}\n  />\n))\nTabsContent.displayName = TabsPrimitive.Content.displayName\n\nexport { Tabs, TabsList, TabsTrigger, TabsContent }\n"], "names": [], "mappings": ";;;;;;;AAEA;AACA;AAEA;AALA;;;;;AAOA,MAAM,OAAO,gKAAA,CAAA,OAAkB;AAE/B,MAAM,yBAAW,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAG9B,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC,gKAAA,CAAA,OAAkB;QACjB,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,8FACA;QAED,GAAG,KAAK;;;;;;AAGb,SAAS,WAAW,GAAG,gKAAA,CAAA,OAAkB,CAAC,WAAW;AAErD,MAAM,4BAAc,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAGjC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC,gKAAA,CAAA,UAAqB;QACpB,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,uYACA;QAED,GAAG,KAAK;;;;;;AAGb,YAAY,WAAW,GAAG,gKAAA,CAAA,UAAqB,CAAC,WAAW;AAE3D,MAAM,4BAAc,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAGjC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC,gKAAA,CAAA,UAAqB;QACpB,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,mIACA;QAED,GAAG,KAAK;;;;;;AAGb,YAAY,WAAW,GAAG,gKAAA,CAAA,UAAqB,CAAC,WAAW", "debugId": null}}, {"offset": {"line": 3132, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/CMS/webapp-nextjs/src/components/bobine/AggiungiCaviDialogSimple.tsx"], "sourcesContent": ["'use client'\n\n/**\n * 🔒 FUNZIONE BLOCCATA - NON MODIFICARE\n *\n * Questa funzione \"Aggiungi Cavi a Bobina\" è stata testata e funziona perfettamente.\n *\n * LOGICA OVER CORRETTA:\n * - OVER = Solo quando UN SINGOLO CAVO supera i metri residui della bobina\n * - NON OVER = Quando nessun singolo cavo supera i residui (anche se il totale li supera)\n *\n * ESEMPI:\n * - <PERSON><PERSON> 300m + Cavo 10m = NON OVER ✅\n * - <PERSON><PERSON> 300m + Cavo 350m = OVER ✅\n * - <PERSON>ina 300m + 3 cavi da 150m = NON OVER ✅\n *\n * ⚠️ QUALSIASI MODIFICA DEVE ESSERE DOCUMENTATA E TESTATA\n */\n\nimport { useState, useEffect, useMemo } from 'react'\nimport {\n  Dialog,\n  DialogContent,\n  DialogDescription,\n  DialogFooter,\n  DialogHeader,\n  DialogTitle,\n} from '@/components/ui/dialog'\nimport { Button } from '@/components/ui/button'\nimport { Input } from '@/components/ui/input'\nimport { Label } from '@/components/ui/label'\nimport { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs'\nimport { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'\nimport { Badge } from '@/components/ui/badge'\nimport { Checkbox } from '@/components/ui/checkbox'\nimport { Alert, AlertDescription } from '@/components/ui/alert'\nimport { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'\nimport {\n  AlertCircle,\n  Cable,\n  Loader2,\n  Search,\n  Filter,\n  CheckSquare,\n  Square,\n  ArrowUpDown,\n  Calculator,\n  Zap\n} from 'lucide-react'\nimport { caviApi } from '@/lib/api'\nimport { useAuth } from '@/contexts/AuthContext'\nimport { Cavo, ParcoCavo } from '@/types'\n\n// Hook per debounce ottimizzato\nconst useDebounce = (value: string, delay: number) => {\n  const [debouncedValue, setDebouncedValue] = useState(value)\n\n  useEffect(() => {\n    const handler = setTimeout(() => {\n      setDebouncedValue(value)\n    }, delay)\n\n    return () => {\n      clearTimeout(handler)\n    }\n  }, [value, delay])\n\n  return debouncedValue\n}\n\ninterface AggiungiCaviDialogProps {\n  open: boolean\n  onClose: () => void\n  bobina: ParcoCavo | null\n  cantiereId: number\n  onSuccess: (message: string) => void\n  onError: (message: string) => void\n}\n\ninterface CavoConMetri extends Cavo {\n  metri_inseriti?: number\n}\n\nexport default function AggiungiCaviDialogSimple({\n  open,\n  onClose,\n  bobina,\n  cantiereId,\n  onSuccess,\n  onError\n}: AggiungiCaviDialogProps) {\n  const [loading, setLoading] = useState(false)\n  const [caviLoading, setCaviLoading] = useState(false)\n  const [saving, setSaving] = useState(false)\n  \n  // Stati per i cavi\n  const [caviCompatibili, setCaviCompatibili] = useState<CavoConMetri[]>([])\n  const [caviIncompatibili, setCaviIncompatibili] = useState<CavoConMetri[]>([])\n  const [caviSelezionati, setCaviSelezionati] = useState<CavoConMetri[]>([])\n  const [caviMetri, setCaviMetri] = useState<Record<string, string>>({})\n  \n  // Stati per validazione\n  const [errors, setErrors] = useState<Record<string, string>>({})\n\n  // Stati per filtri e ricerca avanzata\n  const [searchTerm, setSearchTerm] = useState('')\n  const debouncedSearchTerm = useDebounce(searchTerm, 300) // Debounce 300ms\n  const [sortBy, setSortBy] = useState<'id_cavo' | 'metri_teorici' | 'tipologia' | 'ubicazione_partenza'>('id_cavo')\n  const [sortOrder, setSortOrder] = useState<'asc' | 'desc'>('asc')\n  const [filterTipologia, setFilterTipologia] = useState<string>('all')\n  const [filterFormazione, setFilterFormazione] = useState<string>('all')\n  const [filterMetriMin, setFilterMetriMin] = useState('')\n  const [filterMetriMax, setFilterMetriMax] = useState('')\n  const [currentPage, setCurrentPage] = useState(1)\n  const [itemsPerPage, setItemsPerPage] = useState(20)\n\n  // Funzioni di filtro e ordinamento intelligenti (SPOSTATO QUI)\n  const filteredAndSortedCavi = useMemo(() => {\n    const filterCavi = (cavi: CavoConMetri[]) => {\n      return cavi.filter(cavo => {\n        // Filtro ricerca (OTTIMIZZATO con debounce)\n        const matchesSearch = !debouncedSearchTerm ||\n          cavo.id_cavo.toLowerCase().includes(debouncedSearchTerm.toLowerCase()) ||\n          cavo.tipologia?.toLowerCase().includes(debouncedSearchTerm.toLowerCase()) ||\n          cavo.ubicazione_partenza?.toLowerCase().includes(debouncedSearchTerm.toLowerCase()) ||\n          cavo.ubicazione_arrivo?.toLowerCase().includes(debouncedSearchTerm.toLowerCase())\n\n        // Filtro tipologia\n        const matchesTipologia = filterTipologia === 'all' || cavo.tipologia === filterTipologia\n\n        // Filtro formazione\n        const matchesFormazione = filterFormazione === 'all' || cavo.sezione === filterFormazione\n\n        // Filtro metri teorici\n        const metriTeorici = parseFloat(cavo.metri_teorici?.toString() || '0')\n        const matchesMetriMin = !filterMetriMin || metriTeorici >= parseFloat(filterMetriMin)\n        const matchesMetriMax = !filterMetriMax || metriTeorici <= parseFloat(filterMetriMax)\n\n        return matchesSearch && matchesTipologia && matchesFormazione && matchesMetriMin && matchesMetriMax\n      })\n    }\n\n    const sortCavi = (cavi: CavoConMetri[]) => {\n      return [...cavi].sort((a, b) => {\n        let aValue: any, bValue: any\n\n        switch (sortBy) {\n          case 'id_cavo':\n            aValue = a.id_cavo\n            bValue = b.id_cavo\n            break\n          case 'metri_teorici':\n            aValue = parseFloat(a.metri_teorici?.toString() || '0')\n            bValue = parseFloat(b.metri_teorici?.toString() || '0')\n            break\n          case 'tipologia':\n            aValue = a.tipologia || ''\n            bValue = b.tipologia || ''\n            break\n          case 'ubicazione_partenza':\n            aValue = a.ubicazione_partenza || ''\n            bValue = b.ubicazione_partenza || ''\n            break\n          default:\n            return 0\n        }\n\n        if (typeof aValue === 'string') {\n          const comparison = aValue.localeCompare(bValue)\n          return sortOrder === 'asc' ? comparison : -comparison\n        } else {\n          return sortOrder === 'asc' ? aValue - bValue : bValue - aValue\n        }\n      })\n    }\n\n    const compatibiliFiltrati = sortCavi(filterCavi(caviCompatibili))\n    const incompatibiliFiltrati = sortCavi(filterCavi(caviIncompatibili))\n\n    return { compatibiliFiltrati, incompatibiliFiltrati }\n  }, [caviCompatibili, caviIncompatibili, debouncedSearchTerm, sortBy, sortOrder, filterTipologia, filterFormazione, filterMetriMin, filterMetriMax])\n\n  // Paginazione (SPOSTATO QUI)\n  const paginatedCavi = useMemo(() => {\n    const { compatibiliFiltrati, incompatibiliFiltrati } = filteredAndSortedCavi\n\n    const startIndex = (currentPage - 1) * itemsPerPage\n    const endIndex = startIndex + itemsPerPage\n\n    return {\n      compatibili: compatibiliFiltrati.slice(startIndex, endIndex),\n      incompatibili: incompatibiliFiltrati.slice(startIndex, endIndex),\n      totalCompatibili: compatibiliFiltrati.length,\n      totalIncompatibili: incompatibiliFiltrati.length,\n      totalPages: Math.ceil(Math.max(compatibiliFiltrati.length, incompatibiliFiltrati.length) / itemsPerPage)\n    }\n  }, [filteredAndSortedCavi, currentPage, itemsPerPage])\n\n  // Calcoli intelligenti (SPOSTATO QUI)\n  const calculations = useMemo(() => {\n    const metriTotaliSelezionati = Object.values(caviMetri).reduce((sum, metri) =>\n      sum + parseFloat(metri || '0'), 0\n    )\n    const metriResiduiBobina = bobina?.metri_residui || 0\n\n    // 🔒 LOGICA OVER FINALE - NON MODIFICARE\n    // OVER = Solo quando UN SINGOLO CAVO supera i metri residui della bobina\n    const hasSingleCavoOver = Object.entries(caviMetri).some(([cavoId, metri]) => {\n      const metriInseriti = parseFloat(metri || '0')\n      return metriInseriti > metriResiduiBobina // ✅ OVER = cavo > residui bobina\n    })\n\n    // Incompatibili sono solo un warning, NON causano OVER\n    const hasIncompatibleCavi = caviSelezionati.some(c => c._isIncompatible)\n\n    // OVER state = solo quando un cavo supera i residui (o bobina già OVER dal DB)\n    const isOverState = hasSingleCavoOver || (bobina?.stato_bobina === 'OVER')\n\n    const metriEccedenza = Math.max(0, metriTotaliSelezionati - metriResiduiBobina)\n    const percentualeUtilizzo = metriResiduiBobina > 0 ? (metriTotaliSelezionati / metriResiduiBobina) * 100 : 0\n\n    return {\n      metriTotaliSelezionati,\n      metriResiduiBobina,\n      isOverState,\n      metriEccedenza,\n      percentualeUtilizzo,\n      hasSingleCavoOver,\n      hasIncompatibleCavi\n    }\n  }, [caviMetri, bobina, caviSelezionati])\n\n  // Opzioni per i filtri (SPOSTATO QUI)\n  const tipologieUniche = useMemo(() => {\n    const tipologie = new Set([...caviCompatibili, ...caviIncompatibili].map(c => c.tipologia).filter(Boolean))\n    return Array.from(tipologie).sort()\n  }, [caviCompatibili, caviIncompatibili])\n\n  const formazioniUniche = useMemo(() => {\n    const formazioni = new Set([...caviCompatibili, ...caviIncompatibili].map(c => c.sezione).filter(Boolean))\n    return Array.from(formazioni).sort()\n  }, [caviCompatibili, caviIncompatibili])\n\n  // Carica i cavi disponibili\n  const loadCavi = async () => {\n    if (!cantiereId || !bobina) {\n      return\n    }\n\n    try {\n      setCaviLoading(true)\n      const caviData = await caviApi.getCavi(cantiereId)\n\n      // Filtro ESATTO come webapp originale\n      const caviDisponibili = caviData.filter(cavo => {\n        const metriReali = parseFloat(cavo.metratura_reale?.toString() || '0') || 0\n        const metriTeorici = parseFloat(cavo.metri_teorici?.toString() || '0') || 0\n\n        // Logica ESATTA della webapp originale\n        const isNotInstalled = !(cavo.stato_installazione === 'Installato' || metriReali > 0)\n        const isNotSpare = cavo.modificato_manualmente !== 3\n        const hasTheoreticalMeters = metriTeorici > 0\n\n        const isAvailable = isNotInstalled && isNotSpare && metriReali === 0 && hasTheoreticalMeters\n\n        return isAvailable\n      })\n\n      // Compatibilità ESATTA come webapp originale\n      const compatibili = caviDisponibili.filter(cavo => {\n        const isCompatible = cavo.tipologia === bobina.tipologia &&\n                           String(cavo.sezione) === String(bobina.sezione)\n\n        console.log('🔍 AggiungiCaviDialogSimple: Controllo compatibilità:', {\n          cavoTip: cavo.tipologia,\n          bobinaTip: bobina.tipologia,\n          cavoSez: cavo.sezione,\n          bobinaSez: bobina.sezione,\n          cavoTipType: typeof cavo.tipologia,\n          cavoSezType: typeof cavo.sezione,\n          tipMatch: cavo.tipologia === bobina.tipologia,\n          sezMatch: String(cavo.sezione) === String(bobina.sezione),\n          cavoSezioneString: String(cavo.sezione),\n          bobinaSezioneString: String(bobina.sezione),\n          isCompatible\n        })\n\n        return isCompatible\n      })\n\n      const incompatibili = caviDisponibili.filter(cavo =>\n        !(cavo.tipologia === bobina.tipologia && String(cavo.sezione) === String(bobina.sezione))\n      )\n\n      if (compatibili.length > 0) {\n      }\n\n      setCaviCompatibili(compatibili)\n      setCaviIncompatibili(incompatibili)\n    } catch (error: any) {\n      onError('Errore nel caricamento dei cavi: ' + (error.message || 'Errore sconosciuto'))\n    } finally {\n      setCaviLoading(false)\n    }\n  }\n\n  // Reset quando si apre il dialog\n  useEffect(() => {\n    console.log('🔄 AggiungiCaviDialogSimple: Dialog reset:', {\n      open,\n      bobina: !!bobina,\n      cantiereId,\n      bobinaData: bobina\n    })\n\n    if (!open) {\n      return\n    }\n\n    if (!bobina) {\n      return\n    }\n\n    if (!cantiereId || cantiereId <= 0) {\n      return\n    }\n\n    setCaviSelezionati([])\n    setCaviMetri({})\n    setErrors({})\n    loadCavi()\n  }, [open, bobina, cantiereId])\n\n  // Gestisce la selezione di un cavo\n  const handleCavoToggle = (cavo: CavoConMetri, isCompatible: boolean) => {\n    console.log('🎯 AggiungiCaviDialogSimple: Toggle cavo:', {\n      cavoId: cavo.id_cavo,\n      isCompatible,\n      metriTeorici: cavo.metri_teorici\n    })\n\n    const isSelected = caviSelezionati.some(c => c.id_cavo === cavo.id_cavo)\n\n    if (isSelected) {\n      // Rimuovi dalla selezione\n      setCaviSelezionati(prev => prev.filter(c => c.id_cavo !== cavo.id_cavo))\n      setCaviMetri(prev => {\n        const newMetri = { ...prev }\n        delete newMetri[cavo.id_cavo]\n        return newMetri\n      })\n    } else {\n      // Aggiungi alla selezione con flag di incompatibilità\n      const cavoConFlag = { ...cavo, _isIncompatible: !isCompatible }\n      setCaviSelezionati(prev => [...prev, cavoConFlag])\n\n      // Imposta sempre 0 come default - l'utente deve inserire i metri manualmente\n      setCaviMetri(prev => ({\n        ...prev,\n        [cavo.id_cavo]: '0'\n      }))\n    }\n  }\n\n  // Gestisce il cambio dei metri - SEMPLIFICATO\n  const handleMetriChange = (cavoId: string, value: string) => {\n    const metriResiduiBobina = bobina?.metri_residui || 0\n    const metriInseriti = parseFloat(value || '0')\n\n    // Trova il cavo per verificare se è incompatibile\n    const cavo = caviSelezionati.find(c => c.id_cavo === cavoId)\n    const isIncompatible = cavo?._isIncompatible || false\n\n    // LOGICA SEMPLICE: OVER solo quando UN SINGOLO CAVO supera i residui totali della bobina\n    // NON bloccare l'input, lascia che l'utente inserisca quello che vuole\n    // Il controllo OVER sarà fatto solo al momento del salvataggio\n\n    // Performance: Rimosso console.log in produzione\n\n    // Rimuovi eventuali errori precedenti\n    setErrors(prev => {\n      const newErrors = { ...prev }\n      delete newErrors[cavoId]\n      return newErrors\n    })\n\n    // Aggiorna i metri normalmente - NESSUN BLOCCO\n    setCaviMetri(prev => ({\n      ...prev,\n      [cavoId]: value\n    }))\n  }\n\n  // Funzioni di selezione intelligente\n  const handleSelectAll = (cavi: CavoConMetri[], isCompatible: boolean) => {\n\n    // Aggiungi tutti i cavi\n    const caviConFlag = cavi.map(cavo => ({ ...cavo, _isIncompatible: !isCompatible }))\n    setCaviSelezionati(prev => [...prev, ...caviConFlag])\n\n    // Imposta sempre 0 per tutti - l'utente deve inserire i metri manualmente\n    const nuoviMetri = cavi.reduce((acc, cavo) => ({\n      ...acc,\n      [cavo.id_cavo]: '0'\n    }), {})\n\n    setCaviMetri(prev => ({ ...prev, ...nuoviMetri }))\n  }\n\n  const handleDeselectAll = () => {\n    setCaviSelezionati([])\n    setCaviMetri({})\n    setErrors({})\n  }\n\n  const handleSelectOptimal = () => {\n    // Seleziona automaticamente i cavi compatibili (l'utente inserirà i metri)\n    const caviCompatibili = filteredAndSortedCavi.compatibiliFiltrati\n\n    if (caviCompatibili.length === 0) {\n      return\n    }\n\n    setCaviSelezionati(caviCompatibili)\n\n    const nuoviMetri = caviCompatibili.reduce((acc, cavo) => ({\n      ...acc,\n      [cavo.id_cavo]: '0'\n    }), {})\n\n    setCaviMetri(nuoviMetri)\n  }\n\n  // Calcola la logica progressiva OVER - OTTIMIZZATO con useMemo\n  const progressiveCalculation = useMemo(() => {\n    let metriResiduiSimulati = bobina?.metri_residui || 0\n    let bobinaGiaOver = false\n    const caviValidi: string[] = []\n    const caviBloccati: string[] = []\n    let cavoCheCausaOver: string | null = null\n\n    // Ordina i cavi per mantenere l'ordine di selezione\n    const caviOrdinati = caviSelezionati\n\n    for (const cavo of caviOrdinati) {\n      const metri = parseFloat(caviMetri[cavo.id_cavo] || '0')\n\n      if (metri > 0) {\n        // Se la bobina è già OVER, blocca tutti i cavi successivi\n        if (bobinaGiaOver) {\n          caviBloccati.push(cavo.id_cavo)\n        } else if (metriResiduiSimulati - metri < 0) {\n          // Questo cavo causa OVER: salvalo e marca bobina come OVER\n          caviValidi.push(cavo.id_cavo)\n          cavoCheCausaOver = cavo.id_cavo // ✅ Identifica IL cavo che causa OVER\n          bobinaGiaOver = true\n          metriResiduiSimulati -= metri\n        } else {\n          // Cavo normale: salvalo\n          caviValidi.push(cavo.id_cavo)\n          metriResiduiSimulati -= metri\n        }\n      } else {\n        // Cavi senza metri (0): sempre validi, non influenzano lo stato OVER\n        caviValidi.push(cavo.id_cavo)\n      }\n    }\n\n    return {\n      metriResiduiSimulati,\n      caviValidi,\n      caviBloccati,\n      bobinaGiaOver,\n      cavoCheCausaOver // ✅ Solo UN cavo può causare OVER\n    }\n  }, [caviSelezionati, caviMetri, bobina?.metri_residui])\n\n  // Funzione helper per compatibilità\n  const calculateProgressiveMeters = () => progressiveCalculation\n\n  // Gestisce il salvataggio\n  const handleSave = async () => {\n    if (!cantiereId || !bobina) return\n\n    // VALIDAZIONI PRELIMINARI\n    if (caviSelezionati.length === 0) {\n      onError('Nessun cavo selezionato')\n      return\n    }\n\n    // Verifica che tutti i cavi abbiano metri inseriti (accetta anche 0)\n    const missingMetri = caviSelezionati.filter(cavo => {\n      const metri = caviMetri[cavo.id_cavo]\n      return !metri || metri.trim() === '' || isNaN(parseFloat(metri)) || parseFloat(metri) < 0\n    })\n\n    if (missingMetri.length > 0) {\n      onError(`Metri posati mancanti o non validi per: ${missingMetri.map(c => c.id_cavo).join(', ')}`)\n      return\n    }\n\n    try {\n      setSaving(true)\n\n      // Calcola cavi validi (non bloccati)\n      const { caviValidi, caviBloccati } = calculateProgressiveMeters()\n\n      // Filtra solo i cavi NON bloccati con metri inseriti\n      const caviDaSalvare = caviSelezionati.filter(cavo => {\n        const metri = parseFloat(caviMetri[cavo.id_cavo] || '0')\n        const isBlocked = caviBloccati.includes(cavo.id_cavo)\n        return metri > 0 && !isBlocked\n      })\n\n      if (caviDaSalvare.length === 0) {\n        onError('Nessun cavo valido da salvare (tutti bloccati o senza metri)')\n        return\n      }\n\n      console.log('💾 AggiungiCaviDialogSimple: Preparazione salvataggio:', {\n        caviSelezionati: caviSelezionati.length,\n        caviValidi: caviValidi.length,\n        caviBloccati: caviBloccati.length,\n        caviDaSalvare: caviDaSalvare.length\n      })\n\n      // Aggiorna ogni cavo valido\n      const results = []\n      const errors = []\n      let metriResiduiCorrente = bobina?.metri_residui || 0\n\n      for (const cavo of caviDaSalvare) {\n        try {\n          const metriPosatiInput = caviMetri[cavo.id_cavo]\n          const metriPosati = parseFloat(metriPosatiInput)\n\n          // LOGICA OVER PROGRESSIVA:\n          const causaOver = metriResiduiCorrente - metriPosati < 0\n          const isIncompatible = cavo._isIncompatible || false\n          const needsForceOver = causaOver || isIncompatible\n\n          console.log('⚡ AggiungiCaviDialogSimple: Aggiornamento cavo:', {\n            metriPosati,\n            metriResiduiCorrente,\n            causaOver,\n            isIncompatible,\n            needsForceOver\n          })\n\n          // Chiama l'API con force_over se necessario\n          await caviApi.updateMetriPosati(\n            cantiereId,\n            cavo.id_cavo,\n            metriPosati,\n            bobina.id_bobina,\n            needsForceOver\n          )\n\n          // Aggiorna metri residui per il prossimo cavo\n          metriResiduiCorrente -= metriPosati\n\n          results.push({\n            cavo: cavo.id_cavo,\n            metriPosati,\n            success: true,\n            wasIncompatible: cavo._isIncompatible,\n            wasForceOver: needsForceOver\n          })\n        } catch (error: any) {\n\n          // Gestione errori specifici\n          let errorMessage = 'Errore sconosciuto'\n\n          if (error.response) {\n            // Errore HTTP con risposta dal server\n            const status = error.response.status\n            const data = error.response.data\n\n            if (status === 400) {\n              errorMessage = data?.message || data?.error || 'Richiesta non valida (400)'\n            } else if (status === 404) {\n              errorMessage = 'Cavo o bobina non trovati (404)'\n            } else if (status === 409) {\n              errorMessage = 'Conflitto: cavo già assegnato o bobina non disponibile (409)'\n            } else if (status === 500) {\n              errorMessage = 'Errore interno del server (500)'\n            } else {\n              errorMessage = `Errore HTTP ${status}: ${data?.message || data?.error || 'Errore del server'}`\n            }\n          } else if (error.request) {\n            // Errore di rete\n            errorMessage = 'Errore di connessione al server'\n          } else {\n            // Errore di validazione locale\n            errorMessage = error.message || 'Errore di validazione'\n          }\n\n          errors.push({\n            cavo: cavo.id_cavo,\n            error: errorMessage\n          })\n        }\n      }\n\n      // Gestione del risultato con dettagli\n      if (errors.length === 0) {\n        const incompatibleCount = results.filter(r => r.wasIncompatible).length\n        const forceOverCount = results.filter(r => r.wasForceOver).length\n\n        let message = `${results.length} cavi aggiornati con successo`\n        if (incompatibleCount > 0) {\n          message += ` (${incompatibleCount} incompatibili)`\n        }\n        if (forceOverCount > 0) {\n          message += ` (${forceOverCount} con force_over)`\n        }\n\n        onSuccess(message)\n        onClose()\n      } else {\n        onError(`Errori: ${errors.map(e => `${e.cavo}: ${e.error}`).join(', ')}`)\n      }\n    } catch (error: any) {\n\n      let errorMessage = 'Errore durante il salvataggio dei cavi'\n\n      if (error.response) {\n        const status = error.response.status\n        const data = error.response.data\n\n        if (status === 400) {\n          errorMessage = `Errore di validazione: ${data?.message || data?.error || 'Dati non validi'}`\n        } else if (status === 401) {\n          errorMessage = 'Sessione scaduta. Effettua nuovamente il login.'\n        } else if (status === 403) {\n          errorMessage = 'Non hai i permessi per questa operazione'\n        } else if (status === 500) {\n          errorMessage = 'Errore interno del server. Riprova più tardi.'\n        } else {\n          errorMessage = `Errore del server (${status}): ${data?.message || data?.error || 'Errore sconosciuto'}`\n        }\n      } else if (error.request) {\n        errorMessage = 'Impossibile contattare il server. Verifica la connessione.'\n      } else {\n        errorMessage = error.message || 'Errore imprevisto durante il salvataggio'\n      }\n\n      onError(errorMessage)\n    } finally {\n      setSaving(false)\n    }\n  }\n\n  const handleClose = () => {\n    if (!saving) {\n      setCaviSelezionati([])\n      setCaviMetri({})\n      setErrors({})\n      onClose()\n    }\n  }\n\n  if (!bobina) return null\n\n  const getBobinaNumber = (idBobina: string) => {\n    const match = idBobina.match(/C\\d+_B(\\d+)/)\n    return match ? match[1] : idBobina\n  }\n\n  // Renderizza la paginazione\n  const renderPagination = () => {\n    if (paginatedCavi.totalPages <= 1) return null\n\n    const pages = []\n    const maxVisiblePages = 5\n    let startPage = Math.max(1, currentPage - Math.floor(maxVisiblePages / 2))\n    let endPage = Math.min(paginatedCavi.totalPages, startPage + maxVisiblePages - 1)\n\n    if (endPage - startPage + 1 < maxVisiblePages) {\n      startPage = Math.max(1, endPage - maxVisiblePages + 1)\n    }\n\n    return (\n      <div className=\"flex items-center justify-center gap-2 mt-4\">\n        <Button\n          variant=\"outline\"\n          size=\"sm\"\n          onClick={() => setCurrentPage(1)}\n          disabled={currentPage === 1}\n        >\n          Prima\n        </Button>\n        <Button\n          variant=\"outline\"\n          size=\"sm\"\n          onClick={() => setCurrentPage(currentPage - 1)}\n          disabled={currentPage === 1}\n        >\n          Prec\n        </Button>\n\n        {startPage > 1 && (\n          <>\n            <Button variant=\"outline\" size=\"sm\" onClick={() => setCurrentPage(1)}>1</Button>\n            {startPage > 2 && <span className=\"text-gray-400\">...</span>}\n          </>\n        )}\n\n        {Array.from({ length: endPage - startPage + 1 }, (_, i) => startPage + i).map(page => (\n          <Button\n            key={page}\n            variant={currentPage === page ? \"default\" : \"outline\"}\n            size=\"sm\"\n            onClick={() => setCurrentPage(page)}\n          >\n            {page}\n          </Button>\n        ))}\n\n        {endPage < paginatedCavi.totalPages && (\n          <>\n            {endPage < paginatedCavi.totalPages - 1 && <span className=\"text-gray-400\">...</span>}\n            <Button variant=\"outline\" size=\"sm\" onClick={() => setCurrentPage(paginatedCavi.totalPages)}>\n              {paginatedCavi.totalPages}\n            </Button>\n          </>\n        )}\n\n        <Button\n          variant=\"outline\"\n          size=\"sm\"\n          onClick={() => setCurrentPage(currentPage + 1)}\n          disabled={currentPage === paginatedCavi.totalPages}\n        >\n          Succ\n        </Button>\n        <Button\n          variant=\"outline\"\n          size=\"sm\"\n          onClick={() => setCurrentPage(paginatedCavi.totalPages)}\n          disabled={currentPage === paginatedCavi.totalPages}\n        >\n          Ultima\n        </Button>\n\n        <span className=\"text-sm text-gray-600 ml-4\">\n          Pagina {currentPage} di {paginatedCavi.totalPages}\n        </span>\n      </div>\n    )\n  }\n\n  // Renderizza la lista dei cavi - OTTIMIZZATO\n  const renderCaviList = (cavi: CavoConMetri[], isCompatible: boolean) => {\n    console.log('📋 AggiungiCaviDialogSimple: Rendering lista cavi:', {\n      isCompatible,\n      caviLength: cavi.length,\n      primi3Cavi: cavi.slice(0, 3).map(c => ({\n        id: c.id_cavo,\n        tipologia: c.tipologia,\n        sezione: c.sezione\n      }))\n    })\n\n    if (cavi.length === 0) {\n      return (\n        <div className=\"h-[300px] flex items-center justify-center text-gray-500 border rounded\">\n          <div className=\"text-center\">\n            <Cable className=\"h-8 w-8 mx-auto mb-2 opacity-50\" />\n            <div>Nessun cavo {isCompatible ? 'compatibile' : 'incompatibile'} disponibile</div>\n            <div className=\"text-xs mt-2 text-gray-400\">\n              {isCompatible\n                ? `Cerca cavi con tipologia \"${bobina?.tipologia}\" e formazione \"${bobina?.sezione}\"`\n                : 'I cavi incompatibili hanno tipologia o formazione diverse'\n              }\n            </div>\n          </div>\n        </div>\n      )\n    }\n\n    return (\n      <div className=\"space-y-1 h-[300px] overflow-y-auto border rounded p-2 w-full\">\n        {cavi.map((cavo, index) => {\n          const isSelected = caviSelezionati.some(c => c.id_cavo === cavo.id_cavo)\n          const metri = caviMetri[cavo.id_cavo] || ''\n\n          // Usa il calcolo ottimizzato\n          const { caviBloccati, cavoCheCausaOver } = progressiveCalculation\n          const isBlocked = isSelected && caviBloccati.includes(cavo.id_cavo)\n          const causaOver = isSelected && cavo.id_cavo === cavoCheCausaOver // ✅ Solo IL cavo che causa OVER\n\n          return (\n            <div\n              key={cavo.id_cavo}\n              className={`border rounded px-3 py-2 transition-colors ${\n                isBlocked\n                  ? 'border-red-300 bg-red-50'\n                  : causaOver\n                  ? 'border-orange-300 bg-orange-50'\n                  : isSelected\n                  ? 'border-blue-500 bg-blue-50'\n                  : 'border-gray-200 hover:border-gray-300 hover:bg-gray-50'\n              }`}\n            >\n              {/* Linea principale del cavo - RESPONSIVE */}\n              <div className=\"flex items-center gap-2 w-full overflow-hidden\">\n                {/* Checkbox */}\n                <input\n                  type=\"checkbox\"\n                  checked={isSelected}\n                  onChange={(e) => {\n                    handleCavoToggle(cavo, isCompatible)\n                  }}\n                  className=\"h-4 w-4 text-blue-600 border-gray-300 rounded flex-shrink-0\"\n                />\n\n                {/* Info cavo responsive */}\n                <div className=\"flex items-center gap-2 flex-1 min-w-0 overflow-hidden\">\n                  <span className=\"font-medium text-gray-900 flex-shrink-0\">{cavo.id_cavo}</span>\n                  <span className=\"text-xs bg-gray-100 px-1.5 py-0.5 rounded flex-shrink-0\">{cavo.tipologia}</span>\n                  <span className=\"text-xs bg-gray-100 px-1.5 py-0.5 rounded flex-shrink-0\">{cavo.sezione}</span>\n                  <span className=\"text-xs text-gray-600 flex-shrink-0\">{cavo.metri_teorici}m</span>\n\n                  {/* Badge stato OVER */}\n                  {isBlocked && (\n                    <span className=\"text-xs bg-red-100 text-red-700 px-1.5 py-0.5 rounded flex-shrink-0 font-medium\">\n                      BLOCCATO\n                    </span>\n                  )}\n                  {causaOver && (\n                    <span className=\"text-xs bg-orange-100 text-orange-700 px-1.5 py-0.5 rounded flex-shrink-0 font-medium\">\n                      CAUSA OVER\n                    </span>\n                  )}\n\n                  <span className=\"text-xs text-gray-500 truncate min-w-0\">\n                    {cavo.ubicazione_partenza || 'N/A'} → {cavo.ubicazione_arrivo || 'N/A'}\n                  </span>\n                </div>\n\n                {/* Input metri quando selezionato */}\n                {isSelected && (\n                  <div className=\"flex items-center gap-1 flex-shrink-0\">\n                    <label className=\"text-xs text-gray-600\">m:</label>\n                    <input\n                      type=\"number\"\n                      step=\"0.1\"\n                      min=\"0\"\n                      value={metri}\n                      onChange={(e) => {\n                        handleMetriChange(cavo.id_cavo, e.target.value)\n                      }}\n                      className=\"w-16 px-1 py-1 border rounded text-xs\"\n                      placeholder=\"0\"\n                    />\n                  </div>\n                )}\n              </div>\n\n              {/* Errori se presenti */}\n              {errors[cavo.id_cavo] && (\n                <div className=\"text-red-600 text-xs mt-1 ml-7\">{errors[cavo.id_cavo]}</div>\n              )}\n            </div>\n          )\n        })}\n      </div>\n    )\n  }\n\n  return (\n    <>\n      <Dialog open={open} onOpenChange={handleClose}>\n      <DialogContent\n        className=\"h-[85vh] w-full max-w-5xl overflow-hidden\"\n        style={{\n          width: '950px !important',\n          maxWidth: '95vw !important',\n          minWidth: '850px'\n        }}\n      >\n        <DialogHeader className=\"pb-0\">\n          <DialogTitle className=\"flex items-center gap-2 mb-0 text-lg\">\n            <Cable className=\"h-5 w-5\" />\n            🔥 NUOVO SISTEMA OVER - Aggiungi cavi alla bobina {getBobinaNumber(bobina.id_bobina)}\n          </DialogTitle>\n          <DialogDescription className=\"mb-0 text-xs text-gray-600 mt-0\">\n            Seleziona cavi e inserisci metri posati (SISTEMA AGGIORNATO)\n          </DialogDescription>\n        </DialogHeader>\n\n        <div className=\"space-y-1 mt-2\">\n          {/* Informazioni bobina - ULTRA COMPATTO */}\n          <div className=\"bg-gray-50 px-3 py-1 rounded text-sm\">\n            <div className=\"flex items-center justify-between\">\n              <div className=\"flex items-center gap-3\">\n                <span><strong>Bobina {getBobinaNumber(bobina.id_bobina)}</strong> • {bobina.tipologia} • {bobina.sezione}</span>\n                <span>Residui: <strong>{bobina.metri_residui}m</strong></span>\n              </div>\n              <div className=\"flex items-center gap-2\">\n                <span>Selezionati: <strong>{caviSelezionati.length}</strong> cavi • <strong>{calculations.metriTotaliSelezionati.toFixed(1)}m</strong></span>\n                {calculations.isOverState && (\n                  <span className=\"text-red-600 font-medium\">OVER!</span>\n                )}\n              </div>\n            </div>\n          </div>\n\n          {/* Ricerca compatta */}\n          <div className=\"flex gap-2 items-center\">\n            <div className=\"flex-1 relative\">\n              <Search className=\"absolute left-2 top-2 h-4 w-4 text-gray-400\" />\n              <Input\n                placeholder=\"Cerca cavi...\"\n                value={searchTerm}\n                onChange={(e) => setSearchTerm(e.target.value)}\n                className=\"pl-8 h-8 text-sm\"\n              />\n            </div>\n            <Button\n              variant=\"outline\"\n              size=\"sm\"\n              onClick={handleDeselectAll}\n              disabled={caviSelezionati.length === 0}\n              className=\"h-8 px-3 text-sm\"\n            >\n              Reset\n            </Button>\n          </div>\n\n          {/* Loading */}\n          {caviLoading && (\n            <div className=\"flex items-center justify-center py-8\">\n              <Loader2 className=\"h-6 w-6 animate-spin mr-2\" />\n              <span>Caricamento cavi...</span>\n            </div>\n          )}\n\n          {/* Lista cavi con Tab stile Admin */}\n          {!caviLoading && (\n            <Tabs defaultValue=\"compatibili\" className=\"w-full\">\n              <TabsList className=\"flex justify-center gap-6 bg-transparent border-0 h-auto p-0\">\n                <TabsTrigger\n                  value=\"compatibili\"\n                  className=\"tab-trigger flex items-center gap-2\"\n                >\n                  <span className=\"w-2 h-2 rounded-full bg-green-500\"></span>\n                  Cavi Compatibili ({paginatedCavi.totalCompatibili})\n                </TabsTrigger>\n                <TabsTrigger\n                  value=\"incompatibili\"\n                  className=\"tab-trigger flex items-center gap-2\"\n                >\n                  <span className=\"w-2 h-2 rounded-full bg-orange-500\"></span>\n                  Cavi Incompatibili ({paginatedCavi.totalIncompatibili})\n                </TabsTrigger>\n              </TabsList>\n\n              <TabsContent value=\"compatibili\" className=\"mt-4 w-full overflow-hidden\">\n                <div className=\"w-full overflow-hidden\">\n                  {renderCaviList(paginatedCavi.compatibili, true)}\n                </div>\n              </TabsContent>\n\n              <TabsContent value=\"incompatibili\" className=\"mt-4 w-full overflow-hidden\">\n                <div className=\"w-full overflow-hidden\">\n                  {renderCaviList(paginatedCavi.incompatibili, false)}\n                </div>\n              </TabsContent>\n            </Tabs>\n          )}\n        </div>\n\n        <DialogFooter className=\"flex justify-between items-center\">\n          <div className=\"text-sm text-gray-600\">\n            {caviSelezionati.length > 0 ? (\n              (() => {\n                const { metriResiduiSimulati, caviValidi, caviBloccati, cavoCheCausaOver } = progressiveCalculation\n                const metriTotali = caviSelezionati.reduce((sum, cavo) => {\n                  const metri = parseFloat(caviMetri[cavo.id_cavo] || '0')\n                  return sum + metri\n                }, 0)\n                const metriUsati = (bobina?.metri_residui || 0) - metriResiduiSimulati\n                const incompatibili = caviSelezionati.filter(c => c._isIncompatible).length\n\n                return (\n                  <div className=\"space-y-1\">\n                    <div>\n                      {caviSelezionati.length} cavi selezionati • {metriTotali.toFixed(1)}m totali\n                    </div>\n                    <div>\n                      ✅ {caviValidi.length} salvabili • ❌ {caviBloccati.length} bloccati\n                    </div>\n                    <div>\n                      Usati: {metriUsati.toFixed(1)}m / {bobina?.metri_residui || 0}m\n                      {metriResiduiSimulati < 0 && cavoCheCausaOver && (\n                        <span className=\"text-orange-600 font-medium ml-2\">\n                          (OVER da {cavoCheCausaOver}: +{Math.abs(metriResiduiSimulati).toFixed(1)}m)\n                        </span>\n                      )}\n                    </div>\n                    {incompatibili > 0 && (\n                      <div className=\"text-orange-600 font-medium\">\n                        ⚠️ {incompatibili} cavi incompatibili\n                      </div>\n                    )}\n                  </div>\n                )\n              })()\n            ) : (\n              <div>Nessun cavo selezionato</div>\n            )}\n          </div>\n\n          <div className=\"flex gap-2\">\n            <Button variant=\"outline\" onClick={handleClose} disabled={saving}>\n              Annulla\n            </Button>\n            <Button\n              onClick={handleSave}\n              disabled={saving || caviSelezionati.length === 0}\n              className={calculations.isOverState ? 'bg-orange-600 hover:bg-orange-700' : ''}\n            >\n              {saving && <Loader2 className=\"mr-2 h-4 w-4 animate-spin\" />}\n              {saving ? 'Salvataggio...' :\n               calculations.isOverState ? `Salva ${caviSelezionati.length} cavi (OVER)` :\n               `Salva ${caviSelezionati.length} cavi`}\n            </Button>\n          </div>\n        </DialogFooter>\n      </DialogContent>\n    </Dialog>\n    </>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;;;;;;;;;;;;;;;CAeC,GAED;AACA;AAQA;AACA;AAEA;AAMA;AAAA;AAAA;AAYA;AAjDA;;;;;;;;;AAqDA,gCAAgC;AAChC,MAAM,cAAc,CAAC,OAAe;IAClC,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAErD,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,MAAM,UAAU,WAAW;YACzB,kBAAkB;QACpB,GAAG;QAEH,OAAO;YACL,aAAa;QACf;IACF,GAAG;QAAC;QAAO;KAAM;IAEjB,OAAO;AACT;AAee,SAAS,yBAAyB,EAC/C,IAAI,EACJ,OAAO,EACP,MAAM,EACN,UAAU,EACV,SAAS,EACT,OAAO,EACiB;IACxB,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACvC,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC/C,MAAM,CAAC,QAAQ,UAAU,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAErC,mBAAmB;IACnB,MAAM,CAAC,iBAAiB,mBAAmB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAkB,EAAE;IACzE,MAAM,CAAC,mBAAmB,qBAAqB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAkB,EAAE;IAC7E,MAAM,CAAC,iBAAiB,mBAAmB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAkB,EAAE;IACzE,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAA0B,CAAC;IAEpE,wBAAwB;IACxB,MAAM,CAAC,QAAQ,UAAU,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAA0B,CAAC;IAE9D,sCAAsC;IACtC,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC7C,MAAM,sBAAsB,YAAY,YAAY,KAAK,iBAAiB;;IAC1E,MAAM,CAAC,QAAQ,UAAU,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAqE;IACxG,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAkB;IAC3D,MAAM,CAAC,iBAAiB,mBAAmB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAU;IAC/D,MAAM,CAAC,kBAAkB,oBAAoB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAU;IACjE,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACrD,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACrD,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC/C,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAEjD,+DAA+D;IAC/D,MAAM,wBAAwB,CAAA,GAAA,qMAAA,CAAA,UAAO,AAAD,EAAE;QACpC,MAAM,aAAa,CAAC;YAClB,OAAO,KAAK,MAAM,CAAC,CAAA;gBACjB,4CAA4C;gBAC5C,MAAM,gBAAgB,CAAC,uBACrB,KAAK,OAAO,CAAC,WAAW,GAAG,QAAQ,CAAC,oBAAoB,WAAW,OACnE,KAAK,SAAS,EAAE,cAAc,SAAS,oBAAoB,WAAW,OACtE,KAAK,mBAAmB,EAAE,cAAc,SAAS,oBAAoB,WAAW,OAChF,KAAK,iBAAiB,EAAE,cAAc,SAAS,oBAAoB,WAAW;gBAEhF,mBAAmB;gBACnB,MAAM,mBAAmB,oBAAoB,SAAS,KAAK,SAAS,KAAK;gBAEzE,oBAAoB;gBACpB,MAAM,oBAAoB,qBAAqB,SAAS,KAAK,OAAO,KAAK;gBAEzE,uBAAuB;gBACvB,MAAM,eAAe,WAAW,KAAK,aAAa,EAAE,cAAc;gBAClE,MAAM,kBAAkB,CAAC,kBAAkB,gBAAgB,WAAW;gBACtE,MAAM,kBAAkB,CAAC,kBAAkB,gBAAgB,WAAW;gBAEtE,OAAO,iBAAiB,oBAAoB,qBAAqB,mBAAmB;YACtF;QACF;QAEA,MAAM,WAAW,CAAC;YAChB,OAAO;mBAAI;aAAK,CAAC,IAAI,CAAC,CAAC,GAAG;gBACxB,IAAI,QAAa;gBAEjB,OAAQ;oBACN,KAAK;wBACH,SAAS,EAAE,OAAO;wBAClB,SAAS,EAAE,OAAO;wBAClB;oBACF,KAAK;wBACH,SAAS,WAAW,EAAE,aAAa,EAAE,cAAc;wBACnD,SAAS,WAAW,EAAE,aAAa,EAAE,cAAc;wBACnD;oBACF,KAAK;wBACH,SAAS,EAAE,SAAS,IAAI;wBACxB,SAAS,EAAE,SAAS,IAAI;wBACxB;oBACF,KAAK;wBACH,SAAS,EAAE,mBAAmB,IAAI;wBAClC,SAAS,EAAE,mBAAmB,IAAI;wBAClC;oBACF;wBACE,OAAO;gBACX;gBAEA,IAAI,OAAO,WAAW,UAAU;oBAC9B,MAAM,aAAa,OAAO,aAAa,CAAC;oBACxC,OAAO,cAAc,QAAQ,aAAa,CAAC;gBAC7C,OAAO;oBACL,OAAO,cAAc,QAAQ,SAAS,SAAS,SAAS;gBAC1D;YACF;QACF;QAEA,MAAM,sBAAsB,SAAS,WAAW;QAChD,MAAM,wBAAwB,SAAS,WAAW;QAElD,OAAO;YAAE;YAAqB;QAAsB;IACtD,GAAG;QAAC;QAAiB;QAAmB;QAAqB;QAAQ;QAAW;QAAiB;QAAkB;QAAgB;KAAe;IAElJ,6BAA6B;IAC7B,MAAM,gBAAgB,CAAA,GAAA,qMAAA,CAAA,UAAO,AAAD,EAAE;QAC5B,MAAM,EAAE,mBAAmB,EAAE,qBAAqB,EAAE,GAAG;QAEvD,MAAM,aAAa,CAAC,cAAc,CAAC,IAAI;QACvC,MAAM,WAAW,aAAa;QAE9B,OAAO;YACL,aAAa,oBAAoB,KAAK,CAAC,YAAY;YACnD,eAAe,sBAAsB,KAAK,CAAC,YAAY;YACvD,kBAAkB,oBAAoB,MAAM;YAC5C,oBAAoB,sBAAsB,MAAM;YAChD,YAAY,KAAK,IAAI,CAAC,KAAK,GAAG,CAAC,oBAAoB,MAAM,EAAE,sBAAsB,MAAM,IAAI;QAC7F;IACF,GAAG;QAAC;QAAuB;QAAa;KAAa;IAErD,sCAAsC;IACtC,MAAM,eAAe,CAAA,GAAA,qMAAA,CAAA,UAAO,AAAD,EAAE;QAC3B,MAAM,yBAAyB,OAAO,MAAM,CAAC,WAAW,MAAM,CAAC,CAAC,KAAK,QACnE,MAAM,WAAW,SAAS,MAAM;QAElC,MAAM,qBAAqB,QAAQ,iBAAiB;QAEpD,yCAAyC;QACzC,yEAAyE;QACzE,MAAM,oBAAoB,OAAO,OAAO,CAAC,WAAW,IAAI,CAAC,CAAC,CAAC,QAAQ,MAAM;YACvE,MAAM,gBAAgB,WAAW,SAAS;YAC1C,OAAO,gBAAgB,mBAAmB,iCAAiC;;QAC7E;QAEA,uDAAuD;QACvD,MAAM,sBAAsB,gBAAgB,IAAI,CAAC,CAAA,IAAK,EAAE,eAAe;QAEvE,+EAA+E;QAC/E,MAAM,cAAc,qBAAsB,QAAQ,iBAAiB;QAEnE,MAAM,iBAAiB,KAAK,GAAG,CAAC,GAAG,yBAAyB;QAC5D,MAAM,sBAAsB,qBAAqB,IAAI,AAAC,yBAAyB,qBAAsB,MAAM;QAE3G,OAAO;YACL;YACA;YACA;YACA;YACA;YACA;YACA;QACF;IACF,GAAG;QAAC;QAAW;QAAQ;KAAgB;IAEvC,sCAAsC;IACtC,MAAM,kBAAkB,CAAA,GAAA,qMAAA,CAAA,UAAO,AAAD,EAAE;QAC9B,MAAM,YAAY,IAAI,IAAI;eAAI;eAAoB;SAAkB,CAAC,GAAG,CAAC,CAAA,IAAK,EAAE,SAAS,EAAE,MAAM,CAAC;QAClG,OAAO,MAAM,IAAI,CAAC,WAAW,IAAI;IACnC,GAAG;QAAC;QAAiB;KAAkB;IAEvC,MAAM,mBAAmB,CAAA,GAAA,qMAAA,CAAA,UAAO,AAAD,EAAE;QAC/B,MAAM,aAAa,IAAI,IAAI;eAAI;eAAoB;SAAkB,CAAC,GAAG,CAAC,CAAA,IAAK,EAAE,OAAO,EAAE,MAAM,CAAC;QACjG,OAAO,MAAM,IAAI,CAAC,YAAY,IAAI;IACpC,GAAG;QAAC;QAAiB;KAAkB;IAEvC,4BAA4B;IAC5B,MAAM,WAAW;QACf,IAAI,CAAC,cAAc,CAAC,QAAQ;YAC1B;QACF;QAEA,IAAI;YACF,eAAe;YACf,MAAM,WAAW,MAAM,iHAAA,CAAA,UAAO,CAAC,OAAO,CAAC;YAEvC,sCAAsC;YACtC,MAAM,kBAAkB,SAAS,MAAM,CAAC,CAAA;gBACtC,MAAM,aAAa,WAAW,KAAK,eAAe,EAAE,cAAc,QAAQ;gBAC1E,MAAM,eAAe,WAAW,KAAK,aAAa,EAAE,cAAc,QAAQ;gBAE1E,uCAAuC;gBACvC,MAAM,iBAAiB,CAAC,CAAC,KAAK,mBAAmB,KAAK,gBAAgB,aAAa,CAAC;gBACpF,MAAM,aAAa,KAAK,sBAAsB,KAAK;gBACnD,MAAM,uBAAuB,eAAe;gBAE5C,MAAM,cAAc,kBAAkB,cAAc,eAAe,KAAK;gBAExE,OAAO;YACT;YAEA,6CAA6C;YAC7C,MAAM,cAAc,gBAAgB,MAAM,CAAC,CAAA;gBACzC,MAAM,eAAe,KAAK,SAAS,KAAK,OAAO,SAAS,IACrC,OAAO,KAAK,OAAO,MAAM,OAAO,OAAO,OAAO;gBAEjE,QAAQ,GAAG,CAAC,yDAAyD;oBACnE,SAAS,KAAK,SAAS;oBACvB,WAAW,OAAO,SAAS;oBAC3B,SAAS,KAAK,OAAO;oBACrB,WAAW,OAAO,OAAO;oBACzB,aAAa,OAAO,KAAK,SAAS;oBAClC,aAAa,OAAO,KAAK,OAAO;oBAChC,UAAU,KAAK,SAAS,KAAK,OAAO,SAAS;oBAC7C,UAAU,OAAO,KAAK,OAAO,MAAM,OAAO,OAAO,OAAO;oBACxD,mBAAmB,OAAO,KAAK,OAAO;oBACtC,qBAAqB,OAAO,OAAO,OAAO;oBAC1C;gBACF;gBAEA,OAAO;YACT;YAEA,MAAM,gBAAgB,gBAAgB,MAAM,CAAC,CAAA,OAC3C,CAAC,CAAC,KAAK,SAAS,KAAK,OAAO,SAAS,IAAI,OAAO,KAAK,OAAO,MAAM,OAAO,OAAO,OAAO,CAAC;YAG1F,IAAI,YAAY,MAAM,GAAG,GAAG,CAC5B;YAEA,mBAAmB;YACnB,qBAAqB;QACvB,EAAE,OAAO,OAAY;YACnB,QAAQ,sCAAsC,CAAC,MAAM,OAAO,IAAI,oBAAoB;QACtF,SAAU;YACR,eAAe;QACjB;IACF;IAEA,iCAAiC;IACjC,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,QAAQ,GAAG,CAAC,8CAA8C;YACxD;YACA,QAAQ,CAAC,CAAC;YACV;YACA,YAAY;QACd;QAEA,IAAI,CAAC,MAAM;YACT;QACF;QAEA,IAAI,CAAC,QAAQ;YACX;QACF;QAEA,IAAI,CAAC,cAAc,cAAc,GAAG;YAClC;QACF;QAEA,mBAAmB,EAAE;QACrB,aAAa,CAAC;QACd,UAAU,CAAC;QACX;IACF,GAAG;QAAC;QAAM;QAAQ;KAAW;IAE7B,mCAAmC;IACnC,MAAM,mBAAmB,CAAC,MAAoB;QAC5C,QAAQ,GAAG,CAAC,6CAA6C;YACvD,QAAQ,KAAK,OAAO;YACpB;YACA,cAAc,KAAK,aAAa;QAClC;QAEA,MAAM,aAAa,gBAAgB,IAAI,CAAC,CAAA,IAAK,EAAE,OAAO,KAAK,KAAK,OAAO;QAEvE,IAAI,YAAY;YACd,0BAA0B;YAC1B,mBAAmB,CAAA,OAAQ,KAAK,MAAM,CAAC,CAAA,IAAK,EAAE,OAAO,KAAK,KAAK,OAAO;YACtE,aAAa,CAAA;gBACX,MAAM,WAAW;oBAAE,GAAG,IAAI;gBAAC;gBAC3B,OAAO,QAAQ,CAAC,KAAK,OAAO,CAAC;gBAC7B,OAAO;YACT;QACF,OAAO;YACL,sDAAsD;YACtD,MAAM,cAAc;gBAAE,GAAG,IAAI;gBAAE,iBAAiB,CAAC;YAAa;YAC9D,mBAAmB,CAAA,OAAQ;uBAAI;oBAAM;iBAAY;YAEjD,6EAA6E;YAC7E,aAAa,CAAA,OAAQ,CAAC;oBACpB,GAAG,IAAI;oBACP,CAAC,KAAK,OAAO,CAAC,EAAE;gBAClB,CAAC;QACH;IACF;IAEA,8CAA8C;IAC9C,MAAM,oBAAoB,CAAC,QAAgB;QACzC,MAAM,qBAAqB,QAAQ,iBAAiB;QACpD,MAAM,gBAAgB,WAAW,SAAS;QAE1C,kDAAkD;QAClD,MAAM,OAAO,gBAAgB,IAAI,CAAC,CAAA,IAAK,EAAE,OAAO,KAAK;QACrD,MAAM,iBAAiB,MAAM,mBAAmB;QAEhD,yFAAyF;QACzF,uEAAuE;QACvE,+DAA+D;QAE/D,iDAAiD;QAEjD,sCAAsC;QACtC,UAAU,CAAA;YACR,MAAM,YAAY;gBAAE,GAAG,IAAI;YAAC;YAC5B,OAAO,SAAS,CAAC,OAAO;YACxB,OAAO;QACT;QAEA,+CAA+C;QAC/C,aAAa,CAAA,OAAQ,CAAC;gBACpB,GAAG,IAAI;gBACP,CAAC,OAAO,EAAE;YACZ,CAAC;IACH;IAEA,qCAAqC;IACrC,MAAM,kBAAkB,CAAC,MAAsB;QAE7C,wBAAwB;QACxB,MAAM,cAAc,KAAK,GAAG,CAAC,CAAA,OAAQ,CAAC;gBAAE,GAAG,IAAI;gBAAE,iBAAiB,CAAC;YAAa,CAAC;QACjF,mBAAmB,CAAA,OAAQ;mBAAI;mBAAS;aAAY;QAEpD,0EAA0E;QAC1E,MAAM,aAAa,KAAK,MAAM,CAAC,CAAC,KAAK,OAAS,CAAC;gBAC7C,GAAG,GAAG;gBACN,CAAC,KAAK,OAAO,CAAC,EAAE;YAClB,CAAC,GAAG,CAAC;QAEL,aAAa,CAAA,OAAQ,CAAC;gBAAE,GAAG,IAAI;gBAAE,GAAG,UAAU;YAAC,CAAC;IAClD;IAEA,MAAM,oBAAoB;QACxB,mBAAmB,EAAE;QACrB,aAAa,CAAC;QACd,UAAU,CAAC;IACb;IAEA,MAAM,sBAAsB;QAC1B,2EAA2E;QAC3E,MAAM,kBAAkB,sBAAsB,mBAAmB;QAEjE,IAAI,gBAAgB,MAAM,KAAK,GAAG;YAChC;QACF;QAEA,mBAAmB;QAEnB,MAAM,aAAa,gBAAgB,MAAM,CAAC,CAAC,KAAK,OAAS,CAAC;gBACxD,GAAG,GAAG;gBACN,CAAC,KAAK,OAAO,CAAC,EAAE;YAClB,CAAC,GAAG,CAAC;QAEL,aAAa;IACf;IAEA,+DAA+D;IAC/D,MAAM,yBAAyB,CAAA,GAAA,qMAAA,CAAA,UAAO,AAAD,EAAE;QACrC,IAAI,uBAAuB,QAAQ,iBAAiB;QACpD,IAAI,gBAAgB;QACpB,MAAM,aAAuB,EAAE;QAC/B,MAAM,eAAyB,EAAE;QACjC,IAAI,mBAAkC;QAEtC,oDAAoD;QACpD,MAAM,eAAe;QAErB,KAAK,MAAM,QAAQ,aAAc;YAC/B,MAAM,QAAQ,WAAW,SAAS,CAAC,KAAK,OAAO,CAAC,IAAI;YAEpD,IAAI,QAAQ,GAAG;gBACb,0DAA0D;gBAC1D,IAAI,eAAe;oBACjB,aAAa,IAAI,CAAC,KAAK,OAAO;gBAChC,OAAO,IAAI,uBAAuB,QAAQ,GAAG;oBAC3C,2DAA2D;oBAC3D,WAAW,IAAI,CAAC,KAAK,OAAO;oBAC5B,mBAAmB,KAAK,OAAO,CAAC,sCAAsC;;oBACtE,gBAAgB;oBAChB,wBAAwB;gBAC1B,OAAO;oBACL,wBAAwB;oBACxB,WAAW,IAAI,CAAC,KAAK,OAAO;oBAC5B,wBAAwB;gBAC1B;YACF,OAAO;gBACL,qEAAqE;gBACrE,WAAW,IAAI,CAAC,KAAK,OAAO;YAC9B;QACF;QAEA,OAAO;YACL;YACA;YACA;YACA;YACA;QACF;IACF,GAAG;QAAC;QAAiB;QAAW,QAAQ;KAAc;IAEtD,oCAAoC;IACpC,MAAM,6BAA6B,IAAM;IAEzC,0BAA0B;IAC1B,MAAM,aAAa;QACjB,IAAI,CAAC,cAAc,CAAC,QAAQ;QAE5B,0BAA0B;QAC1B,IAAI,gBAAgB,MAAM,KAAK,GAAG;YAChC,QAAQ;YACR;QACF;QAEA,qEAAqE;QACrE,MAAM,eAAe,gBAAgB,MAAM,CAAC,CAAA;YAC1C,MAAM,QAAQ,SAAS,CAAC,KAAK,OAAO,CAAC;YACrC,OAAO,CAAC,SAAS,MAAM,IAAI,OAAO,MAAM,MAAM,WAAW,WAAW,WAAW,SAAS;QAC1F;QAEA,IAAI,aAAa,MAAM,GAAG,GAAG;YAC3B,QAAQ,CAAC,wCAAwC,EAAE,aAAa,GAAG,CAAC,CAAA,IAAK,EAAE,OAAO,EAAE,IAAI,CAAC,OAAO;YAChG;QACF;QAEA,IAAI;YACF,UAAU;YAEV,qCAAqC;YACrC,MAAM,EAAE,UAAU,EAAE,YAAY,EAAE,GAAG;YAErC,qDAAqD;YACrD,MAAM,gBAAgB,gBAAgB,MAAM,CAAC,CAAA;gBAC3C,MAAM,QAAQ,WAAW,SAAS,CAAC,KAAK,OAAO,CAAC,IAAI;gBACpD,MAAM,YAAY,aAAa,QAAQ,CAAC,KAAK,OAAO;gBACpD,OAAO,QAAQ,KAAK,CAAC;YACvB;YAEA,IAAI,cAAc,MAAM,KAAK,GAAG;gBAC9B,QAAQ;gBACR;YACF;YAEA,QAAQ,GAAG,CAAC,0DAA0D;gBACpE,iBAAiB,gBAAgB,MAAM;gBACvC,YAAY,WAAW,MAAM;gBAC7B,cAAc,aAAa,MAAM;gBACjC,eAAe,cAAc,MAAM;YACrC;YAEA,4BAA4B;YAC5B,MAAM,UAAU,EAAE;YAClB,MAAM,SAAS,EAAE;YACjB,IAAI,uBAAuB,QAAQ,iBAAiB;YAEpD,KAAK,MAAM,QAAQ,cAAe;gBAChC,IAAI;oBACF,MAAM,mBAAmB,SAAS,CAAC,KAAK,OAAO,CAAC;oBAChD,MAAM,cAAc,WAAW;oBAE/B,2BAA2B;oBAC3B,MAAM,YAAY,uBAAuB,cAAc;oBACvD,MAAM,iBAAiB,KAAK,eAAe,IAAI;oBAC/C,MAAM,iBAAiB,aAAa;oBAEpC,QAAQ,GAAG,CAAC,mDAAmD;wBAC7D;wBACA;wBACA;wBACA;wBACA;oBACF;oBAEA,4CAA4C;oBAC5C,MAAM,iHAAA,CAAA,UAAO,CAAC,iBAAiB,CAC7B,YACA,KAAK,OAAO,EACZ,aACA,OAAO,SAAS,EAChB;oBAGF,8CAA8C;oBAC9C,wBAAwB;oBAExB,QAAQ,IAAI,CAAC;wBACX,MAAM,KAAK,OAAO;wBAClB;wBACA,SAAS;wBACT,iBAAiB,KAAK,eAAe;wBACrC,cAAc;oBAChB;gBACF,EAAE,OAAO,OAAY;oBAEnB,4BAA4B;oBAC5B,IAAI,eAAe;oBAEnB,IAAI,MAAM,QAAQ,EAAE;wBAClB,sCAAsC;wBACtC,MAAM,SAAS,MAAM,QAAQ,CAAC,MAAM;wBACpC,MAAM,OAAO,MAAM,QAAQ,CAAC,IAAI;wBAEhC,IAAI,WAAW,KAAK;4BAClB,eAAe,MAAM,WAAW,MAAM,SAAS;wBACjD,OAAO,IAAI,WAAW,KAAK;4BACzB,eAAe;wBACjB,OAAO,IAAI,WAAW,KAAK;4BACzB,eAAe;wBACjB,OAAO,IAAI,WAAW,KAAK;4BACzB,eAAe;wBACjB,OAAO;4BACL,eAAe,CAAC,YAAY,EAAE,OAAO,EAAE,EAAE,MAAM,WAAW,MAAM,SAAS,qBAAqB;wBAChG;oBACF,OAAO,IAAI,MAAM,OAAO,EAAE;wBACxB,iBAAiB;wBACjB,eAAe;oBACjB,OAAO;wBACL,+BAA+B;wBAC/B,eAAe,MAAM,OAAO,IAAI;oBAClC;oBAEA,OAAO,IAAI,CAAC;wBACV,MAAM,KAAK,OAAO;wBAClB,OAAO;oBACT;gBACF;YACF;YAEA,sCAAsC;YACtC,IAAI,OAAO,MAAM,KAAK,GAAG;gBACvB,MAAM,oBAAoB,QAAQ,MAAM,CAAC,CAAA,IAAK,EAAE,eAAe,EAAE,MAAM;gBACvE,MAAM,iBAAiB,QAAQ,MAAM,CAAC,CAAA,IAAK,EAAE,YAAY,EAAE,MAAM;gBAEjE,IAAI,UAAU,GAAG,QAAQ,MAAM,CAAC,6BAA6B,CAAC;gBAC9D,IAAI,oBAAoB,GAAG;oBACzB,WAAW,CAAC,EAAE,EAAE,kBAAkB,eAAe,CAAC;gBACpD;gBACA,IAAI,iBAAiB,GAAG;oBACtB,WAAW,CAAC,EAAE,EAAE,eAAe,gBAAgB,CAAC;gBAClD;gBAEA,UAAU;gBACV;YACF,OAAO;gBACL,QAAQ,CAAC,QAAQ,EAAE,OAAO,GAAG,CAAC,CAAA,IAAK,GAAG,EAAE,IAAI,CAAC,EAAE,EAAE,EAAE,KAAK,EAAE,EAAE,IAAI,CAAC,OAAO;YAC1E;QACF,EAAE,OAAO,OAAY;YAEnB,IAAI,eAAe;YAEnB,IAAI,MAAM,QAAQ,EAAE;gBAClB,MAAM,SAAS,MAAM,QAAQ,CAAC,MAAM;gBACpC,MAAM,OAAO,MAAM,QAAQ,CAAC,IAAI;gBAEhC,IAAI,WAAW,KAAK;oBAClB,eAAe,CAAC,uBAAuB,EAAE,MAAM,WAAW,MAAM,SAAS,mBAAmB;gBAC9F,OAAO,IAAI,WAAW,KAAK;oBACzB,eAAe;gBACjB,OAAO,IAAI,WAAW,KAAK;oBACzB,eAAe;gBACjB,OAAO,IAAI,WAAW,KAAK;oBACzB,eAAe;gBACjB,OAAO;oBACL,eAAe,CAAC,mBAAmB,EAAE,OAAO,GAAG,EAAE,MAAM,WAAW,MAAM,SAAS,sBAAsB;gBACzG;YACF,OAAO,IAAI,MAAM,OAAO,EAAE;gBACxB,eAAe;YACjB,OAAO;gBACL,eAAe,MAAM,OAAO,IAAI;YAClC;YAEA,QAAQ;QACV,SAAU;YACR,UAAU;QACZ;IACF;IAEA,MAAM,cAAc;QAClB,IAAI,CAAC,QAAQ;YACX,mBAAmB,EAAE;YACrB,aAAa,CAAC;YACd,UAAU,CAAC;YACX;QACF;IACF;IAEA,IAAI,CAAC,QAAQ,OAAO;IAEpB,MAAM,kBAAkB,CAAC;QACvB,MAAM,QAAQ,SAAS,KAAK,CAAC;QAC7B,OAAO,QAAQ,KAAK,CAAC,EAAE,GAAG;IAC5B;IAEA,4BAA4B;IAC5B,MAAM,mBAAmB;QACvB,IAAI,cAAc,UAAU,IAAI,GAAG,OAAO;QAE1C,MAAM,QAAQ,EAAE;QAChB,MAAM,kBAAkB;QACxB,IAAI,YAAY,KAAK,GAAG,CAAC,GAAG,cAAc,KAAK,KAAK,CAAC,kBAAkB;QACvE,IAAI,UAAU,KAAK,GAAG,CAAC,cAAc,UAAU,EAAE,YAAY,kBAAkB;QAE/E,IAAI,UAAU,YAAY,IAAI,iBAAiB;YAC7C,YAAY,KAAK,GAAG,CAAC,GAAG,UAAU,kBAAkB;QACtD;QAEA,qBACE,8OAAC;YAAI,WAAU;;8BACb,8OAAC,kIAAA,CAAA,SAAM;oBACL,SAAQ;oBACR,MAAK;oBACL,SAAS,IAAM,eAAe;oBAC9B,UAAU,gBAAgB;8BAC3B;;;;;;8BAGD,8OAAC,kIAAA,CAAA,SAAM;oBACL,SAAQ;oBACR,MAAK;oBACL,SAAS,IAAM,eAAe,cAAc;oBAC5C,UAAU,gBAAgB;8BAC3B;;;;;;gBAIA,YAAY,mBACX;;sCACE,8OAAC,kIAAA,CAAA,SAAM;4BAAC,SAAQ;4BAAU,MAAK;4BAAK,SAAS,IAAM,eAAe;sCAAI;;;;;;wBACrE,YAAY,mBAAK,8OAAC;4BAAK,WAAU;sCAAgB;;;;;;;;gBAIrD,MAAM,IAAI,CAAC;oBAAE,QAAQ,UAAU,YAAY;gBAAE,GAAG,CAAC,GAAG,IAAM,YAAY,GAAG,GAAG,CAAC,CAAA,qBAC5E,8OAAC,kIAAA,CAAA,SAAM;wBAEL,SAAS,gBAAgB,OAAO,YAAY;wBAC5C,MAAK;wBACL,SAAS,IAAM,eAAe;kCAE7B;uBALI;;;;;gBASR,UAAU,cAAc,UAAU,kBACjC;;wBACG,UAAU,cAAc,UAAU,GAAG,mBAAK,8OAAC;4BAAK,WAAU;sCAAgB;;;;;;sCAC3E,8OAAC,kIAAA,CAAA,SAAM;4BAAC,SAAQ;4BAAU,MAAK;4BAAK,SAAS,IAAM,eAAe,cAAc,UAAU;sCACvF,cAAc,UAAU;;;;;;;;8BAK/B,8OAAC,kIAAA,CAAA,SAAM;oBACL,SAAQ;oBACR,MAAK;oBACL,SAAS,IAAM,eAAe,cAAc;oBAC5C,UAAU,gBAAgB,cAAc,UAAU;8BACnD;;;;;;8BAGD,8OAAC,kIAAA,CAAA,SAAM;oBACL,SAAQ;oBACR,MAAK;oBACL,SAAS,IAAM,eAAe,cAAc,UAAU;oBACtD,UAAU,gBAAgB,cAAc,UAAU;8BACnD;;;;;;8BAID,8OAAC;oBAAK,WAAU;;wBAA6B;wBACnC;wBAAY;wBAAK,cAAc,UAAU;;;;;;;;;;;;;IAIzD;IAEA,6CAA6C;IAC7C,MAAM,iBAAiB,CAAC,MAAsB;QAC5C,QAAQ,GAAG,CAAC,sDAAsD;YAChE;YACA,YAAY,KAAK,MAAM;YACvB,YAAY,KAAK,KAAK,CAAC,GAAG,GAAG,GAAG,CAAC,CAAA,IAAK,CAAC;oBACrC,IAAI,EAAE,OAAO;oBACb,WAAW,EAAE,SAAS;oBACtB,SAAS,EAAE,OAAO;gBACpB,CAAC;QACH;QAEA,IAAI,KAAK,MAAM,KAAK,GAAG;YACrB,qBACE,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBAAI,WAAU;;sCACb,8OAAC,oMAAA,CAAA,QAAK;4BAAC,WAAU;;;;;;sCACjB,8OAAC;;gCAAI;gCAAa,eAAe,gBAAgB;gCAAgB;;;;;;;sCACjE,8OAAC;4BAAI,WAAU;sCACZ,eACG,CAAC,0BAA0B,EAAE,QAAQ,UAAU,gBAAgB,EAAE,QAAQ,QAAQ,CAAC,CAAC,GACnF;;;;;;;;;;;;;;;;;QAMd;QAEA,qBACE,8OAAC;YAAI,WAAU;sBACZ,KAAK,GAAG,CAAC,CAAC,MAAM;gBACf,MAAM,aAAa,gBAAgB,IAAI,CAAC,CAAA,IAAK,EAAE,OAAO,KAAK,KAAK,OAAO;gBACvE,MAAM,QAAQ,SAAS,CAAC,KAAK,OAAO,CAAC,IAAI;gBAEzC,6BAA6B;gBAC7B,MAAM,EAAE,YAAY,EAAE,gBAAgB,EAAE,GAAG;gBAC3C,MAAM,YAAY,cAAc,aAAa,QAAQ,CAAC,KAAK,OAAO;gBAClE,MAAM,YAAY,cAAc,KAAK,OAAO,KAAK,iBAAiB,gCAAgC;;gBAElG,qBACE,8OAAC;oBAEC,WAAW,CAAC,2CAA2C,EACrD,YACI,6BACA,YACA,mCACA,aACA,+BACA,0DACJ;;sCAGF,8OAAC;4BAAI,WAAU;;8CAEb,8OAAC;oCACC,MAAK;oCACL,SAAS;oCACT,UAAU,CAAC;wCACT,iBAAiB,MAAM;oCACzB;oCACA,WAAU;;;;;;8CAIZ,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAK,WAAU;sDAA2C,KAAK,OAAO;;;;;;sDACvE,8OAAC;4CAAK,WAAU;sDAA2D,KAAK,SAAS;;;;;;sDACzF,8OAAC;4CAAK,WAAU;sDAA2D,KAAK,OAAO;;;;;;sDACvF,8OAAC;4CAAK,WAAU;;gDAAuC,KAAK,aAAa;gDAAC;;;;;;;wCAGzE,2BACC,8OAAC;4CAAK,WAAU;sDAAkF;;;;;;wCAInG,2BACC,8OAAC;4CAAK,WAAU;sDAAwF;;;;;;sDAK1G,8OAAC;4CAAK,WAAU;;gDACb,KAAK,mBAAmB,IAAI;gDAAM;gDAAI,KAAK,iBAAiB,IAAI;;;;;;;;;;;;;gCAKpE,4BACC,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAM,WAAU;sDAAwB;;;;;;sDACzC,8OAAC;4CACC,MAAK;4CACL,MAAK;4CACL,KAAI;4CACJ,OAAO;4CACP,UAAU,CAAC;gDACT,kBAAkB,KAAK,OAAO,EAAE,EAAE,MAAM,CAAC,KAAK;4CAChD;4CACA,WAAU;4CACV,aAAY;;;;;;;;;;;;;;;;;;wBAOnB,MAAM,CAAC,KAAK,OAAO,CAAC,kBACnB,8OAAC;4BAAI,WAAU;sCAAkC,MAAM,CAAC,KAAK,OAAO,CAAC;;;;;;;mBApElE,KAAK,OAAO;;;;;YAwEvB;;;;;;IAGN;IAEA,qBACE;kBACE,cAAA,8OAAC,kIAAA,CAAA,SAAM;YAAC,MAAM;YAAM,cAAc;sBAClC,cAAA,8OAAC,kIAAA,CAAA,gBAAa;gBACZ,WAAU;gBACV,OAAO;oBACL,OAAO;oBACP,UAAU;oBACV,UAAU;gBACZ;;kCAEA,8OAAC,kIAAA,CAAA,eAAY;wBAAC,WAAU;;0CACtB,8OAAC,kIAAA,CAAA,cAAW;gCAAC,WAAU;;kDACrB,8OAAC,oMAAA,CAAA,QAAK;wCAAC,WAAU;;;;;;oCAAY;oCACsB,gBAAgB,OAAO,SAAS;;;;;;;0CAErF,8OAAC,kIAAA,CAAA,oBAAiB;gCAAC,WAAU;0CAAkC;;;;;;;;;;;;kCAKjE,8OAAC;wBAAI,WAAU;;0CAEb,8OAAC;gCAAI,WAAU;0CACb,cAAA,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;;sEAAK,8OAAC;;gEAAO;gEAAQ,gBAAgB,OAAO,SAAS;;;;;;;wDAAW;wDAAI,OAAO,SAAS;wDAAC;wDAAI,OAAO,OAAO;;;;;;;8DACxG,8OAAC;;wDAAK;sEAAS,8OAAC;;gEAAQ,OAAO,aAAa;gEAAC;;;;;;;;;;;;;;;;;;;sDAE/C,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;;wDAAK;sEAAa,8OAAC;sEAAQ,gBAAgB,MAAM;;;;;;wDAAU;sEAAQ,8OAAC;;gEAAQ,aAAa,sBAAsB,CAAC,OAAO,CAAC;gEAAG;;;;;;;;;;;;;gDAC3H,aAAa,WAAW,kBACvB,8OAAC;oDAAK,WAAU;8DAA2B;;;;;;;;;;;;;;;;;;;;;;;0CAOnD,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAI,WAAU;;0DACb,8OAAC,sMAAA,CAAA,SAAM;gDAAC,WAAU;;;;;;0DAClB,8OAAC,iIAAA,CAAA,QAAK;gDACJ,aAAY;gDACZ,OAAO;gDACP,UAAU,CAAC,IAAM,cAAc,EAAE,MAAM,CAAC,KAAK;gDAC7C,WAAU;;;;;;;;;;;;kDAGd,8OAAC,kIAAA,CAAA,SAAM;wCACL,SAAQ;wCACR,MAAK;wCACL,SAAS;wCACT,UAAU,gBAAgB,MAAM,KAAK;wCACrC,WAAU;kDACX;;;;;;;;;;;;4BAMF,6BACC,8OAAC;gCAAI,WAAU;;kDACb,8OAAC,iNAAA,CAAA,UAAO;wCAAC,WAAU;;;;;;kDACnB,8OAAC;kDAAK;;;;;;;;;;;;4BAKT,CAAC,6BACA,8OAAC,gIAAA,CAAA,OAAI;gCAAC,cAAa;gCAAc,WAAU;;kDACzC,8OAAC,gIAAA,CAAA,WAAQ;wCAAC,WAAU;;0DAClB,8OAAC,gIAAA,CAAA,cAAW;gDACV,OAAM;gDACN,WAAU;;kEAEV,8OAAC;wDAAK,WAAU;;;;;;oDAA2C;oDACxC,cAAc,gBAAgB;oDAAC;;;;;;;0DAEpD,8OAAC,gIAAA,CAAA,cAAW;gDACV,OAAM;gDACN,WAAU;;kEAEV,8OAAC;wDAAK,WAAU;;;;;;oDAA4C;oDACvC,cAAc,kBAAkB;oDAAC;;;;;;;;;;;;;kDAI1D,8OAAC,gIAAA,CAAA,cAAW;wCAAC,OAAM;wCAAc,WAAU;kDACzC,cAAA,8OAAC;4CAAI,WAAU;sDACZ,eAAe,cAAc,WAAW,EAAE;;;;;;;;;;;kDAI/C,8OAAC,gIAAA,CAAA,cAAW;wCAAC,OAAM;wCAAgB,WAAU;kDAC3C,cAAA,8OAAC;4CAAI,WAAU;sDACZ,eAAe,cAAc,aAAa,EAAE;;;;;;;;;;;;;;;;;;;;;;;kCAOvD,8OAAC,kIAAA,CAAA,eAAY;wBAAC,WAAU;;0CACtB,8OAAC;gCAAI,WAAU;0CACZ,gBAAgB,MAAM,GAAG,IACxB,CAAC;oCACC,MAAM,EAAE,oBAAoB,EAAE,UAAU,EAAE,YAAY,EAAE,gBAAgB,EAAE,GAAG;oCAC7E,MAAM,cAAc,gBAAgB,MAAM,CAAC,CAAC,KAAK;wCAC/C,MAAM,QAAQ,WAAW,SAAS,CAAC,KAAK,OAAO,CAAC,IAAI;wCACpD,OAAO,MAAM;oCACf,GAAG;oCACH,MAAM,aAAa,CAAC,QAAQ,iBAAiB,CAAC,IAAI;oCAClD,MAAM,gBAAgB,gBAAgB,MAAM,CAAC,CAAA,IAAK,EAAE,eAAe,EAAE,MAAM;oCAE3E,qBACE,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;;oDACE,gBAAgB,MAAM;oDAAC;oDAAqB,YAAY,OAAO,CAAC;oDAAG;;;;;;;0DAEtE,8OAAC;;oDAAI;oDACA,WAAW,MAAM;oDAAC;oDAAgB,aAAa,MAAM;oDAAC;;;;;;;0DAE3D,8OAAC;;oDAAI;oDACK,WAAW,OAAO,CAAC;oDAAG;oDAAK,QAAQ,iBAAiB;oDAAE;oDAC7D,uBAAuB,KAAK,kCAC3B,8OAAC;wDAAK,WAAU;;4DAAmC;4DACvC;4DAAiB;4DAAI,KAAK,GAAG,CAAC,sBAAsB,OAAO,CAAC;4DAAG;;;;;;;;;;;;;4CAI9E,gBAAgB,mBACf,8OAAC;gDAAI,WAAU;;oDAA8B;oDACvC;oDAAc;;;;;;;;;;;;;gCAK5B,CAAC,oBAED,8OAAC;8CAAI;;;;;;;;;;;0CAIT,8OAAC;gCAAI,WAAU;;kDACb,8OAAC,kIAAA,CAAA,SAAM;wCAAC,SAAQ;wCAAU,SAAS;wCAAa,UAAU;kDAAQ;;;;;;kDAGlE,8OAAC,kIAAA,CAAA,SAAM;wCACL,SAAS;wCACT,UAAU,UAAU,gBAAgB,MAAM,KAAK;wCAC/C,WAAW,aAAa,WAAW,GAAG,sCAAsC;;4CAE3E,wBAAU,8OAAC,iNAAA,CAAA,UAAO;gDAAC,WAAU;;;;;;4CAC7B,SAAS,mBACT,aAAa,WAAW,GAAG,CAAC,MAAM,EAAE,gBAAgB,MAAM,CAAC,YAAY,CAAC,GACxE,CAAC,MAAM,EAAE,gBAAgB,MAAM,CAAC,KAAK,CAAC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAQrD", "debugId": null}}, {"offset": {"line": 4550, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/CMS/webapp-nextjs/src/components/bobine/BobineStatistics.tsx"], "sourcesContent": ["'use client'\n\nimport { useMemo } from 'react'\nimport { Card, CardContent } from '@/components/ui/card'\nimport { Badge } from '@/components/ui/badge'\nimport { \n  Package, \n  CheckCircle, \n  Clock, \n  AlertTriangle,\n  AlertCircle,\n  BarChart3\n} from 'lucide-react'\n\ninterface Bob<PERSON> {\n  id_bobina: string\n  numero_bobina: string\n  utility: string\n  tipologia: string\n  sezione: string\n  metri_totali: number\n  metri_residui: number\n  stato_bobina: string\n  ubicazione_bobina?: string\n  fornitore?: string\n  n_DDT?: string\n  data_DDT?: string\n  configurazione?: string\n}\n\ninterface BobineStatisticsProps {\n  bobine: Bobina[]\n  filteredBobine: Bobina[]\n  className?: string\n}\n\nexport default function BobineStatistics({\n  bobine,\n  filteredBobine,\n  className\n}: BobineStatisticsProps) {\n  const stats = useMemo(() => {\n    const totalBobine = bobine.length\n    const filteredCount = filteredBobine.length\n    \n    // Stati bobine\n    const disponibili = filteredBobine.filter(b => \n      b.stato_bobina === 'Disponibile'\n    ).length\n    \n    const inUso = filteredBobine.filter(b => \n      b.stato_bobina === 'In uso'\n    ).length\n    \n    const terminate = filteredBobine.filter(b => \n      b.stato_bobina === 'Terminata'\n    ).length\n    \n    const over = filteredBobine.filter(b => \n      b.stato_bobina === 'Over'\n    ).length\n    \n    // Calcoli metrature\n    const metriTotali = filteredBobine.reduce((sum, b) => sum + (b.metri_totali || 0), 0)\n    const metriResidui = filteredBobine.reduce((sum, b) => sum + (b.metri_residui || 0), 0)\n    const metriUtilizzati = metriTotali - metriResidui\n    \n    // Percentuale utilizzo\n    const percentualeUtilizzo = metriTotali > 0 ? Math.round((metriUtilizzati / metriTotali) * 100) : 0\n    \n    return {\n      totalBobine,\n      filteredCount,\n      disponibili,\n      inUso,\n      terminate,\n      over,\n      metriTotali,\n      metriResidui,\n      metriUtilizzati,\n      percentualeUtilizzo\n    }\n  }, [bobine, filteredBobine])\n\n  return (\n    <Card className={className}>\n      <CardContent className=\"p-1.5\">\n        {/* Header */}\n        <div className=\"flex items-center justify-between mb-1\">\n          <div className=\"flex items-center space-x-1.5\">\n            <BarChart3 className=\"h-3.5 w-3.5 text-mariner-600\" />\n            <span className=\"text-xs font-semibold text-mariner-900\">Statistiche Bobine</span>\n          </div>\n        </div>\n\n        {/* Statistics distributed across full width */}\n        <div className=\"grid grid-cols-2 md:grid-cols-4 lg:grid-cols-6 gap-2\">\n\n          {/* Total bobine */}\n          <div className=\"flex items-center space-x-1.5 bg-mariner-50 px-1.5 py-1 rounded-lg\">\n            <Package className=\"h-3.5 w-3.5 text-mariner-600\" />\n            <div>\n              <div className=\"font-bold text-mariner-900 text-sm\">{stats.filteredCount}</div>\n              <div className=\"text-xs text-mariner-600\">di {stats.totalBobine} bobine</div>\n            </div>\n          </div>\n\n          {/* Disponibili */}\n          <div className=\"flex items-center space-x-1.5 bg-green-50 px-1.5 py-1 rounded-lg\">\n            <CheckCircle className=\"h-3.5 w-3.5 text-green-600\" />\n            <div>\n              <div className=\"font-bold text-green-700 text-sm\">{stats.disponibili}</div>\n              <div className=\"text-xs text-green-600\">disponibili</div>\n            </div>\n          </div>\n\n          {/* In uso */}\n          <div className=\"flex items-center space-x-1.5 bg-yellow-50 px-1.5 py-1 rounded-lg\">\n            <Clock className=\"h-3.5 w-3.5 text-yellow-600\" />\n            <div>\n              <div className=\"font-bold text-yellow-700 text-sm\">{stats.inUso}</div>\n              <div className=\"text-xs text-yellow-600\">in uso</div>\n            </div>\n          </div>\n\n          {/* Terminate */}\n          <div className=\"flex items-center space-x-1.5 bg-red-50 px-1.5 py-1 rounded-lg\">\n            <AlertTriangle className=\"h-3.5 w-3.5 text-red-600\" />\n            <div>\n              <div className=\"font-bold text-red-700 text-sm\">{stats.terminate}</div>\n              <div className=\"text-xs text-red-600\">terminate</div>\n            </div>\n          </div>\n\n          {/* Over */}\n          <div className=\"flex items-center space-x-1.5 bg-red-50 px-1.5 py-1 rounded-lg\">\n            <AlertCircle className=\"h-3.5 w-3.5 text-red-600\" />\n            <div>\n              <div className=\"font-bold text-red-700 text-sm\">{stats.over}</div>\n              <div className=\"text-xs text-red-600\">over</div>\n            </div>\n          </div>\n\n          {/* Meters progress */}\n          <div className=\"flex items-center space-x-1.5 bg-indigo-50 px-1.5 py-1 rounded-lg\">\n            <div className=\"h-3.5 w-3.5 flex items-center justify-center\">\n              <div className=\"h-2 w-2 bg-indigo-600 rounded-full\"></div>\n            </div>\n            <div>\n              <div className=\"font-bold text-indigo-700 text-sm\">{stats.metriUtilizzati.toLocaleString()}m</div>\n              <div className=\"text-xs text-indigo-600\">di {stats.metriTotali.toLocaleString()}m</div>\n            </div>\n          </div>\n\n        </div>\n\n        {/* Utilizzo Progress bar - Colori morbidi */}\n        {stats.filteredCount > 0 && (\n          <div className=\"mt-2 bg-gray-50 p-2 rounded-lg\">\n            <div className=\"flex justify-between text-xs font-medium text-gray-700 mb-1\">\n              <span>Utilizzo Complessivo Bobine</span>\n              <span className={`font-bold ${\n                stats.percentualeUtilizzo >= 80 ? 'text-amber-700' :\n                stats.percentualeUtilizzo >= 60 ? 'text-orange-700' :\n                stats.percentualeUtilizzo >= 40 ? 'text-yellow-700' : 'text-emerald-700'\n              }`}>\n                {stats.percentualeUtilizzo}%\n              </span>\n            </div>\n            <div className=\"w-full bg-gray-200 rounded-full h-2\">\n              <div\n                className={`h-2 rounded-full transition-all duration-500 ease-in-out ${\n                  stats.percentualeUtilizzo >= 80 ? 'bg-gradient-to-r from-amber-500 to-amber-600' :\n                  stats.percentualeUtilizzo >= 60 ? 'bg-gradient-to-r from-orange-500 to-orange-600' :\n                  stats.percentualeUtilizzo >= 40 ? 'bg-gradient-to-r from-yellow-500 to-yellow-600' :\n                  'bg-gradient-to-r from-emerald-500 to-emerald-600'\n                }`}\n                style={{ width: `${Math.min(stats.percentualeUtilizzo, 100)}%` }}\n              />\n            </div>\n            <div className=\"flex justify-between text-xs text-gray-500 mt-0.5\">\n              <span>Metri utilizzati vs totali disponibili</span>\n              <span>{stats.metriResidui.toLocaleString()}m residui</span>\n            </div>\n          </div>\n        )}\n      </CardContent>\n    </Card>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAEA;AAAA;AAAA;AAAA;AAAA;AAAA;AALA;;;;;AAoCe,SAAS,iBAAiB,EACvC,MAAM,EACN,cAAc,EACd,SAAS,EACa;IACtB,MAAM,QAAQ,CAAA,GAAA,qMAAA,CAAA,UAAO,AAAD,EAAE;QACpB,MAAM,cAAc,OAAO,MAAM;QACjC,MAAM,gBAAgB,eAAe,MAAM;QAE3C,eAAe;QACf,MAAM,cAAc,eAAe,MAAM,CAAC,CAAA,IACxC,EAAE,YAAY,KAAK,eACnB,MAAM;QAER,MAAM,QAAQ,eAAe,MAAM,CAAC,CAAA,IAClC,EAAE,YAAY,KAAK,UACnB,MAAM;QAER,MAAM,YAAY,eAAe,MAAM,CAAC,CAAA,IACtC,EAAE,YAAY,KAAK,aACnB,MAAM;QAER,MAAM,OAAO,eAAe,MAAM,CAAC,CAAA,IACjC,EAAE,YAAY,KAAK,QACnB,MAAM;QAER,oBAAoB;QACpB,MAAM,cAAc,eAAe,MAAM,CAAC,CAAC,KAAK,IAAM,MAAM,CAAC,EAAE,YAAY,IAAI,CAAC,GAAG;QACnF,MAAM,eAAe,eAAe,MAAM,CAAC,CAAC,KAAK,IAAM,MAAM,CAAC,EAAE,aAAa,IAAI,CAAC,GAAG;QACrF,MAAM,kBAAkB,cAAc;QAEtC,uBAAuB;QACvB,MAAM,sBAAsB,cAAc,IAAI,KAAK,KAAK,CAAC,AAAC,kBAAkB,cAAe,OAAO;QAElG,OAAO;YACL;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;QACF;IACF,GAAG;QAAC;QAAQ;KAAe;IAE3B,qBACE,8OAAC,gIAAA,CAAA,OAAI;QAAC,WAAW;kBACf,cAAA,8OAAC,gIAAA,CAAA,cAAW;YAAC,WAAU;;8BAErB,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC;wBAAI,WAAU;;0CACb,8OAAC,kNAAA,CAAA,YAAS;gCAAC,WAAU;;;;;;0CACrB,8OAAC;gCAAK,WAAU;0CAAyC;;;;;;;;;;;;;;;;;8BAK7D,8OAAC;oBAAI,WAAU;;sCAGb,8OAAC;4BAAI,WAAU;;8CACb,8OAAC,wMAAA,CAAA,UAAO;oCAAC,WAAU;;;;;;8CACnB,8OAAC;;sDACC,8OAAC;4CAAI,WAAU;sDAAsC,MAAM,aAAa;;;;;;sDACxE,8OAAC;4CAAI,WAAU;;gDAA2B;gDAAI,MAAM,WAAW;gDAAC;;;;;;;;;;;;;;;;;;;sCAKpE,8OAAC;4BAAI,WAAU;;8CACb,8OAAC,2NAAA,CAAA,cAAW;oCAAC,WAAU;;;;;;8CACvB,8OAAC;;sDACC,8OAAC;4CAAI,WAAU;sDAAoC,MAAM,WAAW;;;;;;sDACpE,8OAAC;4CAAI,WAAU;sDAAyB;;;;;;;;;;;;;;;;;;sCAK5C,8OAAC;4BAAI,WAAU;;8CACb,8OAAC,oMAAA,CAAA,QAAK;oCAAC,WAAU;;;;;;8CACjB,8OAAC;;sDACC,8OAAC;4CAAI,WAAU;sDAAqC,MAAM,KAAK;;;;;;sDAC/D,8OAAC;4CAAI,WAAU;sDAA0B;;;;;;;;;;;;;;;;;;sCAK7C,8OAAC;4BAAI,WAAU;;8CACb,8OAAC,wNAAA,CAAA,gBAAa;oCAAC,WAAU;;;;;;8CACzB,8OAAC;;sDACC,8OAAC;4CAAI,WAAU;sDAAkC,MAAM,SAAS;;;;;;sDAChE,8OAAC;4CAAI,WAAU;sDAAuB;;;;;;;;;;;;;;;;;;sCAK1C,8OAAC;4BAAI,WAAU;;8CACb,8OAAC,oNAAA,CAAA,cAAW;oCAAC,WAAU;;;;;;8CACvB,8OAAC;;sDACC,8OAAC;4CAAI,WAAU;sDAAkC,MAAM,IAAI;;;;;;sDAC3D,8OAAC;4CAAI,WAAU;sDAAuB;;;;;;;;;;;;;;;;;;sCAK1C,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAI,WAAU;8CACb,cAAA,8OAAC;wCAAI,WAAU;;;;;;;;;;;8CAEjB,8OAAC;;sDACC,8OAAC;4CAAI,WAAU;;gDAAqC,MAAM,eAAe,CAAC,cAAc;gDAAG;;;;;;;sDAC3F,8OAAC;4CAAI,WAAU;;gDAA0B;gDAAI,MAAM,WAAW,CAAC,cAAc;gDAAG;;;;;;;;;;;;;;;;;;;;;;;;;gBAOrF,MAAM,aAAa,GAAG,mBACrB,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;8CAAK;;;;;;8CACN,8OAAC;oCAAK,WAAW,CAAC,UAAU,EAC1B,MAAM,mBAAmB,IAAI,KAAK,mBAClC,MAAM,mBAAmB,IAAI,KAAK,oBAClC,MAAM,mBAAmB,IAAI,KAAK,oBAAoB,oBACtD;;wCACC,MAAM,mBAAmB;wCAAC;;;;;;;;;;;;;sCAG/B,8OAAC;4BAAI,WAAU;sCACb,cAAA,8OAAC;gCACC,WAAW,CAAC,yDAAyD,EACnE,MAAM,mBAAmB,IAAI,KAAK,iDAClC,MAAM,mBAAmB,IAAI,KAAK,mDAClC,MAAM,mBAAmB,IAAI,KAAK,mDAClC,oDACA;gCACF,OAAO;oCAAE,OAAO,GAAG,KAAK,GAAG,CAAC,MAAM,mBAAmB,EAAE,KAAK,CAAC,CAAC;gCAAC;;;;;;;;;;;sCAGnE,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;8CAAK;;;;;;8CACN,8OAAC;;wCAAM,MAAM,YAAY,CAAC,cAAc;wCAAG;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAOzD", "debugId": null}}, {"offset": {"line": 4998, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/CMS/webapp-nextjs/src/components/ui/context-menu-custom.tsx"], "sourcesContent": ["'use client'\n\nimport { useState, useEffect, useRef } from 'react'\nimport { createPortal } from 'react-dom'\n\ninterface ContextMenuProps {\n  children: React.ReactNode\n  items: ContextMenuItem[]\n  onAction: (action: string, data?: any) => void\n  disabled?: boolean\n}\n\ninterface ContextMenuItem {\n  id: string\n  label: string\n  icon?: React.ReactNode\n  action: string\n  disabled?: boolean\n  separator?: boolean\n  submenu?: ContextMenuItem[]\n  color?: 'default' | 'warning' | 'danger'\n}\n\ninterface ContextMenuPosition {\n  x: number\n  y: number\n}\n\nexport function ContextMenuCustom({ children, items, onAction, disabled = false }: ContextMenuProps) {\n  const [isOpen, setIsOpen] = useState(false)\n  const [position, setPosition] = useState<ContextMenuPosition>({ x: 0, y: 0 })\n  const [contextData, setContextData] = useState<any>(null)\n  const menuRef = useRef<HTMLDivElement>(null)\n  const triggerRef = useRef<HTMLDivElement>(null)\n\n  useEffect(() => {\n    const handleClickOutside = (event: MouseEvent) => {\n      if (menuRef.current && !menuRef.current.contains(event.target as Node)) {\n        setIsOpen(false)\n      }\n    }\n\n    const handleEscape = (event: KeyboardEvent) => {\n      if (event.key === 'Escape') {\n        setIsOpen(false)\n      }\n    }\n\n    if (isOpen) {\n      document.addEventListener('mousedown', handleClickOutside)\n      document.addEventListener('keydown', handleEscape)\n    }\n\n    return () => {\n      document.removeEventListener('mousedown', handleClickOutside)\n      document.removeEventListener('keydown', handleEscape)\n    }\n  }, [isOpen])\n\n  const handleContextMenu = (event: React.MouseEvent) => {\n    if (disabled) return\n    \n    event.preventDefault()\n    event.stopPropagation()\n\n    const rect = triggerRef.current?.getBoundingClientRect()\n    const x = event.clientX\n    const y = event.clientY\n\n    // Adjust position if menu would go off screen\n    const menuWidth = 200 // Estimated menu width\n    const menuHeight = items.length * 40 // Estimated menu height\n    const viewportWidth = window.innerWidth\n    const viewportHeight = window.innerHeight\n\n    const adjustedX = x + menuWidth > viewportWidth ? x - menuWidth : x\n    const adjustedY = y + menuHeight > viewportHeight ? y - menuHeight : y\n\n    setPosition({ x: adjustedX, y: adjustedY })\n    setContextData(event.currentTarget.dataset)\n    setIsOpen(true)\n  }\n\n  const handleItemClick = (item: ContextMenuItem) => {\n    if (item.disabled) return\n    \n    setIsOpen(false)\n    onAction(item.action, contextData)\n  }\n\n  const getItemColorClass = (color?: string) => {\n    switch (color) {\n      case 'warning':\n        return 'text-amber-600 hover:bg-amber-50'\n      case 'danger':\n        return 'text-red-600 hover:bg-red-50'\n      default:\n        return 'text-gray-700 hover:bg-gray-100'\n    }\n  }\n\n  const renderMenu = () => {\n    if (!isOpen) return null\n\n    return createPortal(\n      <div\n        ref={menuRef}\n        className=\"fixed z-50 min-w-[200px] bg-white border border-gray-200 rounded-md shadow-lg py-1\"\n        style={{\n          left: position.x,\n          top: position.y,\n        }}\n      >\n        {items.map((item, index) => {\n          if (item.separator) {\n            return <div key={`separator-${index}`} className=\"border-t border-gray-200 my-1\" />\n          }\n\n          return (\n            <button\n              key={item.id}\n              className={`w-full px-3 py-2 text-left text-sm flex items-center gap-2 transition-colors ${\n                item.disabled \n                  ? 'text-gray-400 cursor-not-allowed' \n                  : getItemColorClass(item.color)\n              }`}\n              onClick={() => handleItemClick(item)}\n              disabled={item.disabled}\n            >\n              {item.icon && <span className=\"w-4 h-4\">{item.icon}</span>}\n              <span>{item.label}</span>\n            </button>\n          )\n        })}\n      </div>,\n      document.body\n    )\n  }\n\n  return (\n    <>\n      <div\n        ref={triggerRef}\n        onContextMenu={handleContextMenu}\n        className=\"w-full h-full\"\n      >\n        {children}\n      </div>\n      {renderMenu()}\n    </>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAHA;;;;AA4BO,SAAS,kBAAkB,EAAE,QAAQ,EAAE,KAAK,EAAE,QAAQ,EAAE,WAAW,KAAK,EAAoB;IACjG,MAAM,CAAC,QAAQ,UAAU,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACrC,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAuB;QAAE,GAAG;QAAG,GAAG;IAAE;IAC3E,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAO;IACpD,MAAM,UAAU,CAAA,GAAA,qMAAA,CAAA,SAAM,AAAD,EAAkB;IACvC,MAAM,aAAa,CAAA,GAAA,qMAAA,CAAA,SAAM,AAAD,EAAkB;IAE1C,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,MAAM,qBAAqB,CAAC;YAC1B,IAAI,QAAQ,OAAO,IAAI,CAAC,QAAQ,OAAO,CAAC,QAAQ,CAAC,MAAM,MAAM,GAAW;gBACtE,UAAU;YACZ;QACF;QAEA,MAAM,eAAe,CAAC;YACpB,IAAI,MAAM,GAAG,KAAK,UAAU;gBAC1B,UAAU;YACZ;QACF;QAEA,IAAI,QAAQ;YACV,SAAS,gBAAgB,CAAC,aAAa;YACvC,SAAS,gBAAgB,CAAC,WAAW;QACvC;QAEA,OAAO;YACL,SAAS,mBAAmB,CAAC,aAAa;YAC1C,SAAS,mBAAmB,CAAC,WAAW;QAC1C;IACF,GAAG;QAAC;KAAO;IAEX,MAAM,oBAAoB,CAAC;QACzB,IAAI,UAAU;QAEd,MAAM,cAAc;QACpB,MAAM,eAAe;QAErB,MAAM,OAAO,WAAW,OAAO,EAAE;QACjC,MAAM,IAAI,MAAM,OAAO;QACvB,MAAM,IAAI,MAAM,OAAO;QAEvB,8CAA8C;QAC9C,MAAM,YAAY,IAAI,uBAAuB;;QAC7C,MAAM,aAAa,MAAM,MAAM,GAAG,GAAG,wBAAwB;;QAC7D,MAAM,gBAAgB,OAAO,UAAU;QACvC,MAAM,iBAAiB,OAAO,WAAW;QAEzC,MAAM,YAAY,IAAI,YAAY,gBAAgB,IAAI,YAAY;QAClE,MAAM,YAAY,IAAI,aAAa,iBAAiB,IAAI,aAAa;QAErE,YAAY;YAAE,GAAG;YAAW,GAAG;QAAU;QACzC,eAAe,MAAM,aAAa,CAAC,OAAO;QAC1C,UAAU;IACZ;IAEA,MAAM,kBAAkB,CAAC;QACvB,IAAI,KAAK,QAAQ,EAAE;QAEnB,UAAU;QACV,SAAS,KAAK,MAAM,EAAE;IACxB;IAEA,MAAM,oBAAoB,CAAC;QACzB,OAAQ;YACN,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT;gBACE,OAAO;QACX;IACF;IAEA,MAAM,aAAa;QACjB,IAAI,CAAC,QAAQ,OAAO;QAEpB,qBAAO,CAAA,GAAA,4MAAA,CAAA,eAAY,AAAD,gBAChB,8OAAC;YACC,KAAK;YACL,WAAU;YACV,OAAO;gBACL,MAAM,SAAS,CAAC;gBAChB,KAAK,SAAS,CAAC;YACjB;sBAEC,MAAM,GAAG,CAAC,CAAC,MAAM;gBAChB,IAAI,KAAK,SAAS,EAAE;oBAClB,qBAAO,8OAAC;wBAA+B,WAAU;uBAAhC,CAAC,UAAU,EAAE,OAAO;;;;;gBACvC;gBAEA,qBACE,8OAAC;oBAEC,WAAW,CAAC,6EAA6E,EACvF,KAAK,QAAQ,GACT,qCACA,kBAAkB,KAAK,KAAK,GAChC;oBACF,SAAS,IAAM,gBAAgB;oBAC/B,UAAU,KAAK,QAAQ;;wBAEtB,KAAK,IAAI,kBAAI,8OAAC;4BAAK,WAAU;sCAAW,KAAK,IAAI;;;;;;sCAClD,8OAAC;sCAAM,KAAK,KAAK;;;;;;;mBAVZ,KAAK,EAAE;;;;;YAalB;;;;;kBAEF,SAAS,IAAI;IAEjB;IAEA,qBACE;;0BACE,8OAAC;gBACC,KAAK;gBACL,eAAe;gBACf,WAAU;0BAET;;;;;;YAEF;;;AAGP", "debugId": null}}, {"offset": {"line": 5151, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/CMS/webapp-nextjs/src/utils/softColors.ts"], "sourcesContent": ["/**\n * Palette di colori morbidi per il sistema CMS\n * Evita il rosso fuoco e usa tonalità più professionali\n */\n\nexport const SOFT_COLORS = {\n  // Stati di successo - Verde morbido\n  SUCCESS: {\n    bg: 'bg-emerald-50',\n    text: 'text-emerald-700',\n    border: 'border-emerald-200',\n    hover: 'hover:bg-emerald-100',\n    hex: '#10b981' // emerald-500\n  },\n\n  // Stati di warning - Giallo ambra/senape\n  WARNING: {\n    bg: 'bg-amber-50',\n    text: 'text-amber-700',\n    border: 'border-amber-200',\n    hover: 'hover:bg-amber-100',\n    hex: '#f59e0b' // amber-500\n  },\n\n  // Stati di attenzione - Arancione tenue\n  ATTENTION: {\n    bg: 'bg-orange-50',\n    text: 'text-orange-700',\n    border: 'border-orange-200',\n    hover: 'hover:bg-orange-100',\n    hex: '#ea580c' // orange-600\n  },\n\n  // Stati di errore - Rosso morbido (non fuoco)\n  ERROR: {\n    bg: 'bg-rose-50',\n    text: 'text-rose-700',\n    border: 'border-rose-200',\n    hover: 'hover:bg-rose-100',\n    hex: '#e11d48' // rose-600\n  },\n\n  // Stati informativi - Blu morbido\n  INFO: {\n    bg: 'bg-sky-50',\n    text: 'text-sky-700',\n    border: 'border-sky-200',\n    hover: 'hover:bg-sky-100',\n    hex: '#0284c7' // sky-600\n  },\n\n  // Stati neutri - Grigio\n  NEUTRAL: {\n    bg: 'bg-slate-50',\n    text: 'text-slate-700',\n    border: 'border-slate-200',\n    hover: 'hover:bg-slate-100',\n    hex: '#475569' // slate-600\n  },\n\n  // Stati di progresso - Indaco\n  PROGRESS: {\n    bg: 'bg-indigo-50',\n    text: 'text-indigo-700',\n    border: 'border-indigo-200',\n    hover: 'hover:bg-indigo-100',\n    hex: '#4f46e5' // indigo-600\n  }\n}\n\n/**\n * Colori specifici per stati bobine\n */\nexport const BOBINA_COLORS = {\n  DISPONIBILE: SOFT_COLORS.SUCCESS,\n  IN_USO: SOFT_COLORS.PROGRESS,\n  TERMINATA: SOFT_COLORS.NEUTRAL,\n  OVER: SOFT_COLORS.WARNING, // Giallo ambra invece di rosso\n  ERRORE: SOFT_COLORS.ERROR\n}\n\n/**\n * Colori specifici per stati cavi\n */\nexport const CAVO_COLORS = {\n  DA_INSTALLARE: SOFT_COLORS.NEUTRAL,\n  INSTALLATO: SOFT_COLORS.SUCCESS,\n  COLLEGATO_PARTENZA: SOFT_COLORS.INFO,\n  COLLEGATO_ARRIVO: SOFT_COLORS.INFO,\n  COLLEGATO: SOFT_COLORS.PROGRESS,\n  CERTIFICATO: SOFT_COLORS.SUCCESS,\n  SPARE: SOFT_COLORS.WARNING,\n  ERRORE: SOFT_COLORS.ERROR\n}\n\n/**\n * Colori specifici per stati comande\n */\nexport const COMANDA_COLORS = {\n  ATTIVA: SOFT_COLORS.SUCCESS,\n  COMPLETATA: SOFT_COLORS.PROGRESS,\n  ANNULLATA: SOFT_COLORS.NEUTRAL,\n  IN_CORSO: SOFT_COLORS.INFO,\n  ERRORE: SOFT_COLORS.ERROR\n}\n\n/**\n * Funzioni helper per ottenere classi CSS\n */\nexport const getSoftColorClasses = (colorType: keyof typeof SOFT_COLORS) => {\n  const color = SOFT_COLORS[colorType]\n  return {\n    badge: `${color.bg} ${color.text} ${color.border}`,\n    button: `${color.bg} ${color.text} ${color.border} ${color.hover}`,\n    alert: `${color.bg} ${color.text} ${color.border}`,\n    text: color.text,\n    bg: color.bg,\n    border: color.border,\n    hover: color.hover,\n    hex: color.hex\n  }\n}\n\nexport const getBobinaColorClasses = (stato: string) => {\n  const normalizedStato = stato?.toUpperCase() as keyof typeof BOBINA_COLORS\n  const color = BOBINA_COLORS[normalizedStato] || BOBINA_COLORS.ERRORE\n  \n  return {\n    badge: `${color.bg} ${color.text} ${color.border}`,\n    button: `${color.bg} ${color.text} ${color.border} ${color.hover}`,\n    alert: `${color.bg} ${color.text} ${color.border}`,\n    text: color.text,\n    bg: color.bg,\n    border: color.border,\n    hover: color.hover,\n    hex: color.hex\n  }\n}\n\nexport const getCavoColorClasses = (stato: string) => {\n  const normalizedStato = stato?.toUpperCase().replace(/\\s+/g, '_') as keyof typeof CAVO_COLORS\n  const color = CAVO_COLORS[normalizedStato] || CAVO_COLORS.ERRORE\n  \n  return {\n    badge: `${color.bg} ${color.text} ${color.border}`,\n    button: `${color.bg} ${color.text} ${color.border} ${color.hover}`,\n    alert: `${color.bg} ${color.text} ${color.border}`,\n    text: color.text,\n    bg: color.bg,\n    border: color.border,\n    hover: color.hover,\n    hex: color.hex\n  }\n}\n\nexport const getComandaColorClasses = (stato: string) => {\n  const normalizedStato = stato?.toUpperCase().replace(/\\s+/g, '_') as keyof typeof COMANDA_COLORS\n  const color = COMANDA_COLORS[normalizedStato] || COMANDA_COLORS.ERRORE\n  \n  return {\n    badge: `${color.bg} ${color.text} ${color.border}`,\n    button: `${color.bg} ${color.text} ${color.border} ${color.hover}`,\n    alert: `${color.bg} ${color.text} ${color.border}`,\n    text: color.text,\n    bg: color.bg,\n    border: color.border,\n    hover: color.hover,\n    hex: color.hex\n  }\n}\n\n/**\n * Colori per percentuali di progresso\n */\nexport const getProgressColor = (percentage: number) => {\n  if (percentage >= 90) return SOFT_COLORS.SUCCESS\n  if (percentage >= 70) return SOFT_COLORS.PROGRESS\n  if (percentage >= 50) return SOFT_COLORS.INFO\n  if (percentage >= 30) return SOFT_COLORS.WARNING\n  return SOFT_COLORS.ATTENTION\n}\n\n/**\n * Colori per priorità\n */\nexport const PRIORITY_COLORS = {\n  ALTA: SOFT_COLORS.ERROR,\n  MEDIA: SOFT_COLORS.WARNING,\n  BASSA: SOFT_COLORS.INFO,\n  NORMALE: SOFT_COLORS.NEUTRAL\n}\n\nexport const getPriorityColorClasses = (priority: string) => {\n  const normalizedPriority = priority?.toUpperCase() as keyof typeof PRIORITY_COLORS\n  const color = PRIORITY_COLORS[normalizedPriority] || PRIORITY_COLORS.NORMALE\n  \n  return {\n    badge: `${color.bg} ${color.text} ${color.border}`,\n    button: `${color.bg} ${color.text} ${color.border} ${color.hover}`,\n    alert: `${color.bg} ${color.text} ${color.border}`,\n    text: color.text,\n    bg: color.bg,\n    border: color.border,\n    hover: color.hover,\n    hex: color.hex\n  }\n}\n"], "names": [], "mappings": "AAAA;;;CAGC;;;;;;;;;;;;;AAEM,MAAM,cAAc;IACzB,oCAAoC;IACpC,SAAS;QACP,IAAI;QACJ,MAAM;QACN,QAAQ;QACR,OAAO;QACP,KAAK,UAAU,cAAc;IAC/B;IAEA,yCAAyC;IACzC,SAAS;QACP,IAAI;QACJ,MAAM;QACN,QAAQ;QACR,OAAO;QACP,KAAK,UAAU,YAAY;IAC7B;IAEA,wCAAwC;IACxC,WAAW;QACT,IAAI;QACJ,MAAM;QACN,QAAQ;QACR,OAAO;QACP,KAAK,UAAU,aAAa;IAC9B;IAEA,8CAA8C;IAC9C,OAAO;QACL,IAAI;QACJ,MAAM;QACN,QAAQ;QACR,OAAO;QACP,KAAK,UAAU,WAAW;IAC5B;IAEA,kCAAkC;IAClC,MAAM;QACJ,IAAI;QACJ,MAAM;QACN,QAAQ;QACR,OAAO;QACP,KAAK,UAAU,UAAU;IAC3B;IAEA,wBAAwB;IACxB,SAAS;QACP,IAAI;QACJ,MAAM;QACN,QAAQ;QACR,OAAO;QACP,KAAK,UAAU,YAAY;IAC7B;IAEA,8BAA8B;IAC9B,UAAU;QACR,IAAI;QACJ,MAAM;QACN,QAAQ;QACR,OAAO;QACP,KAAK,UAAU,aAAa;IAC9B;AACF;AAKO,MAAM,gBAAgB;IAC3B,aAAa,YAAY,OAAO;IAChC,QAAQ,YAAY,QAAQ;IAC5B,WAAW,YAAY,OAAO;IAC9B,MAAM,YAAY,OAAO;IACzB,QAAQ,YAAY,KAAK;AAC3B;AAKO,MAAM,cAAc;IACzB,eAAe,YAAY,OAAO;IAClC,YAAY,YAAY,OAAO;IAC/B,oBAAoB,YAAY,IAAI;IACpC,kBAAkB,YAAY,IAAI;IAClC,WAAW,YAAY,QAAQ;IAC/B,aAAa,YAAY,OAAO;IAChC,OAAO,YAAY,OAAO;IAC1B,QAAQ,YAAY,KAAK;AAC3B;AAKO,MAAM,iBAAiB;IAC5B,QAAQ,YAAY,OAAO;IAC3B,YAAY,YAAY,QAAQ;IAChC,WAAW,YAAY,OAAO;IAC9B,UAAU,YAAY,IAAI;IAC1B,QAAQ,YAAY,KAAK;AAC3B;AAKO,MAAM,sBAAsB,CAAC;IAClC,MAAM,QAAQ,WAAW,CAAC,UAAU;IACpC,OAAO;QACL,OAAO,GAAG,MAAM,EAAE,CAAC,CAAC,EAAE,MAAM,IAAI,CAAC,CAAC,EAAE,MAAM,MAAM,EAAE;QAClD,QAAQ,GAAG,MAAM,EAAE,CAAC,CAAC,EAAE,MAAM,IAAI,CAAC,CAAC,EAAE,MAAM,MAAM,CAAC,CAAC,EAAE,MAAM,KAAK,EAAE;QAClE,OAAO,GAAG,MAAM,EAAE,CAAC,CAAC,EAAE,MAAM,IAAI,CAAC,CAAC,EAAE,MAAM,MAAM,EAAE;QAClD,MAAM,MAAM,IAAI;QAChB,IAAI,MAAM,EAAE;QACZ,QAAQ,MAAM,MAAM;QACpB,OAAO,MAAM,KAAK;QAClB,KAAK,MAAM,GAAG;IAChB;AACF;AAEO,MAAM,wBAAwB,CAAC;IACpC,MAAM,kBAAkB,OAAO;IAC/B,MAAM,QAAQ,aAAa,CAAC,gBAAgB,IAAI,cAAc,MAAM;IAEpE,OAAO;QACL,OAAO,GAAG,MAAM,EAAE,CAAC,CAAC,EAAE,MAAM,IAAI,CAAC,CAAC,EAAE,MAAM,MAAM,EAAE;QAClD,QAAQ,GAAG,MAAM,EAAE,CAAC,CAAC,EAAE,MAAM,IAAI,CAAC,CAAC,EAAE,MAAM,MAAM,CAAC,CAAC,EAAE,MAAM,KAAK,EAAE;QAClE,OAAO,GAAG,MAAM,EAAE,CAAC,CAAC,EAAE,MAAM,IAAI,CAAC,CAAC,EAAE,MAAM,MAAM,EAAE;QAClD,MAAM,MAAM,IAAI;QAChB,IAAI,MAAM,EAAE;QACZ,QAAQ,MAAM,MAAM;QACpB,OAAO,MAAM,KAAK;QAClB,KAAK,MAAM,GAAG;IAChB;AACF;AAEO,MAAM,sBAAsB,CAAC;IAClC,MAAM,kBAAkB,OAAO,cAAc,QAAQ,QAAQ;IAC7D,MAAM,QAAQ,WAAW,CAAC,gBAAgB,IAAI,YAAY,MAAM;IAEhE,OAAO;QACL,OAAO,GAAG,MAAM,EAAE,CAAC,CAAC,EAAE,MAAM,IAAI,CAAC,CAAC,EAAE,MAAM,MAAM,EAAE;QAClD,QAAQ,GAAG,MAAM,EAAE,CAAC,CAAC,EAAE,MAAM,IAAI,CAAC,CAAC,EAAE,MAAM,MAAM,CAAC,CAAC,EAAE,MAAM,KAAK,EAAE;QAClE,OAAO,GAAG,MAAM,EAAE,CAAC,CAAC,EAAE,MAAM,IAAI,CAAC,CAAC,EAAE,MAAM,MAAM,EAAE;QAClD,MAAM,MAAM,IAAI;QAChB,IAAI,MAAM,EAAE;QACZ,QAAQ,MAAM,MAAM;QACpB,OAAO,MAAM,KAAK;QAClB,KAAK,MAAM,GAAG;IAChB;AACF;AAEO,MAAM,yBAAyB,CAAC;IACrC,MAAM,kBAAkB,OAAO,cAAc,QAAQ,QAAQ;IAC7D,MAAM,QAAQ,cAAc,CAAC,gBAAgB,IAAI,eAAe,MAAM;IAEtE,OAAO;QACL,OAAO,GAAG,MAAM,EAAE,CAAC,CAAC,EAAE,MAAM,IAAI,CAAC,CAAC,EAAE,MAAM,MAAM,EAAE;QAClD,QAAQ,GAAG,MAAM,EAAE,CAAC,CAAC,EAAE,MAAM,IAAI,CAAC,CAAC,EAAE,MAAM,MAAM,CAAC,CAAC,EAAE,MAAM,KAAK,EAAE;QAClE,OAAO,GAAG,MAAM,EAAE,CAAC,CAAC,EAAE,MAAM,IAAI,CAAC,CAAC,EAAE,MAAM,MAAM,EAAE;QAClD,MAAM,MAAM,IAAI;QAChB,IAAI,MAAM,EAAE;QACZ,QAAQ,MAAM,MAAM;QACpB,OAAO,MAAM,KAAK;QAClB,KAAK,MAAM,GAAG;IAChB;AACF;AAKO,MAAM,mBAAmB,CAAC;IAC/B,IAAI,cAAc,IAAI,OAAO,YAAY,OAAO;IAChD,IAAI,cAAc,IAAI,OAAO,YAAY,QAAQ;IACjD,IAAI,cAAc,IAAI,OAAO,YAAY,IAAI;IAC7C,IAAI,cAAc,IAAI,OAAO,YAAY,OAAO;IAChD,OAAO,YAAY,SAAS;AAC9B;AAKO,MAAM,kBAAkB;IAC7B,MAAM,YAAY,KAAK;IACvB,OAAO,YAAY,OAAO;IAC1B,OAAO,YAAY,IAAI;IACvB,SAAS,YAAY,OAAO;AAC9B;AAEO,MAAM,0BAA0B,CAAC;IACtC,MAAM,qBAAqB,UAAU;IACrC,MAAM,QAAQ,eAAe,CAAC,mBAAmB,IAAI,gBAAgB,OAAO;IAE5E,OAAO;QACL,OAAO,GAAG,MAAM,EAAE,CAAC,CAAC,EAAE,MAAM,IAAI,CAAC,CAAC,EAAE,MAAM,MAAM,EAAE;QAClD,QAAQ,GAAG,MAAM,EAAE,CAAC,CAAC,EAAE,MAAM,IAAI,CAAC,CAAC,EAAE,MAAM,MAAM,CAAC,CAAC,EAAE,MAAM,KAAK,EAAE;QAClE,OAAO,GAAG,MAAM,EAAE,CAAC,CAAC,EAAE,MAAM,IAAI,CAAC,CAAC,EAAE,MAAM,MAAM,EAAE;QAClD,MAAM,MAAM,IAAI;QAChB,IAAI,MAAM,EAAE;QACZ,QAAQ,MAAM,MAAM;QACpB,OAAO,MAAM,KAAK;QAClB,KAAK,MAAM,GAAG;IAChB;AACF", "debugId": null}}, {"offset": {"line": 5337, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/CMS/webapp-nextjs/src/app/parco-cavi/page.tsx"], "sourcesContent": ["'use client'\n\nimport { useState, useEffect } from 'react'\nimport { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'\nimport { Button } from '@/components/ui/button'\nimport { Badge } from '@/components/ui/badge'\nimport { Input } from '@/components/ui/input'\nimport { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table'\nimport { Progress } from '@/components/ui/progress'\nimport { Alert, AlertDescription } from '@/components/ui/alert'\nimport { useAuth } from '@/contexts/AuthContext'\nimport { parcoCaviApi } from '@/lib/api'\nimport { ParcoCavo } from '@/types'\nimport {\n  REEL_STATES,\n  getReelStateColor,\n  getReelRowColor,\n  determineReelState,\n  calculateReelUsagePercentage,\n  formatMeters,\n  getReelStateDescription,\n  canReelAcceptNewCables\n} from '@/utils/bobineUtils'\nimport CreaBobinaDialog from '@/components/bobine/CreaBobinaDialog'\nimport ModificaBobinaDialog from '@/components/bobine/ModificaBobinaDialog'\nimport EliminaBobinaDialog from '@/components/bobine/EliminaBobinaDialog'\nimport AggiungiCaviDialogSimple from '@/components/bobine/AggiungiCaviDialogSimple'\nimport BobineStatistics from '@/components/bobine/BobineStatistics'\nimport {\n  Package,\n  Search,\n  Plus,\n  Edit,\n  Trash2,\n  AlertCircle,\n  CheckCircle,\n  Clock,\n  Download,\n  Upload,\n  Loader2,\n  Cable,\n  FileDown,\n  FileUp\n} from 'lucide-react'\nimport { ContextMenuCustom } from '@/components/ui/context-menu-custom'\nimport { getBobinaColorClasses } from '@/utils/softColors'\n\nexport default function ParcoCaviPage() {\n  const [searchTerm, setSearchTerm] = useState('')\n  const [selectedStatus, setSelectedStatus] = useState('all')\n  const [bobine, setBobine] = useState<ParcoCavo[]>([])\n  const [isLoading, setIsLoading] = useState(true)\n  const [error, setError] = useState('')\n\n  const { user, cantiere, isLoading: authLoading } = useAuth()\n\n  // Get cantiere ID con fallback al localStorage come nella pagina cavi\n  const [cantiereId, setCantiereId] = useState<number>(0)\n\n  useEffect(() => {\n    if (typeof window !== 'undefined') {\n      const storedId = cantiere?.id_cantiere || parseInt(localStorage.getItem('selectedCantiereId') || '0')\n      setCantiereId(storedId)\n    }\n  }, [cantiere])\n\n  // Stati per i dialoghi\n  const [showCreateDialog, setShowCreateDialog] = useState(false)\n  const [showEditDialog, setShowEditDialog] = useState(false)\n  const [showDeleteDialog, setShowDeleteDialog] = useState(false)\n  const [showAddCavoDialog, setShowAddCavoDialog] = useState(false)\n  const [selectedBobina, setSelectedBobina] = useState<ParcoCavo | null>(null)\n\n  // Stati per notifiche\n  const [successMessage, setSuccessMessage] = useState('')\n  const [errorMessage, setErrorMessage] = useState('')\n\n  // Carica le bobine dal backend\n  useEffect(() => {\n    if (cantiereId && cantiereId > 0) {\n      loadBobine()\n    } else if (!authLoading) {\n      // Solo se non stiamo caricando, mostra l'errore\n      setError('Cantiere non selezionato. Seleziona un cantiere dal menu principale per visualizzare e gestire le bobine.')\n      setBobine([])\n    }\n  }, [cantiereId, authLoading])\n\n  const loadBobine = async () => {\n    try {\n      setIsLoading(true)\n      setError('')\n\n      // CONTROLLO: Il cantiere DEVE essere selezionato\n      if (!cantiereId || cantiereId <= 0) {\n        setError('Cantiere non selezionato. Seleziona un cantiere per visualizzare le bobine.')\n        setBobine([]) // Svuota la lista per sicurezza\n        return\n      }\n\n      const data = await parcoCaviApi.getBobine(cantiereId)\n      setBobine(data || [])\n    } catch (error: any) {\n      setError(error.response?.data?.detail || 'Errore durante il caricamento delle bobine')\n      setBobine([]) // Svuota la lista in caso di errore\n    } finally {\n      setIsLoading(false)\n    }\n  }\n\n  // I filtri vengono applicati solo lato client per sicurezza\n  // Non ricarichiamo dal server quando cambiano i filtri\n\n  // Gestione notifiche\n  useEffect(() => {\n    if (successMessage) {\n      const timer = setTimeout(() => setSuccessMessage(''), 5000)\n      return () => clearTimeout(timer)\n    }\n  }, [successMessage])\n\n  useEffect(() => {\n    if (errorMessage) {\n      const timer = setTimeout(() => setErrorMessage(''), 5000)\n      return () => clearTimeout(timer)\n    }\n  }, [errorMessage])\n\n  // Funzioni per gestire i dialoghi\n  const handleAddCavoToBobina = (bobina: ParcoCavo) => {\n    setSelectedBobina(bobina)\n    setShowAddCavoDialog(true)\n  }\n\n  const handleEditBobina = (bobina: ParcoCavo) => {\n    setSelectedBobina(bobina)\n    setShowEditDialog(true)\n  }\n\n  const handleDeleteBobina = (bobina: ParcoCavo) => {\n    setSelectedBobina(bobina)\n    setShowDeleteDialog(true)\n  }\n\n  const handleCreateSuccess = (message: string) => {\n    setSuccessMessage(message)\n    loadBobine() // Ricarica la lista\n  }\n\n  const handleCreateError = (message: string) => {\n    setErrorMessage(message)\n  }\n\n  const handleEditSuccess = (message: string) => {\n    setSuccessMessage(message)\n    loadBobine() // Ricarica la lista\n  }\n\n  const handleEditError = (message: string) => {\n    setErrorMessage(message)\n  }\n\n  const handleDeleteSuccess = (message: string) => {\n    setSuccessMessage(message)\n    loadBobine() // Ricarica la lista\n  }\n\n  const handleDeleteError = (message: string) => {\n    setErrorMessage(message)\n  }\n\n  // Gestione menu contestuale\n  const handleContextMenuAction = (action: string, data?: any) => {\n\n    switch (action) {\n      case 'import':\n        handleImportBobine()\n        break\n      case 'export':\n        handleExportBobine()\n        break\n      case 'add_bobina':\n        setShowCreateDialog(true)\n        break\n      default:\n    }\n  }\n\n  const handleImportBobine = () => {\n    // TODO: Implementare import bobine\n    setSuccessMessage('Funzione import in sviluppo')\n  }\n\n  const handleExportBobine = () => {\n    // TODO: Implementare export bobine\n    setSuccessMessage('Funzione export in sviluppo')\n  }\n\n  // Menu contestuale items\n  const getContextMenuItems = () => [\n    {\n      id: 'import',\n      label: 'Importa Bobine',\n      icon: <FileUp className=\"h-4 w-4\" />,\n      action: 'import',\n      disabled: !cantiereId || cantiereId <= 0\n    },\n    {\n      id: 'export',\n      label: 'Esporta Bobine',\n      icon: <FileDown className=\"h-4 w-4\" />,\n      action: 'export',\n      disabled: !cantiereId || cantiereId <= 0\n    },\n    {\n      id: 'separator1',\n      separator: true\n    },\n    {\n      id: 'add_bobina',\n      label: 'Aggiungi Bobina',\n      icon: <Plus className=\"h-4 w-4\" />,\n      action: 'add_bobina',\n      disabled: !cantiereId || cantiereId <= 0\n    }\n  ]\n\n  const getStatusBadge = (stato: string, metri_residui: number, metri_totali: number) => {\n    // Determina lo stato effettivo della bobina\n    const statoEffettivo = stato || determineReelState(metri_residui, metri_totali)\n    const colorClasses = getBobinaColorClasses(statoEffettivo)\n\n    const stateIcons = {\n      'disponibile': CheckCircle,\n      'in_uso': Clock,\n      'terminata': AlertCircle,\n      'over': AlertCircle\n    }\n\n    const Icon = stateIcons[statoEffettivo?.toLowerCase() as keyof typeof stateIcons] || AlertCircle\n\n    return (\n      <Badge\n        className={`flex items-center gap-1 font-medium ${colorClasses.badge}`}\n        title={getReelStateDescription(statoEffettivo)}\n      >\n        <Icon className={`h-3 w-3 ${colorClasses.text}`} />\n        {statoEffettivo.toUpperCase()}\n      </Badge>\n    )\n  }\n\n  const filteredBobine = bobine.filter(bobina => {\n    const matchesSearch = bobina.numero_bobina?.toLowerCase().includes(searchTerm.toLowerCase()) ||\n                         bobina.tipologia?.toLowerCase().includes(searchTerm.toLowerCase()) ||\n                         bobina.utility?.toLowerCase().includes(searchTerm.toLowerCase())\n\n    let matchesStatus = true\n    if (selectedStatus !== 'all') {\n      // Determina lo stato effettivo della bobina\n      const statoEffettivo = bobina.stato_bobina || determineReelState(bobina.metri_residui, bobina.metri_totali)\n\n      switch (selectedStatus) {\n        case 'disponibile':\n          matchesStatus = statoEffettivo === REEL_STATES.DISPONIBILE\n          break\n        case 'in_uso':\n          matchesStatus = statoEffettivo === REEL_STATES.IN_USO\n          break\n        case 'esaurita':\n          matchesStatus = statoEffettivo === REEL_STATES.TERMINATA\n          break\n        case 'over':\n          matchesStatus = statoEffettivo === REEL_STATES.OVER\n          break\n      }\n    }\n\n    return matchesSearch && matchesStatus\n  })\n\n  return (\n    <div className=\"min-h-screen bg-gradient-to-br from-slate-50 to-slate-100 p-6\">\n      <div className=\"max-w-[90%] mx-auto space-y-6\">\n\n        {/* Avviso cantiere non selezionato */}\n        {(!cantiereId || cantiereId <= 0) && !authLoading && (\n          <Alert variant=\"destructive\">\n            <AlertCircle className=\"h-4 w-4\" />\n            <AlertDescription>\n              <strong>Attenzione:</strong> Nessun cantiere selezionato.\n              Seleziona un cantiere dal menu principale per visualizzare e gestire le bobine.\n            </AlertDescription>\n          </Alert>\n        )}\n\n        {/* Statistics */}\n        <BobineStatistics\n          bobine={bobine}\n          filteredBobine={filteredBobine}\n          className=\"mb-6\"\n        />\n\n        {/* Filters and Search */}\n        <Card>\n          <CardHeader>\n            <CardTitle className=\"flex items-center gap-2\">\n              <Search className=\"h-5 w-5\" />\n              Ricerca e Filtri\n            </CardTitle>\n          </CardHeader>\n          <CardContent>\n            <div className=\"flex gap-4\">\n              <div className=\"flex-1\">\n                <Input\n                  placeholder=\"Cerca per bobina, tipologia o utility...\"\n                  value={searchTerm}\n                  onChange={(e) => setSearchTerm(e.target.value)}\n                  className=\"w-full\"\n                />\n              </div>\n              <div className=\"flex gap-2\">\n                {['all', 'disponibile', 'in_uso', 'esaurita', 'over'].map((status) => (\n                  <Button\n                    key={status}\n                    variant={selectedStatus === status ? 'default' : 'outline'}\n                    size=\"sm\"\n                    onClick={() => setSelectedStatus(status)}\n                  >\n                    {status === 'all' ? 'Tutte' :\n                     status === 'disponibile' ? 'Disponibili' :\n                     status === 'in_uso' ? 'In Uso' :\n                     status === 'esaurita' ? 'Esaurite' : 'Over'}\n                  </Button>\n                ))}\n              </div>\n            </div>\n          </CardContent>\n        </Card>\n\n        {/* Bobine Table */}\n        <ContextMenuCustom\n          items={getContextMenuItems()}\n          onAction={handleContextMenuAction}\n        >\n          <Card>\n            <CardHeader>\n              <div className=\"flex items-center justify-between\">\n                <div>\n                  <CardTitle>Elenco Bobine ({filteredBobine.length})</CardTitle>\n                  <CardDescription>\n                    Gestione completa delle bobine con stato utilizzo e metrature. Clicca tasto destro per opzioni aggiuntive.\n                  </CardDescription>\n                </div>\n                <Button\n                  size=\"sm\"\n                  onClick={() => setShowCreateDialog(true)}\n                  disabled={!cantiereId || cantiereId <= 0}\n                  title={(!cantiereId || cantiereId <= 0) ? 'Seleziona un cantiere per creare una bobina' : 'Crea nuova bobina'}\n                >\n                  <Plus className=\"h-4 w-4 mr-2\" />\n                  Nuova Bobina\n                </Button>\n              </div>\n            </CardHeader>\n            <CardContent>\n              <div className=\"rounded-md border\">\n                <Table>\n                  <TableHeader>\n                    <TableRow>\n                      <TableHead>Bobina</TableHead>\n                      <TableHead>Utility</TableHead>\n                      <TableHead>Tipologia</TableHead>\n                      <TableHead>Formazione</TableHead>\n                      <TableHead>Metrature</TableHead>\n                      <TableHead>Utilizzo</TableHead>\n                      <TableHead>Stato</TableHead>\n                      <TableHead>Ubicazione</TableHead>\n                      <TableHead>Azioni</TableHead>\n                    </TableRow>\n                  </TableHeader>\n                  <TableBody>\n                  {isLoading ? (\n                    <TableRow>\n                      <TableCell colSpan={9} className=\"text-center py-8\">\n                        <div className=\"flex items-center justify-center gap-2\">\n                          <Loader2 className=\"h-4 w-4 animate-spin\" />\n                          Caricamento bobine...\n                        </div>\n                      </TableCell>\n                    </TableRow>\n                  ) : error ? (\n                    <TableRow>\n                      <TableCell colSpan={9} className=\"text-center py-8\">\n                        <div className=\"flex items-center justify-center gap-2 text-red-600\">\n                          <AlertCircle className=\"h-4 w-4\" />\n                          {error}\n                        </div>\n                      </TableCell>\n                    </TableRow>\n                  ) : filteredBobine.length === 0 ? (\n                    <TableRow>\n                      <TableCell colSpan={9} className=\"text-center py-8 text-slate-500\">\n                        Nessuna bobina trovata\n                      </TableCell>\n                    </TableRow>\n                  ) : (\n                    filteredBobine.map((bobina) => {\n                      const percentualeUtilizzo = calculateReelUsagePercentage(bobina.metri_residui, bobina.metri_totali)\n                      const statoEffettivo = bobina.stato_bobina || determineReelState(bobina.metri_residui, bobina.metri_totali)\n                      const rowColorClass = getReelRowColor(statoEffettivo)\n\n                      return (\n                        <TableRow\n                          key={bobina.id_bobina}\n                          className={`transition-colors ${rowColorClass}`}\n                        >\n                          <TableCell className=\"font-medium\">{bobina.numero_bobina || '-'}</TableCell>\n                          <TableCell>{bobina.utility || '-'}</TableCell>\n                          <TableCell>{bobina.tipologia || '-'}</TableCell>\n                          <TableCell>\n                            <div className=\"text-sm font-medium\">\n                              {bobina.sezione || '-'}\n                            </div>\n                          </TableCell>\n                          <TableCell>\n                            <div className=\"text-sm\">\n                              <div>Residui: <span className=\"font-medium\">{formatMeters(bobina.metri_residui)}</span></div>\n                              <div className=\"text-slate-500\">Totali: {formatMeters(bobina.metri_totali)}</div>\n                            </div>\n                          </TableCell>\n                          <TableCell>\n                            <div className=\"text-sm font-medium\">{Math.round(percentualeUtilizzo)}%</div>\n                          </TableCell>\n                          <TableCell>\n                            {getStatusBadge(bobina.stato_bobina, bobina.metri_residui, bobina.metri_totali)}\n                          </TableCell>\n                          <TableCell>\n                            <Badge variant=\"outline\">{bobina.ubicazione_bobina || 'Non specificata'}</Badge>\n                          </TableCell>\n                          <TableCell>\n                            <div className=\"flex gap-1\">\n                              <Button\n                                variant=\"ghost\"\n                                size=\"sm\"\n                                onClick={() => handleAddCavoToBobina(bobina)}\n                                disabled={!canReelAcceptNewCables(statoEffettivo)}\n                                title={\n                                  statoEffettivo === REEL_STATES.OVER\n                                    ? \"Bobina OVER - Non può accettare nuovi cavi\"\n                                    : statoEffettivo === REEL_STATES.TERMINATA\n                                    ? \"Bobina terminata - Non può accettare nuovi cavi\"\n                                    : \"Aggiungi cavo a bobina\"\n                                }\n                                className={\n                                  !canReelAcceptNewCables(statoEffettivo)\n                                    ? \"opacity-50 cursor-not-allowed\"\n                                    : \"\"\n                                }\n                              >\n                                <Cable className=\"h-4 w-4\" />\n                              </Button>\n                              <Button\n                                variant=\"ghost\"\n                                size=\"sm\"\n                                onClick={() => handleEditBobina(bobina)}\n                                title={\n                                  statoEffettivo === REEL_STATES.OVER\n                                    ? \"Modifica bobina (limitata per bobine OVER)\"\n                                    : \"Modifica bobina\"\n                                }\n                              >\n                                <Edit className=\"h-4 w-4\" />\n                              </Button>\n                              <Button\n                                variant=\"ghost\"\n                                size=\"sm\"\n                                onClick={() => handleDeleteBobina(bobina)}\n                                disabled={statoEffettivo === REEL_STATES.OVER || statoEffettivo !== REEL_STATES.DISPONIBILE}\n                                title={\n                                  statoEffettivo === REEL_STATES.OVER\n                                    ? \"Bobina OVER - Non può essere eliminata\"\n                                    : statoEffettivo !== REEL_STATES.DISPONIBILE\n                                    ? \"Solo bobine disponibili possono essere eliminate\"\n                                    : \"Elimina bobina\"\n                                }\n                                className={\n                                  (statoEffettivo === REEL_STATES.OVER || statoEffettivo !== REEL_STATES.DISPONIBILE)\n                                    ? \"opacity-50 cursor-not-allowed\"\n                                    : \"\"\n                                }\n                              >\n                                <Trash2 className=\"h-4 w-4\" />\n                              </Button>\n                            </div>\n                          </TableCell>\n                        </TableRow>\n                      )\n                    })\n                  )}\n                </TableBody>\n              </Table>\n            </div>\n          </CardContent>\n        </Card>\n        </ContextMenuCustom>\n\n        {/* Notifiche */}\n        {successMessage && (\n          <div className=\"fixed top-4 right-4 z-50\">\n            <Alert className=\"bg-green-50 border-green-200\">\n              <CheckCircle className=\"h-4 w-4 text-green-600\" />\n              <AlertDescription className=\"text-green-800\">\n                {successMessage}\n              </AlertDescription>\n            </Alert>\n          </div>\n        )}\n\n        {errorMessage && (\n          <div className=\"fixed top-4 right-4 z-50\">\n            <Alert variant=\"destructive\">\n              <AlertCircle className=\"h-4 w-4\" />\n              <AlertDescription>\n                {errorMessage}\n              </AlertDescription>\n            </Alert>\n          </div>\n        )}\n\n      </div>\n\n      {/* Dialoghi */}\n      <CreaBobinaDialog\n        open={showCreateDialog}\n        onClose={() => setShowCreateDialog(false)}\n        cantiereId={cantiereId}\n        onSuccess={handleCreateSuccess}\n        onError={handleCreateError}\n      />\n\n      <ModificaBobinaDialog\n        open={showEditDialog}\n        onClose={() => setShowEditDialog(false)}\n        bobina={selectedBobina}\n        cantiereId={cantiereId}\n        onSuccess={handleEditSuccess}\n        onError={handleEditError}\n      />\n\n      <EliminaBobinaDialog\n        open={showDeleteDialog}\n        onClose={() => setShowDeleteDialog(false)}\n        bobina={selectedBobina}\n        cantiereId={cantiereId}\n        onSuccess={handleDeleteSuccess}\n        onError={handleDeleteError}\n      />\n\n      <AggiungiCaviDialogSimple\n        open={showAddCavoDialog}\n        onClose={() => setShowAddCavoDialog(false)}\n        bobina={selectedBobina}\n        cantiereId={cantiereId}\n        onSuccess={handleCreateSuccess}\n        onError={handleCreateError}\n      />\n    </div>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AACA;AAEA;AACA;AACA;AAEA;AAUA;AACA;AACA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAgBA;AACA;AA7CA;;;;;;;;;;;;;;;;;;;;AA+Ce,SAAS;IACtB,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC7C,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACrD,MAAM,CAAC,QAAQ,UAAU,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAe,EAAE;IACpD,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC3C,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAEnC,MAAM,EAAE,IAAI,EAAE,QAAQ,EAAE,WAAW,WAAW,EAAE,GAAG,CAAA,GAAA,+HAAA,CAAA,UAAO,AAAD;IAEzD,sEAAsE;IACtE,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAU;IAErD,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,uCAAmC;;QAGnC;IACF,GAAG;QAAC;KAAS;IAEb,uBAAuB;IACvB,MAAM,CAAC,kBAAkB,oBAAoB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACzD,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACrD,MAAM,CAAC,kBAAkB,oBAAoB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACzD,MAAM,CAAC,mBAAmB,qBAAqB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC3D,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAoB;IAEvE,sBAAsB;IACtB,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACrD,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAEjD,+BAA+B;IAC/B,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,IAAI,cAAc,aAAa,GAAG;YAChC;QACF,OAAO,IAAI,CAAC,aAAa;YACvB,gDAAgD;YAChD,SAAS;YACT,UAAU,EAAE;QACd;IACF,GAAG;QAAC;QAAY;KAAY;IAE5B,MAAM,aAAa;QACjB,IAAI;YACF,aAAa;YACb,SAAS;YAET,iDAAiD;YACjD,IAAI,CAAC,cAAc,cAAc,GAAG;gBAClC,SAAS;gBACT,UAAU,EAAE,EAAE,gCAAgC;;gBAC9C;YACF;YAEA,MAAM,OAAO,MAAM,iHAAA,CAAA,eAAY,CAAC,SAAS,CAAC;YAC1C,UAAU,QAAQ,EAAE;QACtB,EAAE,OAAO,OAAY;YACnB,SAAS,MAAM,QAAQ,EAAE,MAAM,UAAU;YACzC,UAAU,EAAE,EAAE,oCAAoC;;QACpD,SAAU;YACR,aAAa;QACf;IACF;IAEA,4DAA4D;IAC5D,uDAAuD;IAEvD,qBAAqB;IACrB,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,IAAI,gBAAgB;YAClB,MAAM,QAAQ,WAAW,IAAM,kBAAkB,KAAK;YACtD,OAAO,IAAM,aAAa;QAC5B;IACF,GAAG;QAAC;KAAe;IAEnB,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,IAAI,cAAc;YAChB,MAAM,QAAQ,WAAW,IAAM,gBAAgB,KAAK;YACpD,OAAO,IAAM,aAAa;QAC5B;IACF,GAAG;QAAC;KAAa;IAEjB,kCAAkC;IAClC,MAAM,wBAAwB,CAAC;QAC7B,kBAAkB;QAClB,qBAAqB;IACvB;IAEA,MAAM,mBAAmB,CAAC;QACxB,kBAAkB;QAClB,kBAAkB;IACpB;IAEA,MAAM,qBAAqB,CAAC;QAC1B,kBAAkB;QAClB,oBAAoB;IACtB;IAEA,MAAM,sBAAsB,CAAC;QAC3B,kBAAkB;QAClB,aAAa,oBAAoB;;IACnC;IAEA,MAAM,oBAAoB,CAAC;QACzB,gBAAgB;IAClB;IAEA,MAAM,oBAAoB,CAAC;QACzB,kBAAkB;QAClB,aAAa,oBAAoB;;IACnC;IAEA,MAAM,kBAAkB,CAAC;QACvB,gBAAgB;IAClB;IAEA,MAAM,sBAAsB,CAAC;QAC3B,kBAAkB;QAClB,aAAa,oBAAoB;;IACnC;IAEA,MAAM,oBAAoB,CAAC;QACzB,gBAAgB;IAClB;IAEA,4BAA4B;IAC5B,MAAM,0BAA0B,CAAC,QAAgB;QAE/C,OAAQ;YACN,KAAK;gBACH;gBACA;YACF,KAAK;gBACH;gBACA;YACF,KAAK;gBACH,oBAAoB;gBACpB;YACF;QACF;IACF;IAEA,MAAM,qBAAqB;QACzB,mCAAmC;QACnC,kBAAkB;IACpB;IAEA,MAAM,qBAAqB;QACzB,mCAAmC;QACnC,kBAAkB;IACpB;IAEA,yBAAyB;IACzB,MAAM,sBAAsB,IAAM;YAChC;gBACE,IAAI;gBACJ,OAAO;gBACP,oBAAM,8OAAC,0MAAA,CAAA,SAAM;oBAAC,WAAU;;;;;;gBACxB,QAAQ;gBACR,UAAU,CAAC,cAAc,cAAc;YACzC;YACA;gBACE,IAAI;gBACJ,OAAO;gBACP,oBAAM,8OAAC,8MAAA,CAAA,WAAQ;oBAAC,WAAU;;;;;;gBAC1B,QAAQ;gBACR,UAAU,CAAC,cAAc,cAAc;YACzC;YACA;gBACE,IAAI;gBACJ,WAAW;YACb;YACA;gBACE,IAAI;gBACJ,OAAO;gBACP,oBAAM,8OAAC,kMAAA,CAAA,OAAI;oBAAC,WAAU;;;;;;gBACtB,QAAQ;gBACR,UAAU,CAAC,cAAc,cAAc;YACzC;SACD;IAED,MAAM,iBAAiB,CAAC,OAAe,eAAuB;QAC5D,4CAA4C;QAC5C,MAAM,iBAAiB,SAAS,CAAA,GAAA,2HAAA,CAAA,qBAAkB,AAAD,EAAE,eAAe;QAClE,MAAM,eAAe,CAAA,GAAA,0HAAA,CAAA,wBAAqB,AAAD,EAAE;QAE3C,MAAM,aAAa;YACjB,eAAe,2NAAA,CAAA,cAAW;YAC1B,UAAU,oMAAA,CAAA,QAAK;YACf,aAAa,oNAAA,CAAA,cAAW;YACxB,QAAQ,oNAAA,CAAA,cAAW;QACrB;QAEA,MAAM,OAAO,UAAU,CAAC,gBAAgB,cAAyC,IAAI,oNAAA,CAAA,cAAW;QAEhG,qBACE,8OAAC,iIAAA,CAAA,QAAK;YACJ,WAAW,CAAC,oCAAoC,EAAE,aAAa,KAAK,EAAE;YACtE,OAAO,CAAA,GAAA,2HAAA,CAAA,0BAAuB,AAAD,EAAE;;8BAE/B,8OAAC;oBAAK,WAAW,CAAC,QAAQ,EAAE,aAAa,IAAI,EAAE;;;;;;gBAC9C,eAAe,WAAW;;;;;;;IAGjC;IAEA,MAAM,iBAAiB,OAAO,MAAM,CAAC,CAAA;QACnC,MAAM,gBAAgB,OAAO,aAAa,EAAE,cAAc,SAAS,WAAW,WAAW,OACpE,OAAO,SAAS,EAAE,cAAc,SAAS,WAAW,WAAW,OAC/D,OAAO,OAAO,EAAE,cAAc,SAAS,WAAW,WAAW;QAElF,IAAI,gBAAgB;QACpB,IAAI,mBAAmB,OAAO;YAC5B,4CAA4C;YAC5C,MAAM,iBAAiB,OAAO,YAAY,IAAI,CAAA,GAAA,2HAAA,CAAA,qBAAkB,AAAD,EAAE,OAAO,aAAa,EAAE,OAAO,YAAY;YAE1G,OAAQ;gBACN,KAAK;oBACH,gBAAgB,mBAAmB,2HAAA,CAAA,cAAW,CAAC,WAAW;oBAC1D;gBACF,KAAK;oBACH,gBAAgB,mBAAmB,2HAAA,CAAA,cAAW,CAAC,MAAM;oBACrD;gBACF,KAAK;oBACH,gBAAgB,mBAAmB,2HAAA,CAAA,cAAW,CAAC,SAAS;oBACxD;gBACF,KAAK;oBACH,gBAAgB,mBAAmB,2HAAA,CAAA,cAAW,CAAC,IAAI;oBACnD;YACJ;QACF;QAEA,OAAO,iBAAiB;IAC1B;IAEA,qBACE,8OAAC;QAAI,WAAU;;0BACb,8OAAC;gBAAI,WAAU;;oBAGZ,CAAC,CAAC,cAAc,cAAc,CAAC,KAAK,CAAC,6BACpC,8OAAC,iIAAA,CAAA,QAAK;wBAAC,SAAQ;;0CACb,8OAAC,oNAAA,CAAA,cAAW;gCAAC,WAAU;;;;;;0CACvB,8OAAC,iIAAA,CAAA,mBAAgB;;kDACf,8OAAC;kDAAO;;;;;;oCAAoB;;;;;;;;;;;;;kCAOlC,8OAAC,gJAAA,CAAA,UAAgB;wBACf,QAAQ;wBACR,gBAAgB;wBAChB,WAAU;;;;;;kCAIZ,8OAAC,gIAAA,CAAA,OAAI;;0CACH,8OAAC,gIAAA,CAAA,aAAU;0CACT,cAAA,8OAAC,gIAAA,CAAA,YAAS;oCAAC,WAAU;;sDACnB,8OAAC,sMAAA,CAAA,SAAM;4CAAC,WAAU;;;;;;wCAAY;;;;;;;;;;;;0CAIlC,8OAAC,gIAAA,CAAA,cAAW;0CACV,cAAA,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAI,WAAU;sDACb,cAAA,8OAAC,iIAAA,CAAA,QAAK;gDACJ,aAAY;gDACZ,OAAO;gDACP,UAAU,CAAC,IAAM,cAAc,EAAE,MAAM,CAAC,KAAK;gDAC7C,WAAU;;;;;;;;;;;sDAGd,8OAAC;4CAAI,WAAU;sDACZ;gDAAC;gDAAO;gDAAe;gDAAU;gDAAY;6CAAO,CAAC,GAAG,CAAC,CAAC,uBACzD,8OAAC,kIAAA,CAAA,SAAM;oDAEL,SAAS,mBAAmB,SAAS,YAAY;oDACjD,MAAK;oDACL,SAAS,IAAM,kBAAkB;8DAEhC,WAAW,QAAQ,UACnB,WAAW,gBAAgB,gBAC3B,WAAW,WAAW,WACtB,WAAW,aAAa,aAAa;mDARjC;;;;;;;;;;;;;;;;;;;;;;;;;;;kCAiBjB,8OAAC,qJAAA,CAAA,oBAAiB;wBAChB,OAAO;wBACP,UAAU;kCAEV,cAAA,8OAAC,gIAAA,CAAA,OAAI;;8CACH,8OAAC,gIAAA,CAAA,aAAU;8CACT,cAAA,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;;kEACC,8OAAC,gIAAA,CAAA,YAAS;;4DAAC;4DAAgB,eAAe,MAAM;4DAAC;;;;;;;kEACjD,8OAAC,gIAAA,CAAA,kBAAe;kEAAC;;;;;;;;;;;;0DAInB,8OAAC,kIAAA,CAAA,SAAM;gDACL,MAAK;gDACL,SAAS,IAAM,oBAAoB;gDACnC,UAAU,CAAC,cAAc,cAAc;gDACvC,OAAO,AAAC,CAAC,cAAc,cAAc,IAAK,gDAAgD;;kEAE1F,8OAAC,kMAAA,CAAA,OAAI;wDAAC,WAAU;;;;;;oDAAiB;;;;;;;;;;;;;;;;;;8CAKvC,8OAAC,gIAAA,CAAA,cAAW;8CACV,cAAA,8OAAC;wCAAI,WAAU;kDACb,cAAA,8OAAC,iIAAA,CAAA,QAAK;;8DACJ,8OAAC,iIAAA,CAAA,cAAW;8DACV,cAAA,8OAAC,iIAAA,CAAA,WAAQ;;0EACP,8OAAC,iIAAA,CAAA,YAAS;0EAAC;;;;;;0EACX,8OAAC,iIAAA,CAAA,YAAS;0EAAC;;;;;;0EACX,8OAAC,iIAAA,CAAA,YAAS;0EAAC;;;;;;0EACX,8OAAC,iIAAA,CAAA,YAAS;0EAAC;;;;;;0EACX,8OAAC,iIAAA,CAAA,YAAS;0EAAC;;;;;;0EACX,8OAAC,iIAAA,CAAA,YAAS;0EAAC;;;;;;0EACX,8OAAC,iIAAA,CAAA,YAAS;0EAAC;;;;;;0EACX,8OAAC,iIAAA,CAAA,YAAS;0EAAC;;;;;;0EACX,8OAAC,iIAAA,CAAA,YAAS;0EAAC;;;;;;;;;;;;;;;;;8DAGf,8OAAC,iIAAA,CAAA,YAAS;8DACT,0BACC,8OAAC,iIAAA,CAAA,WAAQ;kEACP,cAAA,8OAAC,iIAAA,CAAA,YAAS;4DAAC,SAAS;4DAAG,WAAU;sEAC/B,cAAA,8OAAC;gEAAI,WAAU;;kFACb,8OAAC,iNAAA,CAAA,UAAO;wEAAC,WAAU;;;;;;oEAAyB;;;;;;;;;;;;;;;;+DAKhD,sBACF,8OAAC,iIAAA,CAAA,WAAQ;kEACP,cAAA,8OAAC,iIAAA,CAAA,YAAS;4DAAC,SAAS;4DAAG,WAAU;sEAC/B,cAAA,8OAAC;gEAAI,WAAU;;kFACb,8OAAC,oNAAA,CAAA,cAAW;wEAAC,WAAU;;;;;;oEACtB;;;;;;;;;;;;;;;;+DAIL,eAAe,MAAM,KAAK,kBAC5B,8OAAC,iIAAA,CAAA,WAAQ;kEACP,cAAA,8OAAC,iIAAA,CAAA,YAAS;4DAAC,SAAS;4DAAG,WAAU;sEAAkC;;;;;;;;;;+DAKrE,eAAe,GAAG,CAAC,CAAC;wDAClB,MAAM,sBAAsB,CAAA,GAAA,2HAAA,CAAA,+BAA4B,AAAD,EAAE,OAAO,aAAa,EAAE,OAAO,YAAY;wDAClG,MAAM,iBAAiB,OAAO,YAAY,IAAI,CAAA,GAAA,2HAAA,CAAA,qBAAkB,AAAD,EAAE,OAAO,aAAa,EAAE,OAAO,YAAY;wDAC1G,MAAM,gBAAgB,CAAA,GAAA,2HAAA,CAAA,kBAAe,AAAD,EAAE;wDAEtC,qBACE,8OAAC,iIAAA,CAAA,WAAQ;4DAEP,WAAW,CAAC,kBAAkB,EAAE,eAAe;;8EAE/C,8OAAC,iIAAA,CAAA,YAAS;oEAAC,WAAU;8EAAe,OAAO,aAAa,IAAI;;;;;;8EAC5D,8OAAC,iIAAA,CAAA,YAAS;8EAAE,OAAO,OAAO,IAAI;;;;;;8EAC9B,8OAAC,iIAAA,CAAA,YAAS;8EAAE,OAAO,SAAS,IAAI;;;;;;8EAChC,8OAAC,iIAAA,CAAA,YAAS;8EACR,cAAA,8OAAC;wEAAI,WAAU;kFACZ,OAAO,OAAO,IAAI;;;;;;;;;;;8EAGvB,8OAAC,iIAAA,CAAA,YAAS;8EACR,cAAA,8OAAC;wEAAI,WAAU;;0FACb,8OAAC;;oFAAI;kGAAS,8OAAC;wFAAK,WAAU;kGAAe,CAAA,GAAA,2HAAA,CAAA,eAAY,AAAD,EAAE,OAAO,aAAa;;;;;;;;;;;;0FAC9E,8OAAC;gFAAI,WAAU;;oFAAiB;oFAAS,CAAA,GAAA,2HAAA,CAAA,eAAY,AAAD,EAAE,OAAO,YAAY;;;;;;;;;;;;;;;;;;8EAG7E,8OAAC,iIAAA,CAAA,YAAS;8EACR,cAAA,8OAAC;wEAAI,WAAU;;4EAAuB,KAAK,KAAK,CAAC;4EAAqB;;;;;;;;;;;;8EAExE,8OAAC,iIAAA,CAAA,YAAS;8EACP,eAAe,OAAO,YAAY,EAAE,OAAO,aAAa,EAAE,OAAO,YAAY;;;;;;8EAEhF,8OAAC,iIAAA,CAAA,YAAS;8EACR,cAAA,8OAAC,iIAAA,CAAA,QAAK;wEAAC,SAAQ;kFAAW,OAAO,iBAAiB,IAAI;;;;;;;;;;;8EAExD,8OAAC,iIAAA,CAAA,YAAS;8EACR,cAAA,8OAAC;wEAAI,WAAU;;0FACb,8OAAC,kIAAA,CAAA,SAAM;gFACL,SAAQ;gFACR,MAAK;gFACL,SAAS,IAAM,sBAAsB;gFACrC,UAAU,CAAC,CAAA,GAAA,2HAAA,CAAA,yBAAsB,AAAD,EAAE;gFAClC,OACE,mBAAmB,2HAAA,CAAA,cAAW,CAAC,IAAI,GAC/B,+CACA,mBAAmB,2HAAA,CAAA,cAAW,CAAC,SAAS,GACxC,oDACA;gFAEN,WACE,CAAC,CAAA,GAAA,2HAAA,CAAA,yBAAsB,AAAD,EAAE,kBACpB,kCACA;0FAGN,cAAA,8OAAC,oMAAA,CAAA,QAAK;oFAAC,WAAU;;;;;;;;;;;0FAEnB,8OAAC,kIAAA,CAAA,SAAM;gFACL,SAAQ;gFACR,MAAK;gFACL,SAAS,IAAM,iBAAiB;gFAChC,OACE,mBAAmB,2HAAA,CAAA,cAAW,CAAC,IAAI,GAC/B,+CACA;0FAGN,cAAA,8OAAC,2MAAA,CAAA,OAAI;oFAAC,WAAU;;;;;;;;;;;0FAElB,8OAAC,kIAAA,CAAA,SAAM;gFACL,SAAQ;gFACR,MAAK;gFACL,SAAS,IAAM,mBAAmB;gFAClC,UAAU,mBAAmB,2HAAA,CAAA,cAAW,CAAC,IAAI,IAAI,mBAAmB,2HAAA,CAAA,cAAW,CAAC,WAAW;gFAC3F,OACE,mBAAmB,2HAAA,CAAA,cAAW,CAAC,IAAI,GAC/B,2CACA,mBAAmB,2HAAA,CAAA,cAAW,CAAC,WAAW,GAC1C,qDACA;gFAEN,WACE,AAAC,mBAAmB,2HAAA,CAAA,cAAW,CAAC,IAAI,IAAI,mBAAmB,2HAAA,CAAA,cAAW,CAAC,WAAW,GAC9E,kCACA;0FAGN,cAAA,8OAAC,0MAAA,CAAA,SAAM;oFAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;2DA9EnB,OAAO,SAAS;;;;;oDAoF3B;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;oBAUX,gCACC,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC,iIAAA,CAAA,QAAK;4BAAC,WAAU;;8CACf,8OAAC,2NAAA,CAAA,cAAW;oCAAC,WAAU;;;;;;8CACvB,8OAAC,iIAAA,CAAA,mBAAgB;oCAAC,WAAU;8CACzB;;;;;;;;;;;;;;;;;oBAMR,8BACC,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC,iIAAA,CAAA,QAAK;4BAAC,SAAQ;;8CACb,8OAAC,oNAAA,CAAA,cAAW;oCAAC,WAAU;;;;;;8CACvB,8OAAC,iIAAA,CAAA,mBAAgB;8CACd;;;;;;;;;;;;;;;;;;;;;;;0BASX,8OAAC,gJAAA,CAAA,UAAgB;gBACf,MAAM;gBACN,SAAS,IAAM,oBAAoB;gBACnC,YAAY;gBACZ,WAAW;gBACX,SAAS;;;;;;0BAGX,8OAAC,oJAAA,CAAA,UAAoB;gBACnB,MAAM;gBACN,SAAS,IAAM,kBAAkB;gBACjC,QAAQ;gBACR,YAAY;gBACZ,WAAW;gBACX,SAAS;;;;;;0BAGX,8OAAC,mJAAA,CAAA,UAAmB;gBAClB,MAAM;gBACN,SAAS,IAAM,oBAAoB;gBACnC,QAAQ;gBACR,YAAY;gBACZ,WAAW;gBACX,SAAS;;;;;;0BAGX,8OAAC,wJAAA,CAAA,UAAwB;gBACvB,MAAM;gBACN,SAAS,IAAM,qBAAqB;gBACpC,QAAQ;gBACR,YAAY;gBACZ,WAAW;gBACX,SAAS;;;;;;;;;;;;AAIjB", "debugId": null}}]}
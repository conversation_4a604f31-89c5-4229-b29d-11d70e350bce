{"version": 3, "sources": [], "sections": [{"offset": {"line": 6, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 60, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/CMS/webapp-nextjs/src/app/api/password/request-password-reset/route.ts"], "sourcesContent": ["import { NextRequest, NextResponse } from 'next/server'\n\nexport async function POST(request: NextRequest) {\n  try {\n    const body = await request.json()\n    \n    // Proxy la richiesta al backend FastAPI\n    const backendUrl = process.env.NEXT_PUBLIC_API_URL || 'http://localhost:8001'\n    \n    const response = await fetch(`${backendUrl}/api/password/request-password-reset`, {\n      method: 'POST',\n      headers: {\n        'Content-Type': 'application/json',\n        // Passa gli headers necessari per il rate limiting\n        'X-Forwarded-For': request.headers.get('x-forwarded-for') || \n                          request.headers.get('x-real-ip') || \n                          request.ip || \n                          'unknown',\n        'User-Agent': request.headers.get('user-agent') || 'unknown'\n      },\n      body: JSON.stringify(body)\n    })\n\n    const data = await response.json()\n\n    return NextResponse.json(data, { \n      status: response.status,\n      headers: {\n        'Content-Type': 'application/json'\n      }\n    })\n\n  } catch (error) {\n    return NextResponse.json(\n      { \n        success: false, \n        detail: 'Errore interno del server' \n      }, \n      { status: 500 }\n    )\n  }\n}\n"], "names": [], "mappings": ";;;AAAA;;AAEO,eAAe,KAAK,OAAoB;IAC7C,IAAI;QACF,MAAM,OAAO,MAAM,QAAQ,IAAI;QAE/B,wCAAwC;QACxC,MAAM,aAAa,6DAAmC;QAEtD,MAAM,WAAW,MAAM,MAAM,GAAG,WAAW,oCAAoC,CAAC,EAAE;YAChF,QAAQ;YACR,SAAS;gBACP,gBAAgB;gBAChB,mDAAmD;gBACnD,mBAAmB,QAAQ,OAAO,CAAC,GAAG,CAAC,sBACrB,QAAQ,OAAO,CAAC,GAAG,CAAC,gBACpB,QAAQ,EAAE,IACV;gBAClB,cAAc,QAAQ,OAAO,CAAC,GAAG,CAAC,iBAAiB;YACrD;YACA,MAAM,KAAK,SAAS,CAAC;QACvB;QAEA,MAAM,OAAO,MAAM,SAAS,IAAI;QAEhC,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC,MAAM;YAC7B,QAAQ,SAAS,MAAM;YACvB,SAAS;gBACP,gBAAgB;YAClB;QACF;IAEF,EAAE,OAAO,OAAO;QACd,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;YACE,SAAS;YACT,QAAQ;QACV,GACA;YAAE,QAAQ;QAAI;IAElB;AACF", "debugId": null}}]}
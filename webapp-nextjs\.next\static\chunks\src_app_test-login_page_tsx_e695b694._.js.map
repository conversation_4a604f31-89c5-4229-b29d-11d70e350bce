{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/CMS/webapp-nextjs/src/app/test-login/page.tsx"], "sourcesContent": ["'use client'\n\nimport { useState } from 'react'\n\nexport default function TestLoginPage() {\n  const [result, setResult] = useState('')\n  const [loading, setLoading] = useState(false)\n\n  const testAdminLogin = async () => {\n    setLoading(true)\n    setResult('Testing...')\n    \n    try {\n      console.log('Starting admin login test...')\n      \n      const formData = new FormData()\n      formData.append('username', 'admin')\n      formData.append('password', 'admin')\n\n      console.log('Sending request to:', 'http://localhost:8001/api/auth/login')\n      \n      const response = await fetch('http://localhost:8001/api/auth/login', {\n        method: 'POST',\n        body: formData\n      })\n\n      console.log('Response status:', response.status)\n      console.log('Response headers:', response.headers)\n\n      const data = await response.json()\n      console.log('Response data:', data)\n\n      if (response.ok) {\n        setResult(`SUCCESS: ${JSON.stringify(data, null, 2)}`)\n      } else {\n        setResult(`ERROR: ${response.status} - ${JSON.stringify(data, null, 2)}`)\n      }\n    } catch (error) {\n      console.error('Login test error:', error)\n      setResult(`EXCEPTION: ${error.message}`)\n    } finally {\n      setLoading(false)\n    }\n  }\n\n  const testCantiereLogin = async () => {\n    setLoading(true)\n    setResult('Testing...')\n    \n    try {\n      console.log('Starting cantiere login test...')\n      \n      const response = await fetch('http://localhost:8001/api/auth/login/cantiere', {\n        method: 'POST',\n        headers: {\n          'Content-Type': 'application/json'\n        },\n        body: JSON.stringify({\n          codice_univoco: 'TEST123',\n          password: 'test123'\n        })\n      })\n\n      console.log('Response status:', response.status)\n      const data = await response.json()\n      console.log('Response data:', data)\n\n      if (response.ok) {\n        setResult(`SUCCESS: ${JSON.stringify(data, null, 2)}`)\n      } else {\n        setResult(`ERROR: ${response.status} - ${JSON.stringify(data, null, 2)}`)\n      }\n    } catch (error) {\n      console.error('Cantiere login test error:', error)\n      setResult(`EXCEPTION: ${error.message}`)\n    } finally {\n      setLoading(false)\n    }\n  }\n\n  return (\n    <div style={{ padding: '20px', fontFamily: 'monospace' }}>\n      <h1>Test Login Page</h1>\n      \n      <div style={{ marginBottom: '20px' }}>\n        <button \n          onClick={testAdminLogin} \n          disabled={loading}\n          style={{ \n            padding: '10px 20px', \n            marginRight: '10px',\n            backgroundColor: '#007bff',\n            color: 'white',\n            border: 'none',\n            borderRadius: '5px',\n            cursor: loading ? 'not-allowed' : 'pointer'\n          }}\n        >\n          {loading ? 'Testing...' : 'Test Admin Login'}\n        </button>\n        \n        <button \n          onClick={testCantiereLogin} \n          disabled={loading}\n          style={{ \n            padding: '10px 20px',\n            backgroundColor: '#28a745',\n            color: 'white',\n            border: 'none',\n            borderRadius: '5px',\n            cursor: loading ? 'not-allowed' : 'pointer'\n          }}\n        >\n          {loading ? 'Testing...' : 'Test Cantiere Login'}\n        </button>\n      </div>\n\n      <div style={{ \n        backgroundColor: '#f8f9fa', \n        padding: '15px', \n        borderRadius: '5px',\n        whiteSpace: 'pre-wrap',\n        minHeight: '200px'\n      }}>\n        {result || 'Click a button to test login...'}\n      </div>\n    </div>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;;;AAFA;;AAIe,SAAS;;IACtB,MAAM,CAAC,QAAQ,UAAU,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACrC,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAEvC,MAAM,iBAAiB;QACrB,WAAW;QACX,UAAU;QAEV,IAAI;YACF,QAAQ,GAAG,CAAC;YAEZ,MAAM,WAAW,IAAI;YACrB,SAAS,MAAM,CAAC,YAAY;YAC5B,SAAS,MAAM,CAAC,YAAY;YAE5B,QAAQ,GAAG,CAAC,uBAAuB;YAEnC,MAAM,WAAW,MAAM,MAAM,wCAAwC;gBACnE,QAAQ;gBACR,MAAM;YACR;YAEA,QAAQ,GAAG,CAAC,oBAAoB,SAAS,MAAM;YAC/C,QAAQ,GAAG,CAAC,qBAAqB,SAAS,OAAO;YAEjD,MAAM,OAAO,MAAM,SAAS,IAAI;YAChC,QAAQ,GAAG,CAAC,kBAAkB;YAE9B,IAAI,SAAS,EAAE,EAAE;gBACf,UAAU,CAAC,SAAS,EAAE,KAAK,SAAS,CAAC,MAAM,MAAM,IAAI;YACvD,OAAO;gBACL,UAAU,CAAC,OAAO,EAAE,SAAS,MAAM,CAAC,GAAG,EAAE,KAAK,SAAS,CAAC,MAAM,MAAM,IAAI;YAC1E;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,qBAAqB;YACnC,UAAU,CAAC,WAAW,EAAE,MAAM,OAAO,EAAE;QACzC,SAAU;YACR,WAAW;QACb;IACF;IAEA,MAAM,oBAAoB;QACxB,WAAW;QACX,UAAU;QAEV,IAAI;YACF,QAAQ,GAAG,CAAC;YAEZ,MAAM,WAAW,MAAM,MAAM,iDAAiD;gBAC5E,QAAQ;gBACR,SAAS;oBACP,gBAAgB;gBAClB;gBACA,MAAM,KAAK,SAAS,CAAC;oBACnB,gBAAgB;oBAChB,UAAU;gBACZ;YACF;YAEA,QAAQ,GAAG,CAAC,oBAAoB,SAAS,MAAM;YAC/C,MAAM,OAAO,MAAM,SAAS,IAAI;YAChC,QAAQ,GAAG,CAAC,kBAAkB;YAE9B,IAAI,SAAS,EAAE,EAAE;gBACf,UAAU,CAAC,SAAS,EAAE,KAAK,SAAS,CAAC,MAAM,MAAM,IAAI;YACvD,OAAO;gBACL,UAAU,CAAC,OAAO,EAAE,SAAS,MAAM,CAAC,GAAG,EAAE,KAAK,SAAS,CAAC,MAAM,MAAM,IAAI;YAC1E;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,8BAA8B;YAC5C,UAAU,CAAC,WAAW,EAAE,MAAM,OAAO,EAAE;QACzC,SAAU;YACR,WAAW;QACb;IACF;IAEA,qBACE,6LAAC;QAAI,OAAO;YAAE,SAAS;YAAQ,YAAY;QAAY;;0BACrD,6LAAC;0BAAG;;;;;;0BAEJ,6LAAC;gBAAI,OAAO;oBAAE,cAAc;gBAAO;;kCACjC,6LAAC;wBACC,SAAS;wBACT,UAAU;wBACV,OAAO;4BACL,SAAS;4BACT,aAAa;4BACb,iBAAiB;4BACjB,OAAO;4BACP,QAAQ;4BACR,cAAc;4BACd,QAAQ,UAAU,gBAAgB;wBACpC;kCAEC,UAAU,eAAe;;;;;;kCAG5B,6LAAC;wBACC,SAAS;wBACT,UAAU;wBACV,OAAO;4BACL,SAAS;4BACT,iBAAiB;4BACjB,OAAO;4BACP,QAAQ;4BACR,cAAc;4BACd,QAAQ,UAAU,gBAAgB;wBACpC;kCAEC,UAAU,eAAe;;;;;;;;;;;;0BAI9B,6LAAC;gBAAI,OAAO;oBACV,iBAAiB;oBACjB,SAAS;oBACT,cAAc;oBACd,YAAY;oBACZ,WAAW;gBACb;0BACG,UAAU;;;;;;;;;;;;AAInB;GA5HwB;KAAA", "debugId": null}}]}
{"version": 3, "middleware": {"/": {"files": ["server/edge/chunks/_0d3699dc._.js", "server/edge/chunks/[root-of-the-server]__dc15d093._.js", "server/edge/chunks/edge-wrapper_594fdf6a.js"], "name": "middleware", "page": "/", "matchers": [{"regexp": "^(?:\\/(_next\\/data\\/[^/]{1,}))?(?:\\/((?!api|_next\\/static|_next\\/image|favicon.ico|public).*))(\\\\.json)?[\\/#\\?]?$", "originalSource": "/((?!api|_next/static|_next/image|favicon.ico|public).*)"}], "wasm": [], "assets": [], "env": {"__NEXT_BUILD_ID": "development", "NEXT_SERVER_ACTIONS_ENCRYPTION_KEY": "bF7oDIAQsiDlYBqvLR/WiZdX1cn2FuoutsR3XKrSPcA=", "__NEXT_PREVIEW_MODE_ID": "e6b6ee109b8b4f3e4e4668b4c34a2c58", "__NEXT_PREVIEW_MODE_ENCRYPTION_KEY": "faee0c4fa55756ce64685287bf28e00bb489ae776156fdb4f858d4367e7147da", "__NEXT_PREVIEW_MODE_SIGNING_KEY": "4f151074e050fb021162729fbe8b1ae59e11520c04c262f2d8e5d8e6f0fe546b"}}}, "sortedMiddleware": ["/"], "functions": {}}
{"version": 3, "middleware": {"/": {"files": ["server/edge/chunks/_0d3699dc._.js", "server/edge/chunks/[root-of-the-server]__dc15d093._.js", "server/edge/chunks/edge-wrapper_594fdf6a.js"], "name": "middleware", "page": "/", "matchers": [{"regexp": "^(?:\\/(_next\\/data\\/[^/]{1,}))?(?:\\/((?!api|_next\\/static|_next\\/image|favicon.ico|public).*))(\\\\.json)?[\\/#\\?]?$", "originalSource": "/((?!api|_next/static|_next/image|favicon.ico|public).*)"}], "wasm": [], "assets": [], "env": {"__NEXT_BUILD_ID": "development", "NEXT_SERVER_ACTIONS_ENCRYPTION_KEY": "bF7oDIAQsiDlYBqvLR/WiZdX1cn2FuoutsR3XKrSPcA=", "__NEXT_PREVIEW_MODE_ID": "58e43ba0dc2093330da3e8db346753d9", "__NEXT_PREVIEW_MODE_ENCRYPTION_KEY": "ba6e94cc5bfb530107fe3a7e027e53b03ac8c765534469f852c0df8c7562ab3f", "__NEXT_PREVIEW_MODE_SIGNING_KEY": "87f5d9aa5e5a23c7b652da7b4709de82a382644c27059a94d4b760ce18659cad"}}}, "sortedMiddleware": ["/"], "functions": {}}
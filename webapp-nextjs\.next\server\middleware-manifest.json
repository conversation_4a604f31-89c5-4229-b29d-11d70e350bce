{"version": 3, "middleware": {"/": {"files": ["server/edge/chunks/_0d3699dc._.js", "server/edge/chunks/[root-of-the-server]__dc15d093._.js", "server/edge/chunks/edge-wrapper_594fdf6a.js"], "name": "middleware", "page": "/", "matchers": [{"regexp": "^(?:\\/(_next\\/data\\/[^/]{1,}))?(?:\\/((?!api|_next\\/static|_next\\/image|favicon.ico|public).*))(\\\\.json)?[\\/#\\?]?$", "originalSource": "/((?!api|_next/static|_next/image|favicon.ico|public).*)"}], "wasm": [], "assets": [], "env": {"__NEXT_BUILD_ID": "development", "NEXT_SERVER_ACTIONS_ENCRYPTION_KEY": "bF7oDIAQsiDlYBqvLR/WiZdX1cn2FuoutsR3XKrSPcA=", "__NEXT_PREVIEW_MODE_ID": "ca447c6e25002cf3a7f3a0a86975b131", "__NEXT_PREVIEW_MODE_ENCRYPTION_KEY": "d6fe5d673569f7490b474d1d5f2d489aca71b4dffa54c69236d2abaa890e21ca", "__NEXT_PREVIEW_MODE_SIGNING_KEY": "27f703368294ae567c42902252309fd008584ccd80294d103d55911c2bf7d252"}}}, "sortedMiddleware": ["/"], "functions": {}}
{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/CMS/webapp-nextjs/src/components/ui/card.tsx"], "sourcesContent": ["import * as React from \"react\"\n\nimport { cn } from \"@/lib/utils\"\n\nfunction Card({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card\"\n      className={cn(\n        \"bg-card text-card-foreground flex flex-col gap-6 rounded-xl border py-6 shadow-sm\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction CardHeader({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-header\"\n      className={cn(\n        \"@container/card-header grid auto-rows-min grid-rows-[auto_auto] items-start gap-1.5 px-6 has-data-[slot=card-action]:grid-cols-[1fr_auto] [.border-b]:pb-6\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction CardTitle({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-title\"\n      className={cn(\"leading-none font-semibold\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction CardDescription({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-description\"\n      className={cn(\"text-muted-foreground text-sm\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction CardAction({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-action\"\n      className={cn(\n        \"col-start-2 row-span-2 row-start-1 self-start justify-self-end\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction CardContent({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-content\"\n      className={cn(\"px-6\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction CardFooter({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-footer\"\n      className={cn(\"flex items-center px-6 [.border-t]:pt-6\", className)}\n      {...props}\n    />\n  )\n}\n\nexport {\n  Card,\n  CardHeader,\n  CardFooter,\n  CardTitle,\n  CardAction,\n  CardDescription,\n  CardContent,\n}\n"], "names": [], "mappings": ";;;;;;;;;;AAEA;;;AAEA,SAAS,KAAK,EAAE,SAAS,EAAE,GAAG,OAAoC;IAChE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,qFACA;QAED,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,WAAW,EAAE,SAAS,EAAE,GAAG,OAAoC;IACtE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,8JACA;QAED,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,UAAU,EAAE,SAAS,EAAE,GAAG,OAAoC;IACrE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,8BAA8B;QAC3C,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,gBAAgB,EAAE,SAAS,EAAE,GAAG,OAAoC;IAC3E,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,iCAAiC;QAC9C,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,WAAW,EAAE,SAAS,EAAE,GAAG,OAAoC;IACtE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,kEACA;QAED,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,YAAY,EAAE,SAAS,EAAE,GAAG,OAAoC;IACvE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,QAAQ;QACrB,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,WAAW,EAAE,SAAS,EAAE,GAAG,OAAoC;IACtE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,2CAA2C;QACxD,GAAG,KAAK;;;;;;AAGf", "debugId": null}}, {"offset": {"line": 104, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/CMS/webapp-nextjs/src/components/ui/badge.tsx"], "sourcesContent": ["import * as React from \"react\"\nimport { Slot } from \"@radix-ui/react-slot\"\nimport { cva, type VariantProps } from \"class-variance-authority\"\n\nimport { cn } from \"@/lib/utils\"\n\nconst badgeVariants = cva(\n  \"inline-flex items-center justify-center rounded-md border px-2 py-0.5 text-xs font-medium w-fit whitespace-nowrap shrink-0 [&>svg]:size-3 gap-1 [&>svg]:pointer-events-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive transition-[color,box-shadow] overflow-hidden\",\n  {\n    variants: {\n      variant: {\n        default:\n          \"border-transparent bg-primary text-primary-foreground [a&]:hover:bg-primary/90\",\n        secondary:\n          \"border-transparent bg-secondary text-secondary-foreground [a&]:hover:bg-secondary/90\",\n        destructive:\n          \"border-transparent bg-destructive text-white [a&]:hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60\",\n        outline:\n          \"text-foreground [a&]:hover:bg-accent [a&]:hover:text-accent-foreground\",\n      },\n    },\n    defaultVariants: {\n      variant: \"default\",\n    },\n  }\n)\n\nfunction Badge({\n  className,\n  variant,\n  asChild = false,\n  ...props\n}: React.ComponentProps<\"span\"> &\n  VariantProps<typeof badgeVariants> & { asChild?: boolean }) {\n  const Comp = asChild ? Slot : \"span\"\n\n  return (\n    <Comp\n      data-slot=\"badge\"\n      className={cn(badgeVariants({ variant }), className)}\n      {...props}\n    />\n  )\n}\n\nexport { Badge, badgeVariants }\n"], "names": [], "mappings": ";;;;;AACA;AACA;AAEA;;;;;AAEA,MAAM,gBAAgB,CAAA,GAAA,gKAAA,CAAA,MAAG,AAAD,EACtB,kZACA;IACE,UAAU;QACR,SAAS;YACP,SACE;YACF,WACE;YACF,aACE;YACF,SACE;QACJ;IACF;IACA,iBAAiB;QACf,SAAS;IACX;AACF;AAGF,SAAS,MAAM,EACb,SAAS,EACT,OAAO,EACP,UAAU,KAAK,EACf,GAAG,OAEuD;IAC1D,MAAM,OAAO,UAAU,gKAAA,CAAA,OAAI,GAAG;IAE9B,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,cAAc;YAAE;QAAQ,IAAI;QACzC,GAAG,KAAK;;;;;;AAGf", "debugId": null}}, {"offset": {"line": 150, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/CMS/webapp-nextjs/src/components/ui/progress.tsx"], "sourcesContent": ["\"use client\"\n\nimport * as React from \"react\"\nimport * as ProgressPrimitive from \"@radix-ui/react-progress\"\n\nimport { cn } from \"@/lib/utils\"\n\nfunction Progress({\n  className,\n  value,\n  ...props\n}: React.ComponentProps<typeof ProgressPrimitive.Root>) {\n  return (\n    <ProgressPrimitive.Root\n      data-slot=\"progress\"\n      className={cn(\n        \"bg-primary/20 relative h-2 w-full overflow-hidden rounded-full\",\n        className\n      )}\n      {...props}\n    >\n      <ProgressPrimitive.Indicator\n        data-slot=\"progress-indicator\"\n        className=\"bg-primary h-full w-full flex-1 transition-all\"\n        style={{ transform: `translateX(-${100 - (value || 0)}%)` }}\n      />\n    </ProgressPrimitive.Root>\n  )\n}\n\nexport { Progress }\n"], "names": [], "mappings": ";;;;AAGA;AAEA;AALA;;;;AAOA,SAAS,SAAS,EAChB,SAAS,EACT,KAAK,EACL,GAAG,OACiD;IACpD,qBACE,8OAAC,oKAAA,CAAA,OAAsB;QACrB,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,kEACA;QAED,GAAG,KAAK;kBAET,cAAA,8OAAC,oKAAA,CAAA,YAA2B;YAC1B,aAAU;YACV,WAAU;YACV,OAAO;gBAAE,WAAW,CAAC,YAAY,EAAE,MAAM,CAAC,SAAS,CAAC,EAAE,EAAE,CAAC;YAAC;;;;;;;;;;;AAIlE", "debugId": null}}, {"offset": {"line": 189, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/CMS/webapp-nextjs/src/components/ui/tabs.tsx"], "sourcesContent": ["\"use client\"\n\nimport * as React from \"react\"\nimport * as TabsPrimitive from \"@radix-ui/react-tabs\"\n\nimport { cn } from \"@/lib/utils\"\n\nconst Tabs = TabsPrimitive.Root\n\nconst TabsList = React.forwardRef<\n  React.ElementRef<typeof TabsPrimitive.List>,\n  React.ComponentPropsWithoutRef<typeof TabsPrimitive.List>\n>(({ className, ...props }, ref) => (\n  <TabsPrimitive.List\n    ref={ref}\n    className={cn(\n      \"inline-flex h-10 items-center justify-center rounded-md bg-muted p-1 text-muted-foreground\",\n      className\n    )}\n    {...props}\n  />\n))\nTabsList.displayName = TabsPrimitive.List.displayName\n\nconst TabsTrigger = React.forwardRef<\n  React.ElementRef<typeof TabsPrimitive.Trigger>,\n  React.ComponentPropsWithoutRef<typeof TabsPrimitive.Trigger>\n>(({ className, ...props }, ref) => (\n  <TabsPrimitive.Trigger\n    ref={ref}\n    className={cn(\n      \"inline-flex items-center justify-center whitespace-nowrap rounded-sm px-3 py-1.5 text-sm font-medium ring-offset-background transition-all focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 data-[state=active]:bg-background data-[state=active]:text-foreground data-[state=active]:shadow-sm\",\n      className\n    )}\n    {...props}\n  />\n))\nTabsTrigger.displayName = TabsPrimitive.Trigger.displayName\n\nconst TabsContent = React.forwardRef<\n  React.ElementRef<typeof TabsPrimitive.Content>,\n  React.ComponentPropsWithoutRef<typeof TabsPrimitive.Content>\n>(({ className, ...props }, ref) => (\n  <TabsPrimitive.Content\n    ref={ref}\n    className={cn(\n      \"mt-2 ring-offset-background focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2\",\n      className\n    )}\n    {...props}\n  />\n))\nTabsContent.displayName = TabsPrimitive.Content.displayName\n\nexport { Tabs, TabsList, TabsTrigger, TabsContent }\n"], "names": [], "mappings": ";;;;;;;AAEA;AACA;AAEA;AALA;;;;;AAOA,MAAM,OAAO,gKAAA,CAAA,OAAkB;AAE/B,MAAM,yBAAW,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAG9B,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC,gKAAA,CAAA,OAAkB;QACjB,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,8FACA;QAED,GAAG,KAAK;;;;;;AAGb,SAAS,WAAW,GAAG,gKAAA,CAAA,OAAkB,CAAC,WAAW;AAErD,MAAM,4BAAc,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAGjC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC,gKAAA,CAAA,UAAqB;QACpB,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,uYACA;QAED,GAAG,KAAK;;;;;;AAGb,YAAY,WAAW,GAAG,gKAAA,CAAA,UAAqB,CAAC,WAAW;AAE3D,MAAM,4BAAc,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAGjC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC,gKAAA,CAAA,UAAqB;QACpB,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,mIACA;QAED,GAAG,KAAK;;;;;;AAGb,YAAY,WAAW,GAAG,gKAAA,CAAA,UAAqB,CAAC,WAAW", "debugId": null}}, {"offset": {"line": 242, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/CMS/webapp-nextjs/src/app/reports/page.tsx"], "sourcesContent": ["'use client'\n\nimport { useState, useEffect } from 'react'\n\nimport { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'\nimport { Button } from '@/components/ui/button'\nimport { Badge } from '@/components/ui/badge'\nimport { Progress } from '@/components/ui/progress'\nimport { Ta<PERSON>, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs'\nimport { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'\nimport { useAuth } from '@/contexts/AuthContext'\nimport { reportsApi, cantieriApi } from '@/lib/api'\nimport { ReportAvanzamento, ReportBOQ, Cantiere } from '@/types'\nimport {\n  BarChart3,\n  Download,\n  Calendar,\n  TrendingUp,\n  Target,\n  Activity,\n  Clock,\n  CheckCircle,\n  Loader2,\n  AlertCircle,\n  AlertTriangle,\n  FileText,\n  Package,\n  Users,\n  Zap,\n  RefreshCw\n} from 'lucide-react'\nimport {\n  BarChart,\n  Bar,\n  XAxis,\n  <PERSON>A<PERSON>s,\n  CartesianGrid,\n  Tooltip,\n  ResponsiveContainer,\n  PieChart,\n  Pie,\n  Cell,\n  LineChart,\n  Line,\n  Area,\n  AreaChart\n} from 'recharts'\n\nexport default function ReportsPage() {\n  const [activeTab, setActiveTab] = useState('avanzamento')\n  const [selectedPeriod, setSelectedPeriod] = useState('month')\n  const [reportAvanzamento, setReportAvanzamento] = useState<any>(null)\n  const [reportBOQ, setReportBOQ] = useState<any>(null)\n  const [reportUtilizzoBobine, setReportUtilizzoBobine] = useState<any>(null)\n  const [reportProgress, setReportProgress] = useState<any>(null)\n  const [isLoading, setIsLoading] = useState(true)\n  const [error, setError] = useState('')\n\n  const { user, cantiere, isLoading: authLoading } = useAuth()\n\n  // Load all basic reports on component mount - MIGLIORATO per evitare race conditions\n  useEffect(() => {\n\n    const loadAllReports = async () => {\n      setIsLoading(true)\n      try {\n        // Usa il cantiere dal contesto di autenticazione come tutte le altre pagine\n        const currentCantiereId = cantiere?.id_cantiere\n\n        // Test del token\n        const token = localStorage.getItem('token')\n        console.log('🏗️ ReportsPage: Caricamento report per cantiere:', currentCantiereId)\n\n        if (!currentCantiereId || currentCantiereId <= 0) {\n          console.warn('🏗️ ReportsPage: Nessun cantiere valido selezionato')\n          setError('Nessun cantiere selezionato. Seleziona un cantiere da \"Gestisci Cantieri\".')\n          setIsLoading(false)\n          return\n        }\n\n        // Create individual promises that handle their own errors - come nella webapp originale\n        // Aggiungiamo il parametro formato=video per ottenere i dati invece dei file\n\n        // Helper function per timeout - aumentato timeout per API lente\n        const fetchWithTimeout = (url: string, options: any, timeout = 30000) => {\n          return Promise.race([\n            fetch(url, options),\n            new Promise((_, reject) =>\n              setTimeout(() => reject(new Error(`Timeout dopo ${timeout/1000}s per ${url}`)), timeout)\n            )\n          ]) as Promise<Response>\n        }\n\n        const progressPromise = fetchWithTimeout(`http://localhost:8001/api/reports/${currentCantiereId}/progress?formato=video`, {\n          headers: {\n            'Authorization': `Bearer ${localStorage.getItem('token')}`,\n            'Content-Type': 'application/json'\n          }\n        })\n          .then(res => {\n            return res.json()\n          })\n          .then(data => {\n            return data\n          })\n          .catch(err => {\n            return { content: null, error: err.message.includes('Timeout') ? 'Timeout API Progress' : 'Errore API Progress' }\n          })\n\n        const boqPromise = fetchWithTimeout(`http://localhost:8001/api/reports/${currentCantiereId}/boq?formato=video`, {\n          headers: {\n            'Authorization': `Bearer ${localStorage.getItem('token')}`,\n            'Content-Type': 'application/json'\n          }\n        })\n          .then(res => {\n            return res.json()\n          })\n          .then(data => {\n            return data\n          })\n          .catch(err => {\n            return { content: null, error: err.message.includes('Timeout') ? 'Timeout API BOQ' : 'Errore API BOQ' }\n          })\n\n        const utilizzoPromise = fetchWithTimeout(`http://localhost:8001/api/reports/${currentCantiereId}/storico-bobine?formato=video`, {\n          headers: {\n            'Authorization': `Bearer ${localStorage.getItem('token')}`,\n            'Content-Type': 'application/json'\n          }\n        })\n          .then(res => {\n            return res.json()\n          })\n          .then(data => {\n            return data\n          })\n          .catch(err => {\n            return { content: null, error: err.message.includes('Timeout') ? 'Timeout API Utilizzo Bobine' : 'Errore API Utilizzo Bobine' }\n          })\n\n        // Wait for all promises to resolve (they won't reject due to the catch handlers)\n        const [progressData, boqData, utilizzoData] = await Promise.all([\n          progressPromise,\n          boqPromise,\n          utilizzoPromise\n        ])\n\n        // Debug: vediamo la struttura dei dati BOQ\n\n        // Set the data for each report - manteniamo la struttura completa con .content\n        setReportProgress(progressData)\n        setReportBOQ(boqData)\n        setReportUtilizzoBobine(utilizzoData)\n\n        // Check for timeout errors specifically\n        const hasTimeoutErrors = [progressData, boqData, utilizzoData].some(data =>\n          data?.error?.includes('Timeout')\n        )\n\n        // Only set error to null if we successfully loaded at least one report\n        if (progressData?.content || boqData?.content || utilizzoData?.content ||\n            progressData || boqData || utilizzoData) {\n          if (hasTimeoutErrors) {\n            setError('Alcuni report hanno riscontrato timeout. I dati disponibili sono mostrati sotto.')\n          } else {\n            setError('')\n          }\n        } else {\n          setError('Errore nel caricamento dei report. Riprova più tardi.')\n        }\n\n        // Always stop loading after processing the data\n        setIsLoading(false)\n      } catch (err) {\n        // This catch block should rarely be hit due to the individual error handling above\n        setError('Errore nel caricamento dei report. Riprova più tardi.')\n      } finally {\n        setIsLoading(false)\n      }\n    }\n\n    // Aspetta che l'autenticazione sia completata prima di procedere\n    if (authLoading) {\n      console.log('🏗️ ReportsPage: Autenticazione in corso, attendo...')\n      return\n    }\n\n    // Carica i report se c'è un cantiere selezionato nel contesto\n    if (cantiere?.id_cantiere && cantiere.id_cantiere > 0) {\n      console.log('🏗️ ReportsPage: Cantiere valido trovato, carico report')\n      loadAllReports()\n    } else {\n      console.warn('🏗️ ReportsPage: Nessun cantiere valido nel contesto')\n      setIsLoading(false)\n      setError('Nessun cantiere selezionato. Seleziona un cantiere da \"Gestisci Cantieri\".')\n    }\n  }, [cantiere?.id_cantiere, authLoading])\n\n  const handleRefresh = () => {\n    // Ricarica i report per il cantiere corrente\n    if (cantiere?.id_cantiere) {\n      setIsLoading(true)\n      setError('')\n      setReportProgress(null)\n      setReportBOQ(null)\n      setReportUtilizzoBobine(null)\n      // Force re-run of useEffect by clearing and setting cantiere\n      setTimeout(() => {\n        // This will trigger the useEffect again\n        window.location.reload()\n      }, 100)\n    }\n  }\n\n  const handleExportReport = async (reportType: string, format: string = 'pdf') => {\n    try {\n      const currentCantiereId = cantiere?.id_cantiere\n      if (!currentCantiereId) {\n        return\n      }\n\n      let response\n      switch (reportType) {\n        case 'progress':\n          response = await reportsApi.getReportProgress(currentCantiereId)\n          break\n        case 'boq':\n          response = await reportsApi.getReportBOQ(currentCantiereId)\n          break\n        case 'utilizzo-bobine':\n          response = await reportsApi.getReportUtilizzoBobine(currentCantiereId)\n          break\n        default:\n          return\n      }\n\n      if (response.data.file_url) {\n        // Apri il file in una nuova finestra\n        window.open(response.data.file_url, '_blank')\n      }\n    } catch (error) {\n    }\n  }\n\n  // Calcolo IAP (Indice di Avanzamento Ponderato)\n  const calculateIAP = (nTot: number, nInst: number, nColl: number, nCert: number): number => {\n    const Wp = 2.0  // Peso fase Posa\n    const Wc = 1.5  // Peso fase Collegamento\n    const Wz = 0.5  // Peso fase Certificazione\n\n    if (nTot === 0) return 0\n\n    const sforzoSoloInstallati = (nInst - nColl) * Wp\n    const sforzoSoloCollegati = (nColl - nCert) * (Wp + Wc)\n    const sforzoCertificati = nCert * (Wp + Wc + Wz)\n    const numeratore = sforzoSoloInstallati + sforzoSoloCollegati + sforzoCertificati\n\n    const denominatore = nTot * (Wp + Wc + Wz)\n    return Math.round((numeratore / denominatore) * 10000) / 100\n  }\n\n  return (\n    <div className=\"min-h-screen bg-gradient-to-br from-slate-50 to-slate-100 p-6\">\n      <div className=\"max-w-[90%] mx-auto space-y-6\">\n\n        {/* Header */}\n        <div className=\"flex justify-between items-center\">\n          <div>\n            <h1 className=\"text-3xl font-bold text-slate-900\">Reports</h1>\n            <p className=\"text-slate-600 mt-1\">\n              {cantiere?.commessa ? `Cantiere: ${cantiere.commessa}` : 'Seleziona un cantiere per visualizzare i report'}\n            </p>\n          </div>\n          <div className=\"flex gap-2\">\n            <Button variant=\"outline\" size=\"sm\" onClick={handleRefresh}>\n              <RefreshCw className=\"h-4 w-4 mr-2\" />\n              Aggiorna\n            </Button>\n          </div>\n        </div>\n\n        {/* Loading State */}\n        {(isLoading || authLoading) ? (\n          <div className=\"flex items-center justify-center py-12\">\n            <div className=\"flex items-center gap-2\">\n              <Loader2 className=\"h-6 w-6 animate-spin\" />\n              <span>Caricamento report...</span>\n            </div>\n          </div>\n        ) : error ? (\n          <div className=\"p-6 border border-amber-200 rounded-lg bg-amber-50\">\n            <div className=\"flex items-center mb-4\">\n              <AlertCircle className=\"h-5 w-5 text-amber-600 mr-2\" />\n              <span className=\"text-amber-800 font-medium\">\n                {error.includes('Nessun cantiere selezionato') || error.includes('Cantiere non selezionato') ? 'Cantiere non selezionato' :\n                 error.includes('timeout') || error.includes('Timeout') ? 'Timeout API' : 'Errore caricamento report'}\n              </span>\n            </div>\n            <p className=\"text-amber-700 mb-4\">{error}</p>\n            {error.includes('timeout') || error.includes('Timeout') ? (\n              <div className=\"mb-4 p-3 bg-blue-50 border border-blue-200 rounded\">\n                <p className=\"text-blue-800 text-sm\">\n                  💡 <strong>Suggerimento:</strong> Le API stanno impiegando più tempo del previsto.\n                  Prova ad aggiornare la pagina o riprova tra qualche minuto.\n                </p>\n              </div>\n            ) : null}\n            <div className=\"flex gap-2\">\n              <Button\n                onClick={() => window.location.href = '/cantieri'}\n                className=\"bg-amber-600 hover:bg-amber-700 text-white\"\n              >\n                Gestisci Cantieri\n              </Button>\n              <Button\n                variant=\"outline\"\n                onClick={handleRefresh}\n                className=\"border-amber-600 text-amber-700 hover:bg-amber-100\"\n              >\n                <RefreshCw className=\"h-4 w-4 mr-2\" />\n                Riprova\n              </Button>\n            </div>\n          </div>\n        ) : (\n          <Tabs value={activeTab} onValueChange={setActiveTab} className=\"w-full\">\n            <TabsList className=\"grid w-full grid-cols-4\">\n              <TabsTrigger value=\"avanzamento\" className=\"flex items-center gap-2\">\n                <Target className=\"h-4 w-4\" />\n                Avanzamento\n              </TabsTrigger>\n              <TabsTrigger value=\"boq\" className=\"flex items-center gap-2\">\n                <FileText className=\"h-4 w-4\" />\n                BOQ\n              </TabsTrigger>\n              <TabsTrigger value=\"bobine\" className=\"flex items-center gap-2\">\n                <Package className=\"h-4 w-4\" />\n                Bobine\n              </TabsTrigger>\n              <TabsTrigger value=\"produttivita\" className=\"flex items-center gap-2\">\n                <Zap className=\"h-4 w-4\" />\n                Produttività\n              </TabsTrigger>\n            </TabsList>\n\n            {/* Tab Content: Avanzamento */}\n            <TabsContent value=\"avanzamento\" className=\"space-y-6\">\n              {reportProgress?.content ? (\n                <>\n                  {/* KPI Cards */}\n                  <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6\">\n                    <Card className=\"border-l-4 border-l-blue-500\">\n                      <CardHeader className=\"flex flex-row items-center justify-between space-y-0 pb-2\">\n                        <CardTitle className=\"text-sm font-medium text-slate-600\">Metri Totali</CardTitle>\n                        <Target className=\"h-4 w-4 text-blue-500\" />\n                      </CardHeader>\n                      <CardContent>\n                        <div className=\"text-2xl font-bold text-slate-900\">\n                          {reportProgress.content.metri_totali?.toLocaleString() || 0}m\n                        </div>\n                        <p className=\"text-xs text-slate-500 mt-2\">\n                          {reportProgress.content.totale_cavi || 0} cavi totali\n                        </p>\n                      </CardContent>\n                    </Card>\n\n                    <Card className=\"border-l-4 border-l-green-500\">\n                      <CardHeader className=\"flex flex-row items-center justify-between space-y-0 pb-2\">\n                        <CardTitle className=\"text-sm font-medium text-slate-600\">Metri Posati</CardTitle>\n                        <CheckCircle className=\"h-4 w-4 text-green-500\" />\n                      </CardHeader>\n                      <CardContent>\n                        <div className=\"text-2xl font-bold text-slate-900\">\n                          {reportProgress.content.metri_posati?.toLocaleString() || 0}m\n                        </div>\n                        <Progress\n                          value={reportProgress.content.percentuale_avanzamento || 0}\n                          className=\"mt-2\"\n                        />\n                        <p className=\"text-xs text-slate-500 mt-2\">\n                          {reportProgress.content.percentuale_avanzamento?.toFixed(1) || 0}% completato\n                        </p>\n                      </CardContent>\n                    </Card>\n\n                    <Card className=\"border-l-4 border-l-purple-500\">\n                      <CardHeader className=\"flex flex-row items-center justify-between space-y-0 pb-2\">\n                        <CardTitle className=\"text-sm font-medium text-slate-600\">Media Giornaliera</CardTitle>\n                        <TrendingUp className=\"h-4 w-4 text-purple-500\" />\n                      </CardHeader>\n                      <CardContent>\n                        <div className=\"text-2xl font-bold text-slate-900\">\n                          {reportProgress.content.media_giornaliera?.toFixed(1) || 0}m\n                        </div>\n                        <p className=\"text-xs text-slate-500\">metri/giorno</p>\n                        <div className=\"flex items-center mt-2\">\n                          <Activity className=\"h-3 w-3 text-purple-500 mr-1\" />\n                          <span className=\"text-xs text-purple-600\">\n                            {reportProgress.content.giorni_lavorativi_effettivi || 0} giorni attivi\n                          </span>\n                        </div>\n                      </CardContent>\n                    </Card>\n\n                    <Card className=\"border-l-4 border-l-orange-500\">\n                      <CardHeader className=\"flex flex-row items-center justify-between space-y-0 pb-2\">\n                        <CardTitle className=\"text-sm font-medium text-slate-600\">Completamento</CardTitle>\n                        <Clock className=\"h-4 w-4 text-orange-500\" />\n                      </CardHeader>\n                      <CardContent>\n                        <div className=\"text-2xl font-bold text-slate-900\">\n                          {reportProgress.content.data_completamento || 'N/A'}\n                        </div>\n                        <p className=\"text-xs text-slate-500\">stima completamento</p>\n                        <div className=\"flex items-center mt-2\">\n                          <Calendar className=\"h-3 w-3 text-orange-500 mr-1\" />\n                          <span className=\"text-xs text-orange-600\">\n                            {reportProgress.content.giorni_stimati || 0} giorni rimanenti\n                          </span>\n                        </div>\n                      </CardContent>\n                    </Card>\n                  </div>\n\n                  {/* Charts */}\n                  <div className=\"grid grid-cols-1 lg:grid-cols-2 gap-6\">\n                    {/* Posa Recente */}\n                    <Card>\n                      <CardHeader className=\"flex flex-row items-center justify-between\">\n                        <div>\n                          <CardTitle>Posa Recente</CardTitle>\n                          <CardDescription>Ultimi 10 giorni di attività</CardDescription>\n                        </div>\n                        <Button\n                          variant=\"outline\"\n                          size=\"sm\"\n                          onClick={() => handleExportReport('progress', 'pdf')}\n                        >\n                          <Download className=\"h-4 w-4 mr-2\" />\n                          PDF\n                        </Button>\n                      </CardHeader>\n                      <CardContent>\n                        <ResponsiveContainer width=\"100%\" height={300}>\n                          <BarChart data={reportProgress.content.posa_recente || []}>\n                            <CartesianGrid strokeDasharray=\"3 3\" />\n                            <XAxis dataKey=\"data\" />\n                            <YAxis />\n                            <Tooltip />\n                            <Bar dataKey=\"metri\" fill=\"#3b82f6\" name=\"Metri Posati\" />\n                          </BarChart>\n                        </ResponsiveContainer>\n                      </CardContent>\n                    </Card>\n\n                    {/* Statistiche Cavi */}\n                    <Card>\n                      <CardHeader>\n                        <CardTitle>Stato Cavi</CardTitle>\n                        <CardDescription>Distribuzione per stato di avanzamento</CardDescription>\n                      </CardHeader>\n                      <CardContent>\n                        <div className=\"space-y-4\">\n                          <div className=\"flex justify-between items-center\">\n                            <span className=\"text-sm font-medium\">Cavi Posati</span>\n                            <Badge variant=\"secondary\">\n                              {reportProgress.content.cavi_posati || 0}\n                            </Badge>\n                          </div>\n                          <div className=\"flex justify-between items-center\">\n                            <span className=\"text-sm font-medium\">Cavi Rimanenti</span>\n                            <Badge variant=\"outline\">\n                              {reportProgress.content.cavi_rimanenti || 0}\n                            </Badge>\n                          </div>\n                          <div className=\"flex justify-between items-center\">\n                            <span className=\"text-sm font-medium\">Percentuale Cavi</span>\n                            <Badge variant=\"default\">\n                              {reportProgress.content.percentuale_cavi?.toFixed(1) || 0}%\n                            </Badge>\n                          </div>\n                        </div>\n                      </CardContent>\n                    </Card>\n                  </div>\n                </>\n              ) : (\n                <div className=\"text-center py-12 text-slate-500\">\n                  <Target className=\"h-12 w-12 mx-auto mb-4 text-slate-300\" />\n                  <p>Nessun dato di avanzamento disponibile</p>\n                </div>\n              )}\n            </TabsContent>\n\n            {/* Tab Content: BOQ */}\n            <TabsContent value=\"boq\" className=\"space-y-6\">\n              {reportBOQ?.error ? (\n                <div className=\"text-center py-12\">\n                  <AlertCircle className=\"h-12 w-12 mx-auto mb-4 text-amber-500\" />\n                  <h3 className=\"text-lg font-semibold text-slate-700 mb-2\">API BOQ Temporaneamente Non Disponibile</h3>\n                  <p className=\"text-slate-500 mb-4\">Il servizio BOQ sta riscontrando problemi di performance.</p>\n                  <p className=\"text-sm text-slate-400\">Stiamo lavorando per risolvere il problema.</p>\n                </div>\n              ) : reportBOQ?.content ? (\n                <>\n                  <div className=\"flex justify-between items-center\">\n                    <div>\n                      <h3 className=\"text-lg font-semibold\">📋 Bill of Quantities - Distinta Materiali</h3>\n                      <p className=\"text-sm text-slate-600\">Riepilogo completo dei materiali per tipologia di cavo</p>\n                    </div>\n                    <Button\n                      variant=\"outline\"\n                      size=\"sm\"\n                      onClick={() => handleExportReport('boq', 'excel')}\n                    >\n                      <Download className=\"h-4 w-4 mr-2\" />\n                      Excel\n                    </Button>\n                  </div>\n\n                  {/* Alert Metri Orfani */}\n                  {reportBOQ.content.metri_orfani && reportBOQ.content.metri_orfani.metri_orfani_totali > 0 && (\n                    <Card className=\"border-red-200 bg-red-50\">\n                      <CardHeader className=\"pb-3\">\n                        <CardTitle className=\"text-red-700 flex items-center\">\n                          <AlertTriangle className=\"h-5 w-5 mr-2\" />\n                          🚨 METRI POSATI SENZA TRACCIABILITÀ BOBINA\n                        </CardTitle>\n                      </CardHeader>\n                      <CardContent>\n                        <p className=\"text-red-800 font-medium mb-2\">\n                          <strong>{reportBOQ.content.metri_orfani.metri_orfani_totali}m</strong> installati con BOBINA_VUOTA\n                          ({reportBOQ.content.metri_orfani.num_cavi_orfani} cavi)\n                        </p>\n                        <div className=\"text-sm text-red-700 space-y-1\">\n                          {Array.isArray(reportBOQ.content.metri_orfani.dettaglio_per_categoria) ?\n                            reportBOQ.content.metri_orfani.dettaglio_per_categoria.map((categoria: any, index: number) => (\n                              <div key={index}>\n                                • <strong>{categoria.tipologia} {categoria.formazione}</strong>: {categoria.metri_orfani}m ({categoria.num_cavi} cavi)\n                              </div>\n                            )) : (\n                              <div>Dettaglio metri orfani non disponibile</div>\n                            )\n                          }\n                        </div>\n                        <div className=\"mt-3 p-3 bg-amber-50 border border-amber-200 rounded\">\n                          <p className=\"text-amber-800 text-sm\">\n                            ⚠️ <strong>NOTA:</strong> I metri orfani NON sono inclusi nel calcolo acquisti.\n                            Prima di acquistare, verificare se questi metri possono essere associati a bobine esistenti.\n                          </p>\n                        </div>\n                      </CardContent>\n                    </Card>\n                  )}\n\n                  {/* Riepilogo Generale */}\n                  {reportBOQ.content.riepilogo && (\n                    <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4\">\n                      <Card className=\"border-l-4 border-l-blue-500\">\n                        <CardHeader className=\"pb-2\">\n                          <CardTitle className=\"text-sm font-medium text-slate-600\">Metri da Acquistare</CardTitle>\n                        </CardHeader>\n                        <CardContent>\n                          <div className=\"text-2xl font-bold text-blue-600\">\n                            {reportBOQ.content.riepilogo.totale_metri_mancanti?.toLocaleString() || 0}m\n                          </div>\n                          <p className=\"text-xs text-slate-500 mt-1\">per completamento progetto</p>\n                        </CardContent>\n                      </Card>\n\n                      <Card className=\"border-l-4 border-l-green-500\">\n                        <CardHeader className=\"pb-2\">\n                          <CardTitle className=\"text-sm font-medium text-slate-600\">Metri Residui</CardTitle>\n                        </CardHeader>\n                        <CardContent>\n                          <div className=\"text-2xl font-bold text-green-600\">\n                            {reportBOQ.content.riepilogo.totale_metri_residui?.toLocaleString() || 0}m\n                          </div>\n                          <p className=\"text-xs text-slate-500 mt-1\">disponibili in magazzino</p>\n                        </CardContent>\n                      </Card>\n\n                      <Card className=\"border-l-4 border-l-purple-500\">\n                        <CardHeader className=\"pb-2\">\n                          <CardTitle className=\"text-sm font-medium text-slate-600\">Completamento</CardTitle>\n                        </CardHeader>\n                        <CardContent>\n                          <div className=\"text-2xl font-bold text-purple-600\">\n                            {reportBOQ.content.riepilogo.percentuale_completamento?.toFixed(1) || 0}%\n                          </div>\n                          <p className=\"text-xs text-slate-500 mt-1\">progetto completato</p>\n                        </CardContent>\n                      </Card>\n\n                      <Card className=\"border-l-4 border-l-orange-500\">\n                        <CardHeader className=\"pb-2\">\n                          <CardTitle className=\"text-sm font-medium text-slate-600\">Categorie</CardTitle>\n                        </CardHeader>\n                        <CardContent>\n                          <div className=\"text-2xl font-bold text-orange-600\">\n                            {reportBOQ.content.riepilogo.categorie_necessitano_acquisto || 0}\n                          </div>\n                          <p className=\"text-xs text-slate-500 mt-1\">necessitano acquisto</p>\n                        </CardContent>\n                      </Card>\n                    </div>\n                  )}\n\n                  {/* Distinta Materiali */}\n                  <Card>\n                    <CardHeader>\n                      <CardTitle>Distinta Materiali</CardTitle>\n                      <CardDescription>Fabbisogno materiali raggruppati per tipologia e formazione</CardDescription>\n                    </CardHeader>\n                    <CardContent>\n                      <div className=\"overflow-x-auto\">\n                        <table className=\"w-full text-sm\">\n                          <thead>\n                            <tr className=\"border-b\">\n                              <th className=\"text-left p-2\">Tipologia</th>\n                              <th className=\"text-left p-2\">Formazione</th>\n                              <th className=\"text-right p-2\">Cavi</th>\n                              <th className=\"text-right p-2\">Metri Teorici</th>\n                              <th className=\"text-right p-2\">Metri Posati</th>\n                              <th className=\"text-right p-2\">Metri da Posare</th>\n                              <th className=\"text-right p-2\">Bobine</th>\n                              <th className=\"text-right p-2\">Metri Residui</th>\n                              <th className=\"text-right p-2\">Metri Mancanti</th>\n                              <th className=\"text-center p-2\">Acquisto</th>\n                            </tr>\n                          </thead>\n                          <tbody>\n                            {reportBOQ.content.distinta_materiali?.map((item: any, index: number) => (\n                              <tr key={index} className={`border-b hover:bg-slate-50 ${item.ha_bobina_vuota ? 'bg-red-50' : ''}`}>\n                                <td className=\"p-2 font-medium\">{item.tipologia}</td>\n                                <td className=\"p-2\">{item.formazione}</td>\n                                <td className=\"p-2 text-right\">{item.num_cavi}</td>\n                                <td className=\"p-2 text-right\">{item.metri_teorici_totali?.toLocaleString()}</td>\n                                <td className=\"p-2 text-right\">{item.metri_reali_posati?.toLocaleString()}</td>\n                                <td className=\"p-2 text-right\">{item.metri_da_posare?.toLocaleString()}</td>\n                                <td className=\"p-2 text-right\">{item.num_bobine}</td>\n                                <td className=\"p-2 text-right\">{item.metri_residui?.toLocaleString()}</td>\n                                <td className=\"p-2 text-right font-medium\">\n                                  {item.metri_mancanti > 0 ? (\n                                    <span className=\"text-red-600\">{item.metri_mancanti?.toLocaleString()}m</span>\n                                  ) : (\n                                    <span className=\"text-green-600\">0m</span>\n                                  )}\n                                </td>\n                                <td className=\"p-2 text-center\">\n                                  {item.necessita_acquisto ? (\n                                    <Badge variant=\"destructive\">Sì</Badge>\n                                  ) : (\n                                    <Badge variant=\"secondary\">No</Badge>\n                                  )}\n                                </td>\n                              </tr>\n                            ))}\n                          </tbody>\n                        </table>\n                      </div>\n                    </CardContent>\n                  </Card>\n\n                  {/* Bobine per Tipo */}\n                  <Card>\n                    <CardHeader>\n                      <CardTitle>Bobine Disponibili</CardTitle>\n                      <CardDescription>Inventario bobine per tipologia</CardDescription>\n                    </CardHeader>\n                    <CardContent>\n                      <div className=\"overflow-x-auto\">\n                        <table className=\"w-full text-sm\">\n                          <thead>\n                            <tr className=\"border-b\">\n                              <th className=\"text-left p-2\">Tipologia</th>\n                              <th className=\"text-left p-2\">Formazione</th>\n                              <th className=\"text-right p-2\">Numero Bobine</th>\n                              <th className=\"text-right p-2\">Metri Disponibili</th>\n                            </tr>\n                          </thead>\n                          <tbody>\n                            {reportBOQ.content.bobine_per_tipo?.map((item: any, index: number) => (\n                              <tr key={index} className=\"border-b hover:bg-slate-50\">\n                                <td className=\"p-2 font-medium\">{item.tipologia}</td>\n                                <td className=\"p-2\">{item.formazione}</td>\n                                <td className=\"p-2 text-right\">{item.num_bobine}</td>\n                                <td className=\"p-2 text-right\">\n                                  <span className={item.metri_disponibili > 0 ? 'text-green-600' : 'text-red-600'}>\n                                    {item.metri_disponibili?.toLocaleString()}\n                                  </span>\n                                </td>\n                              </tr>\n                            ))}\n                          </tbody>\n                        </table>\n                      </div>\n                    </CardContent>\n                  </Card>\n                </>\n              ) : (\n                <div className=\"text-center py-12 text-slate-500\">\n                  <FileText className=\"h-12 w-12 mx-auto mb-4 text-slate-300\" />\n                  <p>Nessun dato BOQ disponibile</p>\n                </div>\n              )}\n            </TabsContent>\n\n            {/* Tab Content: Bobine */}\n            <TabsContent value=\"bobine\" className=\"space-y-6\">\n              {reportUtilizzoBobine?.content ? (\n                <>\n                  <div className=\"flex justify-between items-center\">\n                    <div>\n                      <h3 className=\"text-lg font-semibold\">Utilizzo Bobine</h3>\n                      <p className=\"text-sm text-slate-600\">Stato e utilizzo delle bobine</p>\n                    </div>\n                    <Button\n                      variant=\"outline\"\n                      size=\"sm\"\n                      onClick={() => handleExportReport('utilizzo-bobine', 'excel')}\n                    >\n                      <Download className=\"h-4 w-4 mr-2\" />\n                      Excel\n                    </Button>\n                  </div>\n\n                  {/* Statistiche Bobine */}\n                  <div className=\"grid grid-cols-1 md:grid-cols-3 gap-6\">\n                    <Card>\n                      <CardHeader className=\"flex flex-row items-center justify-between space-y-0 pb-2\">\n                        <CardTitle className=\"text-sm font-medium text-slate-600\">Totale Bobine</CardTitle>\n                        <Package className=\"h-4 w-4 text-blue-500\" />\n                      </CardHeader>\n                      <CardContent>\n                        <div className=\"text-2xl font-bold text-slate-900\">\n                          {reportUtilizzoBobine.content.totale_bobine || 0}\n                        </div>\n                        <p className=\"text-xs text-slate-500\">bobine nel cantiere</p>\n                      </CardContent>\n                    </Card>\n\n                    <Card>\n                      <CardHeader className=\"flex flex-row items-center justify-between space-y-0 pb-2\">\n                        <CardTitle className=\"text-sm font-medium text-slate-600\">Bobine Attive</CardTitle>\n                        <Activity className=\"h-4 w-4 text-green-500\" />\n                      </CardHeader>\n                      <CardContent>\n                        <div className=\"text-2xl font-bold text-slate-900\">\n                          {reportUtilizzoBobine.content.bobine?.filter((b: any) =>\n                            b.stato === 'In uso' || b.stato === 'Disponibile'\n                          ).length || 0}\n                        </div>\n                        <p className=\"text-xs text-slate-500\">disponibili/in uso</p>\n                      </CardContent>\n                    </Card>\n\n                    <Card>\n                      <CardHeader className=\"flex flex-row items-center justify-between space-y-0 pb-2\">\n                        <CardTitle className=\"text-sm font-medium text-slate-600\">Utilizzo Medio</CardTitle>\n                        <TrendingUp className=\"h-4 w-4 text-purple-500\" />\n                      </CardHeader>\n                      <CardContent>\n                        <div className=\"text-2xl font-bold text-slate-900\">\n                          {reportUtilizzoBobine.content.bobine?.length > 0 ?\n                            (reportUtilizzoBobine.content.bobine.reduce((acc: number, b: any) =>\n                              acc + (b.percentuale_utilizzo || 0), 0) / reportUtilizzoBobine.content.bobine.length\n                            ).toFixed(1) : 0}%\n                        </div>\n                        <p className=\"text-xs text-slate-500\">utilizzo medio</p>\n                      </CardContent>\n                    </Card>\n                  </div>\n\n                  {/* Lista Bobine */}\n                  <Card>\n                    <CardHeader>\n                      <CardTitle>Dettaglio Bobine</CardTitle>\n                      <CardDescription>Stato dettagliato di tutte le bobine</CardDescription>\n                    </CardHeader>\n                    <CardContent>\n                      <div className=\"overflow-x-auto\">\n                        <table className=\"w-full text-sm\">\n                          <thead>\n                            <tr className=\"border-b\">\n                              <th className=\"text-left p-2\">Codice</th>\n                              <th className=\"text-left p-2\">Tipologia</th>\n                              <th className=\"text-right p-2\">Metri Totali</th>\n                              <th className=\"text-right p-2\">Metri Utilizzati</th>\n                              <th className=\"text-right p-2\">Metri Residui</th>\n                              <th className=\"text-right p-2\">Utilizzo %</th>\n                              <th className=\"text-left p-2\">Stato</th>\n                              <th className=\"text-right p-2\">Cavi</th>\n                            </tr>\n                          </thead>\n                          <tbody>\n                            {reportUtilizzoBobine.content.bobine?.map((bobina: any, index: number) => (\n                              <tr key={index} className=\"border-b hover:bg-slate-50\">\n                                <td className=\"p-2 font-medium\">{bobina.codice}</td>\n                                <td className=\"p-2\">{bobina.tipologia}</td>\n                                <td className=\"p-2 text-right\">{bobina.metri_totali?.toLocaleString()}</td>\n                                <td className=\"p-2 text-right\">{bobina.metri_utilizzati?.toLocaleString()}</td>\n                                <td className=\"p-2 text-right\">{bobina.metri_residui?.toLocaleString()}</td>\n                                <td className=\"p-2 text-right\">\n                                  <div className=\"flex items-center gap-2\">\n                                    <span>{bobina.percentuale_utilizzo?.toFixed(1)}%</span>\n                                    <Progress\n                                      value={Math.min(bobina.percentuale_utilizzo || 0, 100)}\n                                      className=\"w-16 h-2\"\n                                    />\n                                  </div>\n                                </td>\n                                <td className=\"p-2\">\n                                  <Badge\n                                    variant={\n                                      bobina.stato === 'Disponibile' ? 'default' :\n                                      bobina.stato === 'In uso' ? 'secondary' :\n                                      bobina.stato === 'Terminata' ? 'outline' :\n                                      bobina.stato === 'Over' ? 'destructive' : 'outline'\n                                    }\n                                  >\n                                    {bobina.stato}\n                                  </Badge>\n                                </td>\n                                <td className=\"p-2 text-right\">{bobina.totale_cavi_associati || 0}</td>\n                              </tr>\n                            ))}\n                          </tbody>\n                        </table>\n                      </div>\n                    </CardContent>\n                  </Card>\n                </>\n              ) : (\n                <div className=\"text-center py-12 text-slate-500\">\n                  <Package className=\"h-12 w-12 mx-auto mb-4 text-slate-300\" />\n                  <p>Nessun dato bobine disponibile</p>\n                </div>\n              )}\n            </TabsContent>\n\n            {/* Tab Content: Produttività */}\n            <TabsContent value=\"produttivita\" className=\"space-y-6\">\n              <div className=\"text-center py-12 text-slate-500\">\n                <Zap className=\"h-12 w-12 mx-auto mb-4 text-slate-300\" />\n                <h3 className=\"text-lg font-semibold mb-2\">Produttività</h3>\n                <p>Funzionalità in fase di sviluppo</p>\n                <p className=\"text-sm mt-2\">\n                  Includerà calcoli IAP, statistiche team e analisi performance\n                </p>\n              </div>\n            </TabsContent>\n\n          </Tabs>\n        )}\n\n      </div>\n    </div>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AAEA;AACA;AACA;AACA;AACA;AAEA;AACA;AAEA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAkBA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AA/BA;;;;;;;;;;;;AAgDe,SAAS;IACtB,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC3C,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACrD,MAAM,CAAC,mBAAmB,qBAAqB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAO;IAChE,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAO;IAChD,MAAM,CAAC,sBAAsB,wBAAwB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAO;IACtE,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAO;IAC1D,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC3C,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAEnC,MAAM,EAAE,IAAI,EAAE,QAAQ,EAAE,WAAW,WAAW,EAAE,GAAG,CAAA,GAAA,+HAAA,CAAA,UAAO,AAAD;IAEzD,qFAAqF;IACrF,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QAER,MAAM,iBAAiB;YACrB,aAAa;YACb,IAAI;gBACF,4EAA4E;gBAC5E,MAAM,oBAAoB,UAAU;gBAEpC,iBAAiB;gBACjB,MAAM,QAAQ,aAAa,OAAO,CAAC;gBACnC,QAAQ,GAAG,CAAC,qDAAqD;gBAEjE,IAAI,CAAC,qBAAqB,qBAAqB,GAAG;oBAChD,QAAQ,IAAI,CAAC;oBACb,SAAS;oBACT,aAAa;oBACb;gBACF;gBAEA,wFAAwF;gBACxF,6EAA6E;gBAE7E,gEAAgE;gBAChE,MAAM,mBAAmB,CAAC,KAAa,SAAc,UAAU,KAAK;oBAClE,OAAO,QAAQ,IAAI,CAAC;wBAClB,MAAM,KAAK;wBACX,IAAI,QAAQ,CAAC,GAAG,SACd,WAAW,IAAM,OAAO,IAAI,MAAM,CAAC,aAAa,EAAE,UAAQ,KAAK,MAAM,EAAE,KAAK,IAAI;qBAEnF;gBACH;gBAEA,MAAM,kBAAkB,iBAAiB,CAAC,kCAAkC,EAAE,kBAAkB,uBAAuB,CAAC,EAAE;oBACxH,SAAS;wBACP,iBAAiB,CAAC,OAAO,EAAE,aAAa,OAAO,CAAC,UAAU;wBAC1D,gBAAgB;oBAClB;gBACF,GACG,IAAI,CAAC,CAAA;oBACJ,OAAO,IAAI,IAAI;gBACjB,GACC,IAAI,CAAC,CAAA;oBACJ,OAAO;gBACT,GACC,KAAK,CAAC,CAAA;oBACL,OAAO;wBAAE,SAAS;wBAAM,OAAO,IAAI,OAAO,CAAC,QAAQ,CAAC,aAAa,yBAAyB;oBAAsB;gBAClH;gBAEF,MAAM,aAAa,iBAAiB,CAAC,kCAAkC,EAAE,kBAAkB,kBAAkB,CAAC,EAAE;oBAC9G,SAAS;wBACP,iBAAiB,CAAC,OAAO,EAAE,aAAa,OAAO,CAAC,UAAU;wBAC1D,gBAAgB;oBAClB;gBACF,GACG,IAAI,CAAC,CAAA;oBACJ,OAAO,IAAI,IAAI;gBACjB,GACC,IAAI,CAAC,CAAA;oBACJ,OAAO;gBACT,GACC,KAAK,CAAC,CAAA;oBACL,OAAO;wBAAE,SAAS;wBAAM,OAAO,IAAI,OAAO,CAAC,QAAQ,CAAC,aAAa,oBAAoB;oBAAiB;gBACxG;gBAEF,MAAM,kBAAkB,iBAAiB,CAAC,kCAAkC,EAAE,kBAAkB,6BAA6B,CAAC,EAAE;oBAC9H,SAAS;wBACP,iBAAiB,CAAC,OAAO,EAAE,aAAa,OAAO,CAAC,UAAU;wBAC1D,gBAAgB;oBAClB;gBACF,GACG,IAAI,CAAC,CAAA;oBACJ,OAAO,IAAI,IAAI;gBACjB,GACC,IAAI,CAAC,CAAA;oBACJ,OAAO;gBACT,GACC,KAAK,CAAC,CAAA;oBACL,OAAO;wBAAE,SAAS;wBAAM,OAAO,IAAI,OAAO,CAAC,QAAQ,CAAC,aAAa,gCAAgC;oBAA6B;gBAChI;gBAEF,iFAAiF;gBACjF,MAAM,CAAC,cAAc,SAAS,aAAa,GAAG,MAAM,QAAQ,GAAG,CAAC;oBAC9D;oBACA;oBACA;iBACD;gBAED,2CAA2C;gBAE3C,+EAA+E;gBAC/E,kBAAkB;gBAClB,aAAa;gBACb,wBAAwB;gBAExB,wCAAwC;gBACxC,MAAM,mBAAmB;oBAAC;oBAAc;oBAAS;iBAAa,CAAC,IAAI,CAAC,CAAA,OAClE,MAAM,OAAO,SAAS;gBAGxB,uEAAuE;gBACvE,IAAI,cAAc,WAAW,SAAS,WAAW,cAAc,WAC3D,gBAAgB,WAAW,cAAc;oBAC3C,IAAI,kBAAkB;wBACpB,SAAS;oBACX,OAAO;wBACL,SAAS;oBACX;gBACF,OAAO;oBACL,SAAS;gBACX;gBAEA,gDAAgD;gBAChD,aAAa;YACf,EAAE,OAAO,KAAK;gBACZ,mFAAmF;gBACnF,SAAS;YACX,SAAU;gBACR,aAAa;YACf;QACF;QAEA,iEAAiE;QACjE,IAAI,aAAa;YACf,QAAQ,GAAG,CAAC;YACZ;QACF;QAEA,8DAA8D;QAC9D,IAAI,UAAU,eAAe,SAAS,WAAW,GAAG,GAAG;YACrD,QAAQ,GAAG,CAAC;YACZ;QACF,OAAO;YACL,QAAQ,IAAI,CAAC;YACb,aAAa;YACb,SAAS;QACX;IACF,GAAG;QAAC,UAAU;QAAa;KAAY;IAEvC,MAAM,gBAAgB;QACpB,6CAA6C;QAC7C,IAAI,UAAU,aAAa;YACzB,aAAa;YACb,SAAS;YACT,kBAAkB;YAClB,aAAa;YACb,wBAAwB;YACxB,6DAA6D;YAC7D,WAAW;gBACT,wCAAwC;gBACxC,OAAO,QAAQ,CAAC,MAAM;YACxB,GAAG;QACL;IACF;IAEA,MAAM,qBAAqB,OAAO,YAAoB,SAAiB,KAAK;QAC1E,IAAI;YACF,MAAM,oBAAoB,UAAU;YACpC,IAAI,CAAC,mBAAmB;gBACtB;YACF;YAEA,IAAI;YACJ,OAAQ;gBACN,KAAK;oBACH,WAAW,MAAM,iHAAA,CAAA,aAAU,CAAC,iBAAiB,CAAC;oBAC9C;gBACF,KAAK;oBACH,WAAW,MAAM,iHAAA,CAAA,aAAU,CAAC,YAAY,CAAC;oBACzC;gBACF,KAAK;oBACH,WAAW,MAAM,iHAAA,CAAA,aAAU,CAAC,uBAAuB,CAAC;oBACpD;gBACF;oBACE;YACJ;YAEA,IAAI,SAAS,IAAI,CAAC,QAAQ,EAAE;gBAC1B,qCAAqC;gBACrC,OAAO,IAAI,CAAC,SAAS,IAAI,CAAC,QAAQ,EAAE;YACtC;QACF,EAAE,OAAO,OAAO,CAChB;IACF;IAEA,gDAAgD;IAChD,MAAM,eAAe,CAAC,MAAc,OAAe,OAAe;QAChE,MAAM,KAAK,IAAK,iBAAiB;;QACjC,MAAM,KAAK,IAAK,yBAAyB;;QACzC,MAAM,KAAK,IAAK,2BAA2B;;QAE3C,IAAI,SAAS,GAAG,OAAO;QAEvB,MAAM,uBAAuB,CAAC,QAAQ,KAAK,IAAI;QAC/C,MAAM,sBAAsB,CAAC,QAAQ,KAAK,IAAI,CAAC,KAAK,EAAE;QACtD,MAAM,oBAAoB,QAAQ,CAAC,KAAK,KAAK,EAAE;QAC/C,MAAM,aAAa,uBAAuB,sBAAsB;QAEhE,MAAM,eAAe,OAAO,CAAC,KAAK,KAAK,EAAE;QACzC,OAAO,KAAK,KAAK,CAAC,AAAC,aAAa,eAAgB,SAAS;IAC3D;IAEA,qBACE,8OAAC;QAAI,WAAU;kBACb,cAAA,8OAAC;YAAI,WAAU;;8BAGb,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;;8CACC,8OAAC;oCAAG,WAAU;8CAAoC;;;;;;8CAClD,8OAAC;oCAAE,WAAU;8CACV,UAAU,WAAW,CAAC,UAAU,EAAE,SAAS,QAAQ,EAAE,GAAG;;;;;;;;;;;;sCAG7D,8OAAC;4BAAI,WAAU;sCACb,cAAA,8OAAC,kIAAA,CAAA,SAAM;gCAAC,SAAQ;gCAAU,MAAK;gCAAK,SAAS;;kDAC3C,8OAAC,gNAAA,CAAA,YAAS;wCAAC,WAAU;;;;;;oCAAiB;;;;;;;;;;;;;;;;;;gBAO1C,aAAa,4BACb,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC;wBAAI,WAAU;;0CACb,8OAAC,iNAAA,CAAA,UAAO;gCAAC,WAAU;;;;;;0CACnB,8OAAC;0CAAK;;;;;;;;;;;;;;;;2BAGR,sBACF,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAI,WAAU;;8CACb,8OAAC,oNAAA,CAAA,cAAW;oCAAC,WAAU;;;;;;8CACvB,8OAAC;oCAAK,WAAU;8CACb,MAAM,QAAQ,CAAC,kCAAkC,MAAM,QAAQ,CAAC,8BAA8B,6BAC9F,MAAM,QAAQ,CAAC,cAAc,MAAM,QAAQ,CAAC,aAAa,gBAAgB;;;;;;;;;;;;sCAG9E,8OAAC;4BAAE,WAAU;sCAAuB;;;;;;wBACnC,MAAM,QAAQ,CAAC,cAAc,MAAM,QAAQ,CAAC,2BAC3C,8OAAC;4BAAI,WAAU;sCACb,cAAA,8OAAC;gCAAE,WAAU;;oCAAwB;kDAChC,8OAAC;kDAAO;;;;;;oCAAsB;;;;;;;;;;;mCAInC;sCACJ,8OAAC;4BAAI,WAAU;;8CACb,8OAAC,kIAAA,CAAA,SAAM;oCACL,SAAS,IAAM,OAAO,QAAQ,CAAC,IAAI,GAAG;oCACtC,WAAU;8CACX;;;;;;8CAGD,8OAAC,kIAAA,CAAA,SAAM;oCACL,SAAQ;oCACR,SAAS;oCACT,WAAU;;sDAEV,8OAAC,gNAAA,CAAA,YAAS;4CAAC,WAAU;;;;;;wCAAiB;;;;;;;;;;;;;;;;;;yCAM5C,8OAAC,gIAAA,CAAA,OAAI;oBAAC,OAAO;oBAAW,eAAe;oBAAc,WAAU;;sCAC7D,8OAAC,gIAAA,CAAA,WAAQ;4BAAC,WAAU;;8CAClB,8OAAC,gIAAA,CAAA,cAAW;oCAAC,OAAM;oCAAc,WAAU;;sDACzC,8OAAC,sMAAA,CAAA,SAAM;4CAAC,WAAU;;;;;;wCAAY;;;;;;;8CAGhC,8OAAC,gIAAA,CAAA,cAAW;oCAAC,OAAM;oCAAM,WAAU;;sDACjC,8OAAC,8MAAA,CAAA,WAAQ;4CAAC,WAAU;;;;;;wCAAY;;;;;;;8CAGlC,8OAAC,gIAAA,CAAA,cAAW;oCAAC,OAAM;oCAAS,WAAU;;sDACpC,8OAAC,wMAAA,CAAA,UAAO;4CAAC,WAAU;;;;;;wCAAY;;;;;;;8CAGjC,8OAAC,gIAAA,CAAA,cAAW;oCAAC,OAAM;oCAAe,WAAU;;sDAC1C,8OAAC,gMAAA,CAAA,MAAG;4CAAC,WAAU;;;;;;wCAAY;;;;;;;;;;;;;sCAM/B,8OAAC,gIAAA,CAAA,cAAW;4BAAC,OAAM;4BAAc,WAAU;sCACxC,gBAAgB,wBACf;;kDAEE,8OAAC;wCAAI,WAAU;;0DACb,8OAAC,gIAAA,CAAA,OAAI;gDAAC,WAAU;;kEACd,8OAAC,gIAAA,CAAA,aAAU;wDAAC,WAAU;;0EACpB,8OAAC,gIAAA,CAAA,YAAS;gEAAC,WAAU;0EAAqC;;;;;;0EAC1D,8OAAC,sMAAA,CAAA,SAAM;gEAAC,WAAU;;;;;;;;;;;;kEAEpB,8OAAC,gIAAA,CAAA,cAAW;;0EACV,8OAAC;gEAAI,WAAU;;oEACZ,eAAe,OAAO,CAAC,YAAY,EAAE,oBAAoB;oEAAE;;;;;;;0EAE9D,8OAAC;gEAAE,WAAU;;oEACV,eAAe,OAAO,CAAC,WAAW,IAAI;oEAAE;;;;;;;;;;;;;;;;;;;0DAK/C,8OAAC,gIAAA,CAAA,OAAI;gDAAC,WAAU;;kEACd,8OAAC,gIAAA,CAAA,aAAU;wDAAC,WAAU;;0EACpB,8OAAC,gIAAA,CAAA,YAAS;gEAAC,WAAU;0EAAqC;;;;;;0EAC1D,8OAAC,2NAAA,CAAA,cAAW;gEAAC,WAAU;;;;;;;;;;;;kEAEzB,8OAAC,gIAAA,CAAA,cAAW;;0EACV,8OAAC;gEAAI,WAAU;;oEACZ,eAAe,OAAO,CAAC,YAAY,EAAE,oBAAoB;oEAAE;;;;;;;0EAE9D,8OAAC,oIAAA,CAAA,WAAQ;gEACP,OAAO,eAAe,OAAO,CAAC,uBAAuB,IAAI;gEACzD,WAAU;;;;;;0EAEZ,8OAAC;gEAAE,WAAU;;oEACV,eAAe,OAAO,CAAC,uBAAuB,EAAE,QAAQ,MAAM;oEAAE;;;;;;;;;;;;;;;;;;;0DAKvE,8OAAC,gIAAA,CAAA,OAAI;gDAAC,WAAU;;kEACd,8OAAC,gIAAA,CAAA,aAAU;wDAAC,WAAU;;0EACpB,8OAAC,gIAAA,CAAA,YAAS;gEAAC,WAAU;0EAAqC;;;;;;0EAC1D,8OAAC,kNAAA,CAAA,aAAU;gEAAC,WAAU;;;;;;;;;;;;kEAExB,8OAAC,gIAAA,CAAA,cAAW;;0EACV,8OAAC;gEAAI,WAAU;;oEACZ,eAAe,OAAO,CAAC,iBAAiB,EAAE,QAAQ,MAAM;oEAAE;;;;;;;0EAE7D,8OAAC;gEAAE,WAAU;0EAAyB;;;;;;0EACtC,8OAAC;gEAAI,WAAU;;kFACb,8OAAC,0MAAA,CAAA,WAAQ;wEAAC,WAAU;;;;;;kFACpB,8OAAC;wEAAK,WAAU;;4EACb,eAAe,OAAO,CAAC,2BAA2B,IAAI;4EAAE;;;;;;;;;;;;;;;;;;;;;;;;;0DAMjE,8OAAC,gIAAA,CAAA,OAAI;gDAAC,WAAU;;kEACd,8OAAC,gIAAA,CAAA,aAAU;wDAAC,WAAU;;0EACpB,8OAAC,gIAAA,CAAA,YAAS;gEAAC,WAAU;0EAAqC;;;;;;0EAC1D,8OAAC,oMAAA,CAAA,QAAK;gEAAC,WAAU;;;;;;;;;;;;kEAEnB,8OAAC,gIAAA,CAAA,cAAW;;0EACV,8OAAC;gEAAI,WAAU;0EACZ,eAAe,OAAO,CAAC,kBAAkB,IAAI;;;;;;0EAEhD,8OAAC;gEAAE,WAAU;0EAAyB;;;;;;0EACtC,8OAAC;gEAAI,WAAU;;kFACb,8OAAC,0MAAA,CAAA,WAAQ;wEAAC,WAAU;;;;;;kFACpB,8OAAC;wEAAK,WAAU;;4EACb,eAAe,OAAO,CAAC,cAAc,IAAI;4EAAE;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;kDAQtD,8OAAC;wCAAI,WAAU;;0DAEb,8OAAC,gIAAA,CAAA,OAAI;;kEACH,8OAAC,gIAAA,CAAA,aAAU;wDAAC,WAAU;;0EACpB,8OAAC;;kFACC,8OAAC,gIAAA,CAAA,YAAS;kFAAC;;;;;;kFACX,8OAAC,gIAAA,CAAA,kBAAe;kFAAC;;;;;;;;;;;;0EAEnB,8OAAC,kIAAA,CAAA,SAAM;gEACL,SAAQ;gEACR,MAAK;gEACL,SAAS,IAAM,mBAAmB,YAAY;;kFAE9C,8OAAC,0MAAA,CAAA,WAAQ;wEAAC,WAAU;;;;;;oEAAiB;;;;;;;;;;;;;kEAIzC,8OAAC,gIAAA,CAAA,cAAW;kEACV,cAAA,8OAAC,mKAAA,CAAA,sBAAmB;4DAAC,OAAM;4DAAO,QAAQ;sEACxC,cAAA,8OAAC,oJAAA,CAAA,WAAQ;gEAAC,MAAM,eAAe,OAAO,CAAC,YAAY,IAAI,EAAE;;kFACvD,8OAAC,6JAAA,CAAA,gBAAa;wEAAC,iBAAgB;;;;;;kFAC/B,8OAAC,qJAAA,CAAA,QAAK;wEAAC,SAAQ;;;;;;kFACf,8OAAC,qJAAA,CAAA,QAAK;;;;;kFACN,8OAAC,uJAAA,CAAA,UAAO;;;;;kFACR,8OAAC,mJAAA,CAAA,MAAG;wEAAC,SAAQ;wEAAQ,MAAK;wEAAU,MAAK;;;;;;;;;;;;;;;;;;;;;;;;;;;;0DAOjD,8OAAC,gIAAA,CAAA,OAAI;;kEACH,8OAAC,gIAAA,CAAA,aAAU;;0EACT,8OAAC,gIAAA,CAAA,YAAS;0EAAC;;;;;;0EACX,8OAAC,gIAAA,CAAA,kBAAe;0EAAC;;;;;;;;;;;;kEAEnB,8OAAC,gIAAA,CAAA,cAAW;kEACV,cAAA,8OAAC;4DAAI,WAAU;;8EACb,8OAAC;oEAAI,WAAU;;sFACb,8OAAC;4EAAK,WAAU;sFAAsB;;;;;;sFACtC,8OAAC,iIAAA,CAAA,QAAK;4EAAC,SAAQ;sFACZ,eAAe,OAAO,CAAC,WAAW,IAAI;;;;;;;;;;;;8EAG3C,8OAAC;oEAAI,WAAU;;sFACb,8OAAC;4EAAK,WAAU;sFAAsB;;;;;;sFACtC,8OAAC,iIAAA,CAAA,QAAK;4EAAC,SAAQ;sFACZ,eAAe,OAAO,CAAC,cAAc,IAAI;;;;;;;;;;;;8EAG9C,8OAAC;oEAAI,WAAU;;sFACb,8OAAC;4EAAK,WAAU;sFAAsB;;;;;;sFACtC,8OAAC,iIAAA,CAAA,QAAK;4EAAC,SAAQ;;gFACZ,eAAe,OAAO,CAAC,gBAAgB,EAAE,QAAQ,MAAM;gFAAE;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;6DASxE,8OAAC;gCAAI,WAAU;;kDACb,8OAAC,sMAAA,CAAA,SAAM;wCAAC,WAAU;;;;;;kDAClB,8OAAC;kDAAE;;;;;;;;;;;;;;;;;sCAMT,8OAAC,gIAAA,CAAA,cAAW;4BAAC,OAAM;4BAAM,WAAU;sCAChC,WAAW,sBACV,8OAAC;gCAAI,WAAU;;kDACb,8OAAC,oNAAA,CAAA,cAAW;wCAAC,WAAU;;;;;;kDACvB,8OAAC;wCAAG,WAAU;kDAA4C;;;;;;kDAC1D,8OAAC;wCAAE,WAAU;kDAAsB;;;;;;kDACnC,8OAAC;wCAAE,WAAU;kDAAyB;;;;;;;;;;;uCAEtC,WAAW,wBACb;;kDACE,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;;kEACC,8OAAC;wDAAG,WAAU;kEAAwB;;;;;;kEACtC,8OAAC;wDAAE,WAAU;kEAAyB;;;;;;;;;;;;0DAExC,8OAAC,kIAAA,CAAA,SAAM;gDACL,SAAQ;gDACR,MAAK;gDACL,SAAS,IAAM,mBAAmB,OAAO;;kEAEzC,8OAAC,0MAAA,CAAA,WAAQ;wDAAC,WAAU;;;;;;oDAAiB;;;;;;;;;;;;;oCAMxC,UAAU,OAAO,CAAC,YAAY,IAAI,UAAU,OAAO,CAAC,YAAY,CAAC,mBAAmB,GAAG,mBACtF,8OAAC,gIAAA,CAAA,OAAI;wCAAC,WAAU;;0DACd,8OAAC,gIAAA,CAAA,aAAU;gDAAC,WAAU;0DACpB,cAAA,8OAAC,gIAAA,CAAA,YAAS;oDAAC,WAAU;;sEACnB,8OAAC,wNAAA,CAAA,gBAAa;4DAAC,WAAU;;;;;;wDAAiB;;;;;;;;;;;;0DAI9C,8OAAC,gIAAA,CAAA,cAAW;;kEACV,8OAAC;wDAAE,WAAU;;0EACX,8OAAC;;oEAAQ,UAAU,OAAO,CAAC,YAAY,CAAC,mBAAmB;oEAAC;;;;;;;4DAAU;4DACpE,UAAU,OAAO,CAAC,YAAY,CAAC,eAAe;4DAAC;;;;;;;kEAEnD,8OAAC;wDAAI,WAAU;kEACZ,MAAM,OAAO,CAAC,UAAU,OAAO,CAAC,YAAY,CAAC,uBAAuB,IACnE,UAAU,OAAO,CAAC,YAAY,CAAC,uBAAuB,CAAC,GAAG,CAAC,CAAC,WAAgB,sBAC1E,8OAAC;;oEAAgB;kFACb,8OAAC;;4EAAQ,UAAU,SAAS;4EAAC;4EAAE,UAAU,UAAU;;;;;;;oEAAU;oEAAG,UAAU,YAAY;oEAAC;oEAAI,UAAU,QAAQ;oEAAC;;+DADxG;;;;sFAIV,8OAAC;sEAAI;;;;;;;;;;;kEAIX,8OAAC;wDAAI,WAAU;kEACb,cAAA,8OAAC;4DAAE,WAAU;;gEAAyB;8EACjC,8OAAC;8EAAO;;;;;;gEAAc;;;;;;;;;;;;;;;;;;;;;;;;oCASlC,UAAU,OAAO,CAAC,SAAS,kBAC1B,8OAAC;wCAAI,WAAU;;0DACb,8OAAC,gIAAA,CAAA,OAAI;gDAAC,WAAU;;kEACd,8OAAC,gIAAA,CAAA,aAAU;wDAAC,WAAU;kEACpB,cAAA,8OAAC,gIAAA,CAAA,YAAS;4DAAC,WAAU;sEAAqC;;;;;;;;;;;kEAE5D,8OAAC,gIAAA,CAAA,cAAW;;0EACV,8OAAC;gEAAI,WAAU;;oEACZ,UAAU,OAAO,CAAC,SAAS,CAAC,qBAAqB,EAAE,oBAAoB;oEAAE;;;;;;;0EAE5E,8OAAC;gEAAE,WAAU;0EAA8B;;;;;;;;;;;;;;;;;;0DAI/C,8OAAC,gIAAA,CAAA,OAAI;gDAAC,WAAU;;kEACd,8OAAC,gIAAA,CAAA,aAAU;wDAAC,WAAU;kEACpB,cAAA,8OAAC,gIAAA,CAAA,YAAS;4DAAC,WAAU;sEAAqC;;;;;;;;;;;kEAE5D,8OAAC,gIAAA,CAAA,cAAW;;0EACV,8OAAC;gEAAI,WAAU;;oEACZ,UAAU,OAAO,CAAC,SAAS,CAAC,oBAAoB,EAAE,oBAAoB;oEAAE;;;;;;;0EAE3E,8OAAC;gEAAE,WAAU;0EAA8B;;;;;;;;;;;;;;;;;;0DAI/C,8OAAC,gIAAA,CAAA,OAAI;gDAAC,WAAU;;kEACd,8OAAC,gIAAA,CAAA,aAAU;wDAAC,WAAU;kEACpB,cAAA,8OAAC,gIAAA,CAAA,YAAS;4DAAC,WAAU;sEAAqC;;;;;;;;;;;kEAE5D,8OAAC,gIAAA,CAAA,cAAW;;0EACV,8OAAC;gEAAI,WAAU;;oEACZ,UAAU,OAAO,CAAC,SAAS,CAAC,yBAAyB,EAAE,QAAQ,MAAM;oEAAE;;;;;;;0EAE1E,8OAAC;gEAAE,WAAU;0EAA8B;;;;;;;;;;;;;;;;;;0DAI/C,8OAAC,gIAAA,CAAA,OAAI;gDAAC,WAAU;;kEACd,8OAAC,gIAAA,CAAA,aAAU;wDAAC,WAAU;kEACpB,cAAA,8OAAC,gIAAA,CAAA,YAAS;4DAAC,WAAU;sEAAqC;;;;;;;;;;;kEAE5D,8OAAC,gIAAA,CAAA,cAAW;;0EACV,8OAAC;gEAAI,WAAU;0EACZ,UAAU,OAAO,CAAC,SAAS,CAAC,8BAA8B,IAAI;;;;;;0EAEjE,8OAAC;gEAAE,WAAU;0EAA8B;;;;;;;;;;;;;;;;;;;;;;;;kDAOnD,8OAAC,gIAAA,CAAA,OAAI;;0DACH,8OAAC,gIAAA,CAAA,aAAU;;kEACT,8OAAC,gIAAA,CAAA,YAAS;kEAAC;;;;;;kEACX,8OAAC,gIAAA,CAAA,kBAAe;kEAAC;;;;;;;;;;;;0DAEnB,8OAAC,gIAAA,CAAA,cAAW;0DACV,cAAA,8OAAC;oDAAI,WAAU;8DACb,cAAA,8OAAC;wDAAM,WAAU;;0EACf,8OAAC;0EACC,cAAA,8OAAC;oEAAG,WAAU;;sFACZ,8OAAC;4EAAG,WAAU;sFAAgB;;;;;;sFAC9B,8OAAC;4EAAG,WAAU;sFAAgB;;;;;;sFAC9B,8OAAC;4EAAG,WAAU;sFAAiB;;;;;;sFAC/B,8OAAC;4EAAG,WAAU;sFAAiB;;;;;;sFAC/B,8OAAC;4EAAG,WAAU;sFAAiB;;;;;;sFAC/B,8OAAC;4EAAG,WAAU;sFAAiB;;;;;;sFAC/B,8OAAC;4EAAG,WAAU;sFAAiB;;;;;;sFAC/B,8OAAC;4EAAG,WAAU;sFAAiB;;;;;;sFAC/B,8OAAC;4EAAG,WAAU;sFAAiB;;;;;;sFAC/B,8OAAC;4EAAG,WAAU;sFAAkB;;;;;;;;;;;;;;;;;0EAGpC,8OAAC;0EACE,UAAU,OAAO,CAAC,kBAAkB,EAAE,IAAI,CAAC,MAAW,sBACrD,8OAAC;wEAAe,WAAW,CAAC,2BAA2B,EAAE,KAAK,eAAe,GAAG,cAAc,IAAI;;0FAChG,8OAAC;gFAAG,WAAU;0FAAmB,KAAK,SAAS;;;;;;0FAC/C,8OAAC;gFAAG,WAAU;0FAAO,KAAK,UAAU;;;;;;0FACpC,8OAAC;gFAAG,WAAU;0FAAkB,KAAK,QAAQ;;;;;;0FAC7C,8OAAC;gFAAG,WAAU;0FAAkB,KAAK,oBAAoB,EAAE;;;;;;0FAC3D,8OAAC;gFAAG,WAAU;0FAAkB,KAAK,kBAAkB,EAAE;;;;;;0FACzD,8OAAC;gFAAG,WAAU;0FAAkB,KAAK,eAAe,EAAE;;;;;;0FACtD,8OAAC;gFAAG,WAAU;0FAAkB,KAAK,UAAU;;;;;;0FAC/C,8OAAC;gFAAG,WAAU;0FAAkB,KAAK,aAAa,EAAE;;;;;;0FACpD,8OAAC;gFAAG,WAAU;0FACX,KAAK,cAAc,GAAG,kBACrB,8OAAC;oFAAK,WAAU;;wFAAgB,KAAK,cAAc,EAAE;wFAAiB;;;;;;yGAEtE,8OAAC;oFAAK,WAAU;8FAAiB;;;;;;;;;;;0FAGrC,8OAAC;gFAAG,WAAU;0FACX,KAAK,kBAAkB,iBACtB,8OAAC,iIAAA,CAAA,QAAK;oFAAC,SAAQ;8FAAc;;;;;yGAE7B,8OAAC,iIAAA,CAAA,QAAK;oFAAC,SAAQ;8FAAY;;;;;;;;;;;;uEApBxB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;kDAgCrB,8OAAC,gIAAA,CAAA,OAAI;;0DACH,8OAAC,gIAAA,CAAA,aAAU;;kEACT,8OAAC,gIAAA,CAAA,YAAS;kEAAC;;;;;;kEACX,8OAAC,gIAAA,CAAA,kBAAe;kEAAC;;;;;;;;;;;;0DAEnB,8OAAC,gIAAA,CAAA,cAAW;0DACV,cAAA,8OAAC;oDAAI,WAAU;8DACb,cAAA,8OAAC;wDAAM,WAAU;;0EACf,8OAAC;0EACC,cAAA,8OAAC;oEAAG,WAAU;;sFACZ,8OAAC;4EAAG,WAAU;sFAAgB;;;;;;sFAC9B,8OAAC;4EAAG,WAAU;sFAAgB;;;;;;sFAC9B,8OAAC;4EAAG,WAAU;sFAAiB;;;;;;sFAC/B,8OAAC;4EAAG,WAAU;sFAAiB;;;;;;;;;;;;;;;;;0EAGnC,8OAAC;0EACE,UAAU,OAAO,CAAC,eAAe,EAAE,IAAI,CAAC,MAAW,sBAClD,8OAAC;wEAAe,WAAU;;0FACxB,8OAAC;gFAAG,WAAU;0FAAmB,KAAK,SAAS;;;;;;0FAC/C,8OAAC;gFAAG,WAAU;0FAAO,KAAK,UAAU;;;;;;0FACpC,8OAAC;gFAAG,WAAU;0FAAkB,KAAK,UAAU;;;;;;0FAC/C,8OAAC;gFAAG,WAAU;0FACZ,cAAA,8OAAC;oFAAK,WAAW,KAAK,iBAAiB,GAAG,IAAI,mBAAmB;8FAC9D,KAAK,iBAAiB,EAAE;;;;;;;;;;;;uEANtB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;6DAkBvB,8OAAC;gCAAI,WAAU;;kDACb,8OAAC,8MAAA,CAAA,WAAQ;wCAAC,WAAU;;;;;;kDACpB,8OAAC;kDAAE;;;;;;;;;;;;;;;;;sCAMT,8OAAC,gIAAA,CAAA,cAAW;4BAAC,OAAM;4BAAS,WAAU;sCACnC,sBAAsB,wBACrB;;kDACE,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;;kEACC,8OAAC;wDAAG,WAAU;kEAAwB;;;;;;kEACtC,8OAAC;wDAAE,WAAU;kEAAyB;;;;;;;;;;;;0DAExC,8OAAC,kIAAA,CAAA,SAAM;gDACL,SAAQ;gDACR,MAAK;gDACL,SAAS,IAAM,mBAAmB,mBAAmB;;kEAErD,8OAAC,0MAAA,CAAA,WAAQ;wDAAC,WAAU;;;;;;oDAAiB;;;;;;;;;;;;;kDAMzC,8OAAC;wCAAI,WAAU;;0DACb,8OAAC,gIAAA,CAAA,OAAI;;kEACH,8OAAC,gIAAA,CAAA,aAAU;wDAAC,WAAU;;0EACpB,8OAAC,gIAAA,CAAA,YAAS;gEAAC,WAAU;0EAAqC;;;;;;0EAC1D,8OAAC,wMAAA,CAAA,UAAO;gEAAC,WAAU;;;;;;;;;;;;kEAErB,8OAAC,gIAAA,CAAA,cAAW;;0EACV,8OAAC;gEAAI,WAAU;0EACZ,qBAAqB,OAAO,CAAC,aAAa,IAAI;;;;;;0EAEjD,8OAAC;gEAAE,WAAU;0EAAyB;;;;;;;;;;;;;;;;;;0DAI1C,8OAAC,gIAAA,CAAA,OAAI;;kEACH,8OAAC,gIAAA,CAAA,aAAU;wDAAC,WAAU;;0EACpB,8OAAC,gIAAA,CAAA,YAAS;gEAAC,WAAU;0EAAqC;;;;;;0EAC1D,8OAAC,0MAAA,CAAA,WAAQ;gEAAC,WAAU;;;;;;;;;;;;kEAEtB,8OAAC,gIAAA,CAAA,cAAW;;0EACV,8OAAC;gEAAI,WAAU;0EACZ,qBAAqB,OAAO,CAAC,MAAM,EAAE,OAAO,CAAC,IAC5C,EAAE,KAAK,KAAK,YAAY,EAAE,KAAK,KAAK,eACpC,UAAU;;;;;;0EAEd,8OAAC;gEAAE,WAAU;0EAAyB;;;;;;;;;;;;;;;;;;0DAI1C,8OAAC,gIAAA,CAAA,OAAI;;kEACH,8OAAC,gIAAA,CAAA,aAAU;wDAAC,WAAU;;0EACpB,8OAAC,gIAAA,CAAA,YAAS;gEAAC,WAAU;0EAAqC;;;;;;0EAC1D,8OAAC,kNAAA,CAAA,aAAU;gEAAC,WAAU;;;;;;;;;;;;kEAExB,8OAAC,gIAAA,CAAA,cAAW;;0EACV,8OAAC;gEAAI,WAAU;;oEACZ,qBAAqB,OAAO,CAAC,MAAM,EAAE,SAAS,IAC7C,CAAC,qBAAqB,OAAO,CAAC,MAAM,CAAC,MAAM,CAAC,CAAC,KAAa,IACxD,MAAM,CAAC,EAAE,oBAAoB,IAAI,CAAC,GAAG,KAAK,qBAAqB,OAAO,CAAC,MAAM,CAAC,MAAM,AACtF,EAAE,OAAO,CAAC,KAAK;oEAAE;;;;;;;0EAErB,8OAAC;gEAAE,WAAU;0EAAyB;;;;;;;;;;;;;;;;;;;;;;;;kDAM5C,8OAAC,gIAAA,CAAA,OAAI;;0DACH,8OAAC,gIAAA,CAAA,aAAU;;kEACT,8OAAC,gIAAA,CAAA,YAAS;kEAAC;;;;;;kEACX,8OAAC,gIAAA,CAAA,kBAAe;kEAAC;;;;;;;;;;;;0DAEnB,8OAAC,gIAAA,CAAA,cAAW;0DACV,cAAA,8OAAC;oDAAI,WAAU;8DACb,cAAA,8OAAC;wDAAM,WAAU;;0EACf,8OAAC;0EACC,cAAA,8OAAC;oEAAG,WAAU;;sFACZ,8OAAC;4EAAG,WAAU;sFAAgB;;;;;;sFAC9B,8OAAC;4EAAG,WAAU;sFAAgB;;;;;;sFAC9B,8OAAC;4EAAG,WAAU;sFAAiB;;;;;;sFAC/B,8OAAC;4EAAG,WAAU;sFAAiB;;;;;;sFAC/B,8OAAC;4EAAG,WAAU;sFAAiB;;;;;;sFAC/B,8OAAC;4EAAG,WAAU;sFAAiB;;;;;;sFAC/B,8OAAC;4EAAG,WAAU;sFAAgB;;;;;;sFAC9B,8OAAC;4EAAG,WAAU;sFAAiB;;;;;;;;;;;;;;;;;0EAGnC,8OAAC;0EACE,qBAAqB,OAAO,CAAC,MAAM,EAAE,IAAI,CAAC,QAAa,sBACtD,8OAAC;wEAAe,WAAU;;0FACxB,8OAAC;gFAAG,WAAU;0FAAmB,OAAO,MAAM;;;;;;0FAC9C,8OAAC;gFAAG,WAAU;0FAAO,OAAO,SAAS;;;;;;0FACrC,8OAAC;gFAAG,WAAU;0FAAkB,OAAO,YAAY,EAAE;;;;;;0FACrD,8OAAC;gFAAG,WAAU;0FAAkB,OAAO,gBAAgB,EAAE;;;;;;0FACzD,8OAAC;gFAAG,WAAU;0FAAkB,OAAO,aAAa,EAAE;;;;;;0FACtD,8OAAC;gFAAG,WAAU;0FACZ,cAAA,8OAAC;oFAAI,WAAU;;sGACb,8OAAC;;gGAAM,OAAO,oBAAoB,EAAE,QAAQ;gGAAG;;;;;;;sGAC/C,8OAAC,oIAAA,CAAA,WAAQ;4FACP,OAAO,KAAK,GAAG,CAAC,OAAO,oBAAoB,IAAI,GAAG;4FAClD,WAAU;;;;;;;;;;;;;;;;;0FAIhB,8OAAC;gFAAG,WAAU;0FACZ,cAAA,8OAAC,iIAAA,CAAA,QAAK;oFACJ,SACE,OAAO,KAAK,KAAK,gBAAgB,YACjC,OAAO,KAAK,KAAK,WAAW,cAC5B,OAAO,KAAK,KAAK,cAAc,YAC/B,OAAO,KAAK,KAAK,SAAS,gBAAgB;8FAG3C,OAAO,KAAK;;;;;;;;;;;0FAGjB,8OAAC;gFAAG,WAAU;0FAAkB,OAAO,qBAAqB,IAAI;;;;;;;uEA3BzD;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;6DAqCvB,8OAAC;gCAAI,WAAU;;kDACb,8OAAC,wMAAA,CAAA,UAAO;wCAAC,WAAU;;;;;;kDACnB,8OAAC;kDAAE;;;;;;;;;;;;;;;;;sCAMT,8OAAC,gIAAA,CAAA,cAAW;4BAAC,OAAM;4BAAe,WAAU;sCAC1C,cAAA,8OAAC;gCAAI,WAAU;;kDACb,8OAAC,gMAAA,CAAA,MAAG;wCAAC,WAAU;;;;;;kDACf,8OAAC;wCAAG,WAAU;kDAA6B;;;;;;kDAC3C,8OAAC;kDAAE;;;;;;kDACH,8OAAC;wCAAE,WAAU;kDAAe;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAY5C", "debugId": null}}]}
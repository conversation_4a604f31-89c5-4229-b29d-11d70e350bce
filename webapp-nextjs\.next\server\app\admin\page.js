(()=>{var e={};e.id=698,e.ids=[698],e.modules={1132:(e,t,a)=>{"use strict";a.r(t),a.d(t,{default:()=>s});let s=(0,a(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\CMS\\\\webapp-nextjs\\\\src\\\\app\\\\admin\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\CMS\\webapp-nextjs\\src\\app\\admin\\page.tsx","default")},3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},6211:(e,t,a)=>{"use strict";a.d(t,{A0:()=>l,BF:()=>n,Hj:()=>d,XI:()=>i,nA:()=>c,nd:()=>o});var s=a(60687);a(43210);var r=a(4780);function i({className:e,...t}){return(0,s.jsx)("div",{"data-slot":"table-container",className:"relative w-full overflow-x-auto",children:(0,s.jsx)("table",{"data-slot":"table",className:(0,r.cn)("w-full caption-bottom text-sm border-collapse",e),...t})})}function l({className:e,...t}){return(0,s.jsx)("thead",{"data-slot":"table-header",className:(0,r.cn)("[&_tr]:border-b",e),...t})}function n({className:e,...t}){return(0,s.jsx)("tbody",{"data-slot":"table-body",className:(0,r.cn)("[&_tr:last-child]:border-0",e),...t})}function d({className:e,...t}){return(0,s.jsx)("tr",{"data-slot":"table-row",className:(0,r.cn)("hover:bg-muted/50 data-[state=selected]:bg-muted border-b transition-colors",e),...t})}function o({className:e,...t}){return(0,s.jsx)("th",{"data-slot":"table-head",className:(0,r.cn)("text-foreground h-10 px-2 text-left align-middle font-medium whitespace-nowrap [&:has([role=checkbox])]:pr-0 [&>[role=checkbox]]:translate-y-[2px]",e),...t})}function c({className:e,...t}){return(0,s.jsx)("td",{"data-slot":"table-cell",className:(0,r.cn)("p-2 align-middle whitespace-nowrap [&:has([role=checkbox])]:pr-0 [&>[role=checkbox]]:translate-y-[2px]",e),...t})}},8819:(e,t,a)=>{"use strict";a.d(t,{A:()=>s});let s=(0,a(62688).A)("save",[["path",{d:"M15.2 3a2 2 0 0 1 1.4.6l3.8 3.8a2 2 0 0 1 .6 1.4V19a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2V5a2 2 0 0 1 2-2z",key:"1c8476"}],["path",{d:"M17 21v-7a1 1 0 0 0-1-1H8a1 1 0 0 0-1 1v7",key:"1ydtos"}],["path",{d:"M7 3v4a1 1 0 0 0 1 1h7",key:"t51u73"}]])},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},12412:e=>{"use strict";e.exports=require("assert")},12597:(e,t,a)=>{"use strict";a.d(t,{A:()=>s});let s=(0,a(62688).A)("eye-off",[["path",{d:"M10.733 5.076a10.744 10.744 0 0 1 11.205 6.575 1 1 0 0 1 0 .696 10.747 10.747 0 0 1-1.444 2.49",key:"ct8e1f"}],["path",{d:"M14.084 14.158a3 3 0 0 1-4.242-4.242",key:"151rxh"}],["path",{d:"M17.479 17.499a10.75 10.75 0 0 1-15.417-5.151 1 1 0 0 1 0-.696 10.75 10.75 0 0 1 4.446-5.143",key:"13bj9a"}],["path",{d:"m2 2 20 20",key:"1ooewy"}]])},13861:(e,t,a)=>{"use strict";a.d(t,{A:()=>s});let s=(0,a(62688).A)("eye",[["path",{d:"M2.062 12.348a1 1 0 0 1 0-.696 10.75 10.75 0 0 1 19.876 0 1 1 0 0 1 0 .696 10.75 10.75 0 0 1-19.876 0",key:"1nclc0"}],["circle",{cx:"12",cy:"12",r:"3",key:"1v7zrd"}]])},15079:(e,t,a)=>{"use strict";a.d(t,{bq:()=>x,eb:()=>u,gC:()=>m,l6:()=>o,yv:()=>c});var s=a(60687);a(43210);var r=a(97822),i=a(78272),l=a(13964),n=a(3589),d=a(4780);function o({...e}){return(0,s.jsx)(r.bL,{"data-slot":"select",...e})}function c({...e}){return(0,s.jsx)(r.WT,{"data-slot":"select-value",...e})}function x({className:e,size:t="default",children:a,...l}){return(0,s.jsxs)(r.l9,{"data-slot":"select-trigger","data-size":t,className:(0,d.cn)("border-input data-[placeholder]:text-muted-foreground [&_svg:not([class*='text-'])]:text-muted-foreground focus-visible:border-ring focus-visible:ring-ring/50 aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive dark:bg-input/30 dark:hover:bg-input/50 flex w-fit items-center justify-between gap-2 rounded-md border bg-transparent px-3 py-2 text-sm whitespace-nowrap shadow-xs transition-[color,box-shadow] outline-none focus-visible:ring-[3px] disabled:cursor-not-allowed disabled:opacity-50 data-[size=default]:h-9 data-[size=sm]:h-8 *:data-[slot=select-value]:line-clamp-1 *:data-[slot=select-value]:flex *:data-[slot=select-value]:items-center *:data-[slot=select-value]:gap-2 [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4",e),...l,children:[a,(0,s.jsx)(r.In,{asChild:!0,children:(0,s.jsx)(i.A,{className:"size-4 opacity-50"})})]})}function m({className:e,children:t,position:a="popper",...i}){return(0,s.jsx)(r.ZL,{children:(0,s.jsxs)(r.UC,{"data-slot":"select-content",className:(0,d.cn)("bg-popover text-popover-foreground data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2 relative z-50 max-h-(--radix-select-content-available-height) min-w-[8rem] origin-(--radix-select-content-transform-origin) overflow-x-hidden overflow-y-auto rounded-md border shadow-md","popper"===a&&"data-[side=bottom]:translate-y-1 data-[side=left]:-translate-x-1 data-[side=right]:translate-x-1 data-[side=top]:-translate-y-1",e),position:a,...i,children:[(0,s.jsx)(h,{}),(0,s.jsx)(r.LM,{className:(0,d.cn)("p-1","popper"===a&&"h-[var(--radix-select-trigger-height)] w-full min-w-[var(--radix-select-trigger-width)] scroll-my-1"),children:t}),(0,s.jsx)(p,{})]})})}function u({className:e,children:t,...a}){return(0,s.jsxs)(r.q7,{"data-slot":"select-item",className:(0,d.cn)("focus:bg-accent focus:text-accent-foreground [&_svg:not([class*='text-'])]:text-muted-foreground relative flex w-full cursor-default items-center gap-2 rounded-sm py-1.5 pr-8 pl-2 text-sm outline-hidden select-none data-[disabled]:pointer-events-none data-[disabled]:opacity-50 [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4 *:[span]:last:flex *:[span]:last:items-center *:[span]:last:gap-2",e),...a,children:[(0,s.jsx)("span",{className:"absolute right-2 flex size-3.5 items-center justify-center",children:(0,s.jsx)(r.VF,{children:(0,s.jsx)(l.A,{className:"size-4"})})}),(0,s.jsx)(r.p4,{children:t})]})}function h({className:e,...t}){return(0,s.jsx)(r.PP,{"data-slot":"select-scroll-up-button",className:(0,d.cn)("flex cursor-default items-center justify-center py-1",e),...t,children:(0,s.jsx)(n.A,{className:"size-4"})})}function p({className:e,...t}){return(0,s.jsx)(r.wn,{"data-slot":"select-scroll-down-button",className:(0,d.cn)("flex cursor-default items-center justify-center py-1",e),...t,children:(0,s.jsx)(i.A,{className:"size-4"})})}},15391:(e,t,a)=>{"use strict";a.d(t,{Fw:()=>d,Tr:()=>n,qn:()=>l});let s={SUCCESS:{bg:"bg-emerald-50",text:"text-emerald-700",border:"border-emerald-200",hover:"hover:bg-emerald-100",hex:"#10b981"},WARNING:{bg:"bg-amber-50",text:"text-amber-700",border:"border-amber-200",hover:"hover:bg-amber-100",hex:"#f59e0b"},ATTENTION:{bg:"bg-orange-50",text:"text-orange-700",border:"border-orange-200",hover:"hover:bg-orange-100",hex:"#ea580c"},ERROR:{bg:"bg-rose-50",text:"text-rose-700",border:"border-rose-200",hover:"hover:bg-rose-100",hex:"#e11d48"},INFO:{bg:"bg-sky-50",text:"text-sky-700",border:"border-sky-200",hover:"hover:bg-sky-100",hex:"#0284c7"},NEUTRAL:{bg:"bg-slate-50",text:"text-slate-700",border:"border-slate-200",hover:"hover:bg-slate-100",hex:"#475569"},PROGRESS:{bg:"bg-indigo-50",text:"text-indigo-700",border:"border-indigo-200",hover:"hover:bg-indigo-100",hex:"#4f46e5"}};s.SUCCESS,s.PROGRESS,s.NEUTRAL,s.WARNING,s.ERROR;let r={DA_INSTALLARE:s.NEUTRAL,INSTALLATO:s.SUCCESS,COLLEGATO_PARTENZA:s.INFO,COLLEGATO_ARRIVO:s.INFO,COLLEGATO:s.PROGRESS,CERTIFICATO:s.SUCCESS,SPARE:s.WARNING,ERRORE:s.ERROR},i={ATTIVA:s.SUCCESS,COMPLETATA:s.PROGRESS,ANNULLATA:s.NEUTRAL,IN_CORSO:s.INFO,ERRORE:s.ERROR},l=e=>{let t=s[e];return{badge:`${t.bg} ${t.text} ${t.border}`,button:`${t.bg} ${t.text} ${t.border} ${t.hover}`,alert:`${t.bg} ${t.text} ${t.border}`,text:t.text,bg:t.bg,border:t.border,hover:t.hover,hex:t.hex}},n=e=>{let t=r[e?.toUpperCase().replace(/\s+/g,"_")]||r.ERRORE;return{badge:`${t.bg} ${t.text} ${t.border}`,button:`${t.bg} ${t.text} ${t.border} ${t.hover}`,alert:`${t.bg} ${t.text} ${t.border}`,text:t.text,bg:t.bg,border:t.border,hover:t.hover,hex:t.hex}},d=e=>{let t=i[e?.toUpperCase().replace(/\s+/g,"_")]||i.ERRORE;return{badge:`${t.bg} ${t.text} ${t.border}`,button:`${t.bg} ${t.text} ${t.border} ${t.hover}`,alert:`${t.bg} ${t.text} ${t.border}`,text:t.text,bg:t.bg,border:t.border,hover:t.hover,hex:t.hex}};s.ERROR,s.WARNING,s.INFO,s.NEUTRAL},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},21820:e=>{"use strict";e.exports=require("os")},23652:(e,t,a)=>{"use strict";a.r(t),a.d(t,{default:()=>H});var s=a(60687),r=a(43210),i=a(44493),l=a(55527),n=a(63143),d=a(48730),o=a(5336),c=a(88233);function x({user:e,onEdit:t,onToggleStatus:a,onDelete:r}){return(0,s.jsxs)("div",{className:"flex items-center gap-1",children:[(0,s.jsx)("button",{onClick:e=>{e.preventDefault(),e.stopPropagation(),t()},type:"button",className:"p-1.5 rounded hover:bg-blue-50 transition-colors",title:"Modifica utente",children:(0,s.jsx)(n.A,{className:"h-4 w-4 text-blue-600 hover:text-blue-700"})}),(0,s.jsx)("button",{onClick:e=>{e.preventDefault(),e.stopPropagation(),a()},disabled:"owner"===e.ruolo,type:"button",className:`p-1.5 rounded transition-colors ${"owner"===e.ruolo?"opacity-50 cursor-not-allowed":"hover:bg-slate-50"}`,title:e.abilitato?"Disabilita utente":"Abilita utente",children:e.abilitato?(0,s.jsx)(d.A,{className:"h-4 w-4 text-red-500 hover:text-red-600"}):(0,s.jsx)(o.A,{className:"h-4 w-4 text-green-500 hover:text-green-600"})}),(0,s.jsx)("button",{onClick:e=>{e.preventDefault(),e.stopPropagation(),r()},disabled:"owner"===e.ruolo,type:"button",className:`p-1.5 rounded transition-colors ${"owner"===e.ruolo?"opacity-50 cursor-not-allowed":"hover:bg-red-50"}`,title:"Elimina utente",children:(0,s.jsx)(c.A,{className:"h-4 w-4 text-red-500 hover:text-red-600"})})]})}var m=a(96834),u=a(15391),h=a(6211),p=a(56770),g=a(63213),b=a(16189),v=a(62185),j=a(29523),f=a(89667),N=a(80013),y=a(15079),w=a(56896),A=a(81806),z=a(12597),E=a(13861),k=a(11860),S=a(8819);function C({user:e,onSave:t,onCancel:a}){let[n,d]=(0,r.useState)({username:"",password:"",ruolo:"user",data_scadenza:"",abilitato:!0,ragione_sociale:"",indirizzo:"",nazione:"",email:"",vat:"",referente_aziendale:""}),[o,c]=(0,r.useState)({}),[x,m]=(0,r.useState)(!1),[u,h]=(0,r.useState)(""),[p,g]=(0,r.useState)(!1),b=(e,t)=>{d(a=>({...a,[e]:t})),o[e]&&c(t=>({...t,[e]:""}))},C=()=>{let t=(0,A.GN)({username:n.username,password:e?void 0:n.password,ragione_sociale:n.ragione_sociale,email:n.email,vat:n.vat,indirizzo:n.indirizzo,nazione:n.nazione,referente_aziendale:n.referente_aziendale});return c(t.errors),t.isValid},R=async a=>{a.preventDefault();let s=`user-form-${e?.id_utente||"new"}-${Date.now()}`;if(!(0,A.Eb)(s,5,6e4))return void h("Troppi tentativi. Riprova tra un minuto.");if(C()){m(!0),h("");try{let a,s={...n};e||(s.ruolo="user"),e&&!s.password.trim()&&delete s.password,s.data_scadenza&&(s.data_scadenza=s.data_scadenza),a=e?await v.dG.updateUser(e.id_utente,s):await v.dG.createUser(s),t(a)}catch(e){h(e.response?.data?.detail||e.message||"Errore durante il salvataggio dell'utente")}finally{m(!1)}}};return(0,s.jsxs)(i.Zp,{children:[(0,s.jsx)(i.aR,{children:(0,s.jsx)(i.ZB,{children:e?`Modifica Utente: ${e.username}`:"Crea Nuovo Utente Standard"})}),(0,s.jsxs)(i.Wu,{children:[u&&(0,s.jsx)("div",{className:"bg-red-50 border border-red-200 rounded-lg p-4 mb-6",children:(0,s.jsx)("p",{className:"text-red-600",children:u})}),(0,s.jsxs)("form",{onSubmit:R,className:"space-y-6",children:[(0,s.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6",children:[(0,s.jsxs)("div",{className:"space-y-2",children:[(0,s.jsx)(N.J,{htmlFor:"username",children:"Username *"}),(0,s.jsx)(f.p,{id:"username",value:n.username,onChange:e=>b("username",e.target.value),disabled:x,className:o.username?"border-red-500":""}),o.username&&(0,s.jsx)("p",{className:"text-sm text-red-600",children:o.username})]}),(0,s.jsxs)("div",{className:"space-y-2",children:[(0,s.jsx)(N.J,{htmlFor:"password",children:e?"Nuova Password (lascia vuoto per non modificare)":"Password *"}),(0,s.jsxs)("div",{className:"relative",children:[(0,s.jsx)(f.p,{id:"password",type:p?"text":"password",value:n.password,onChange:e=>b("password",e.target.value),disabled:x,className:o.password?"border-red-500 pr-10":"pr-10"}),(0,s.jsx)(j.$,{type:"button",variant:"ghost",size:"sm",className:"absolute right-0 top-0 h-full px-3 py-2 hover:bg-transparent",onClick:()=>g(!p),disabled:x,children:p?(0,s.jsx)(z.A,{className:"h-4 w-4 text-gray-400"}):(0,s.jsx)(E.A,{className:"h-4 w-4 text-gray-400"})})]}),o.password&&(0,s.jsx)("p",{className:"text-sm text-red-600",children:o.password})]}),(0,s.jsxs)("div",{className:"space-y-2",children:[(0,s.jsx)(N.J,{htmlFor:"ragione_sociale",children:"Ragione Sociale *"}),(0,s.jsx)(f.p,{id:"ragione_sociale",value:n.ragione_sociale,onChange:e=>b("ragione_sociale",e.target.value),disabled:x,className:o.ragione_sociale?"border-red-500":""}),o.ragione_sociale&&(0,s.jsx)("p",{className:"text-sm text-red-600",children:o.ragione_sociale})]}),(0,s.jsxs)("div",{className:"space-y-2",children:[(0,s.jsx)(N.J,{htmlFor:"email",children:"Email"}),(0,s.jsx)(f.p,{id:"email",type:"email",value:n.email,onChange:e=>b("email",e.target.value),disabled:x,className:o.email?"border-red-500":""}),o.email&&(0,s.jsx)("p",{className:"text-sm text-red-600",children:o.email})]}),(0,s.jsxs)("div",{className:"space-y-2",children:[(0,s.jsx)(N.J,{htmlFor:"indirizzo",children:"Indirizzo"}),(0,s.jsx)(f.p,{id:"indirizzo",value:n.indirizzo,onChange:e=>b("indirizzo",e.target.value),disabled:x})]}),(0,s.jsxs)("div",{className:"space-y-2",children:[(0,s.jsx)(N.J,{htmlFor:"nazione",children:"Nazione"}),(0,s.jsx)(f.p,{id:"nazione",value:n.nazione,onChange:e=>b("nazione",e.target.value),disabled:x})]}),(0,s.jsxs)("div",{className:"space-y-2",children:[(0,s.jsx)(N.J,{htmlFor:"vat",children:"VAT"}),(0,s.jsx)(f.p,{id:"vat",value:n.vat,onChange:e=>b("vat",e.target.value),disabled:x})]}),(0,s.jsxs)("div",{className:"space-y-2",children:[(0,s.jsx)(N.J,{htmlFor:"referente_aziendale",children:"Referente Aziendale"}),(0,s.jsx)(f.p,{id:"referente_aziendale",value:n.referente_aziendale,onChange:e=>b("referente_aziendale",e.target.value),disabled:x})]}),(0,s.jsxs)("div",{className:"space-y-2",children:[(0,s.jsx)(N.J,{htmlFor:"data_scadenza",children:"Data Scadenza"}),(0,s.jsx)(f.p,{id:"data_scadenza",type:"date",value:n.data_scadenza,onChange:e=>b("data_scadenza",e.target.value),disabled:x})]}),e?(0,s.jsxs)("div",{className:"space-y-2",children:[(0,s.jsx)(N.J,{htmlFor:"ruolo",children:"Ruolo"}),(0,s.jsxs)(y.l6,{value:n.ruolo,onValueChange:e=>b("ruolo",e),disabled:x,children:[(0,s.jsx)(y.bq,{children:(0,s.jsx)(y.yv,{})}),(0,s.jsxs)(y.gC,{children:[(0,s.jsx)(y.eb,{value:"user",children:"User"}),(0,s.jsx)(y.eb,{value:"cantieri_user",children:"Cantieri User"})]})]})]}):(0,s.jsxs)("div",{className:"space-y-2",children:[(0,s.jsx)(N.J,{htmlFor:"ruolo",children:"Ruolo"}),(0,s.jsx)("div",{className:"px-3 py-2 bg-slate-50 border border-slate-200 rounded-md text-sm text-slate-600",children:"User (Standard)"})]})]}),(0,s.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,s.jsx)(w.S,{id:"abilitato",checked:n.abilitato,onCheckedChange:e=>b("abilitato",e),disabled:x||e&&"owner"===e.ruolo}),(0,s.jsx)(N.J,{htmlFor:"abilitato",children:"Utente abilitato"})]}),(0,s.jsxs)("div",{className:"flex justify-end space-x-4 pt-6",children:[(0,s.jsx)(l.tA,{type:"button",onClick:a,disabled:x,icon:(0,s.jsx)(k.A,{className:"h-4 w-4"}),children:"Annulla"}),(0,s.jsx)(l.jn,{type:"submit",loading:x,icon:(0,s.jsx)(S.A,{className:"h-4 w-4"}),glow:!0,children:x?"Salvataggio...":"Salva"})]})]})]})]})}var R=a(61611),T=a(78122),_=a(41862);function V(){let[e,t]=(0,r.useState)(null),[a,n]=(0,r.useState)(!1),[d,o]=(0,r.useState)(""),c=async()=>{n(!0),o("");try{let e=await v.dG.getDatabaseData();t(e)}catch(e){o(e.response?.data?.detail||e.message||"Errore durante il caricamento dei dati del database")}finally{n(!1)}},x=(e,t,a)=>{if(!t||0===t.length)return(0,s.jsxs)("div",{className:"text-center py-4 text-slate-500 border rounded-lg",children:["Nessun dato disponibile per ",a]});let r=Object.keys(t[0]);return(0,s.jsxs)("div",{className:"border rounded-lg overflow-hidden mb-6",children:[(0,s.jsxs)("div",{className:"bg-slate-100 px-4 py-3 border-b",children:[(0,s.jsx)("h4",{className:"font-medium text-slate-900",children:a}),(0,s.jsxs)("p",{className:"text-sm text-slate-600",children:["Totale record: ",t.length]})]}),(0,s.jsx)("div",{className:"overflow-x-auto max-h-96",children:(0,s.jsxs)(h.XI,{children:[(0,s.jsx)(h.A0,{className:"sticky top-0 bg-slate-50",children:(0,s.jsx)(h.Hj,{children:r.map(e=>(0,s.jsx)(h.nd,{className:"font-medium",children:e},e))})}),(0,s.jsx)(h.BF,{children:t.map((e,t)=>(0,s.jsx)(h.Hj,{children:r.map(t=>(0,s.jsx)(h.nA,{className:"font-mono text-sm",children:null!==e[t]&&void 0!==e[t]?String(e[t]):(0,s.jsx)("span",{className:"text-slate-400",children:"NULL"})},t))},t))})]})})]})},m=[{key:"users",title:"Utenti",description:"Tutti gli utenti del sistema"},{key:"cantieri",title:"Cantieri",description:"Tutti i cantieri/progetti"},{key:"cavi",title:"Cavi",description:"Tutti i cavi installati"},{key:"parco_cavi",title:"Bobine",description:"Tutte le bobine del parco cavi"},{key:"strumenti_certificati",title:"Strumenti",description:"Strumenti certificati"},{key:"certificazioni_cavi",title:"Certificazioni",description:"Certificazioni dei cavi"}];return(0,s.jsxs)(i.Zp,{children:[(0,s.jsx)(i.aR,{children:(0,s.jsxs)("div",{className:"flex items-center justify-between",children:[(0,s.jsxs)(i.ZB,{className:"flex items-center gap-2",children:[(0,s.jsx)(R.A,{className:"h-5 w-5"}),"Visualizzazione Database Raw"]}),(0,s.jsx)(l.jn,{size:"sm",onClick:c,loading:a,icon:(0,s.jsx)(T.A,{className:"h-4 w-4"}),children:"Aggiorna"})]})}),(0,s.jsxs)(i.Wu,{className:"space-y-6",children:[(0,s.jsx)("div",{className:"bg-blue-50 border border-blue-200 rounded-lg p-4",children:(0,s.jsxs)("div",{className:"flex items-start gap-3",children:[(0,s.jsx)(E.A,{className:"h-5 w-5 text-blue-600 mt-0.5"}),(0,s.jsxs)("div",{children:[(0,s.jsx)("h4",{className:"font-medium text-blue-900",children:"Visualizzazione Raw del Database"}),(0,s.jsx)("p",{className:"text-sm text-blue-700 mt-1",children:"Questa sezione mostra i dati grezzi delle tabelle del database. Utile per debugging e analisi dei dati."})]})]})}),a?(0,s.jsxs)("div",{className:"flex items-center justify-center py-12",children:[(0,s.jsx)(_.A,{className:"h-8 w-8 animate-spin mr-3"}),(0,s.jsx)("span",{className:"text-lg",children:"Caricamento dati database..."})]}):d?(0,s.jsxs)("div",{className:"bg-red-50 border border-red-200 rounded-lg p-6",children:[(0,s.jsx)("p",{className:"text-red-600 font-medium",children:"Errore durante il caricamento:"}),(0,s.jsx)("p",{className:"text-red-600",children:d})]}):e?(0,s.jsxs)("div",{className:"space-y-8",children:[(0,s.jsx)("div",{className:"bg-blue-50 border border-blue-200 rounded-lg p-4",children:(0,s.jsx)("p",{className:"text-sm text-blue-700",children:"Visualizzazione completa di tutte le tabelle del database. I dati sono mostrati in formato raw per debugging e analisi."})}),m.map(t=>e[t.key]&&(0,s.jsxs)("div",{children:[(0,s.jsxs)("div",{className:"mb-4",children:[(0,s.jsx)("h3",{className:"text-xl font-semibold text-slate-900",children:t.title}),(0,s.jsx)("p",{className:"text-sm text-slate-600",children:t.description})]}),x(t.key,e[t.key],t.title)]},t.key)),(0,s.jsxs)("div",{className:"bg-slate-50 border border-slate-200 rounded-lg p-4",children:[(0,s.jsx)("h4",{className:"font-medium text-slate-900 mb-2",children:"Riepilogo Database"}),(0,s.jsx)("div",{className:"grid grid-cols-2 md:grid-cols-3 gap-4 text-sm",children:m.map(t=>(0,s.jsxs)("div",{className:"flex justify-between",children:[(0,s.jsxs)("span",{className:"text-slate-600",children:[t.title,":"]}),(0,s.jsxs)("span",{className:"font-medium",children:[e[t.key]?e[t.key].length:0," record"]})]},t.key))})]})]}):(0,s.jsx)("div",{className:"text-center py-12 text-slate-500",children:"Nessun dato disponibile"})]})]})}var I=a(62688);let M=(0,I.A)("rotate-ccw",[["path",{d:"M3 12a9 9 0 1 0 9-9 9.75 9.75 0 0 0-6.74 2.74L3 8",key:"1357e3"}],["path",{d:"M3 3v5h5",key:"1xhq8a"}]]);var D=a(43649);function P(){let[e,t]=(0,r.useState)(""),[a,n]=(0,r.useState)(!1),[d,o]=(0,r.useState)(!1),[x,m]=(0,r.useState)(""),[u,h]=(0,r.useState)(""),p=async()=>{if("RESET DATABASE"!==e||!a)return void m("Conferma richiesta per procedere con il reset");o(!0),m(""),h("");try{await v.dG.resetDatabase(),h("Database resettato con successo! Tutti i dati sono stati eliminati."),t(""),n(!1)}catch(e){m(e.response?.data?.detail||e.message||"Errore durante il reset del database")}finally{o(!1)}},g="RESET DATABASE"===e&&a&&!d;return(0,s.jsxs)(i.Zp,{children:[(0,s.jsx)(i.aR,{children:(0,s.jsxs)(i.ZB,{className:"flex items-center gap-2 text-red-600",children:[(0,s.jsx)(M,{className:"h-5 w-5"}),"Reset Database"]})}),(0,s.jsxs)(i.Wu,{className:"space-y-6",children:[(0,s.jsx)("div",{className:"bg-red-50 border border-red-200 rounded-lg p-4",children:(0,s.jsxs)("div",{className:"flex items-start gap-3",children:[(0,s.jsx)(D.A,{className:"h-6 w-6 text-red-600 mt-0.5"}),(0,s.jsxs)("div",{children:[(0,s.jsx)("h4",{className:"font-bold text-red-900 text-lg",children:"⚠️ ATTENZIONE - OPERAZIONE IRREVERSIBILE"}),(0,s.jsxs)("div",{className:"text-red-700 mt-2 space-y-2",children:[(0,s.jsx)("p",{className:"font-medium",children:"Questa operazione eliminer\xe0 PERMANENTEMENTE tutti i dati dal database:"}),(0,s.jsxs)("ul",{className:"list-disc list-inside space-y-1 text-sm",children:[(0,s.jsx)("li",{children:"Tutti gli utenti (eccetto l'amministratore principale)"}),(0,s.jsx)("li",{children:"Tutti i cantieri e i progetti"}),(0,s.jsx)("li",{children:"Tutti i cavi installati"}),(0,s.jsx)("li",{children:"Tutte le bobine del parco cavi"}),(0,s.jsx)("li",{children:"Tutti i comandi e le certificazioni"}),(0,s.jsx)("li",{children:"Tutti i report e i dati di produttivit\xe0"})]}),(0,s.jsx)("p",{className:"font-bold text-red-800 mt-3",children:"NON \xc8 POSSIBILE RECUPERARE I DATI DOPO IL RESET!"})]})]})]})}),x&&(0,s.jsx)("div",{className:"bg-red-50 border border-red-200 rounded-lg p-4",children:(0,s.jsx)("p",{className:"text-red-600",children:x})}),u&&(0,s.jsx)("div",{className:"bg-green-50 border border-green-200 rounded-lg p-4",children:(0,s.jsx)("p",{className:"text-green-600",children:u})}),(0,s.jsxs)("div",{className:"space-y-4 border-t pt-6",children:[(0,s.jsxs)("div",{children:[(0,s.jsx)("h4",{className:"font-semibold text-slate-900 mb-4",children:"Conferma Reset Database"}),(0,s.jsx)("p",{className:"text-sm text-slate-600 mb-4",children:"Per procedere con il reset, devi confermare l'operazione seguendo questi passaggi:"})]}),(0,s.jsxs)("div",{className:"space-y-4",children:[(0,s.jsxs)("div",{className:"space-y-2",children:[(0,s.jsxs)(N.J,{htmlFor:"confirm-text",className:"text-sm font-medium",children:["1. Digita esattamente: ",(0,s.jsx)("code",{className:"bg-slate-100 px-2 py-1 rounded text-red-600 font-bold",children:"RESET DATABASE"})]}),(0,s.jsx)(f.p,{id:"confirm-text",value:e,onChange:e=>t(e.target.value),placeholder:"Digita: RESET DATABASE",disabled:d,className:"RESET DATABASE"===e?"border-green-500":""})]}),(0,s.jsxs)("div",{className:"flex items-start space-x-3",children:[(0,s.jsx)(w.S,{id:"confirm-checkbox",checked:a,onCheckedChange:n,disabled:d}),(0,s.jsx)(N.J,{htmlFor:"confirm-checkbox",className:"text-sm leading-relaxed",children:"2. Confermo di aver compreso che questa operazione eliminer\xe0 TUTTI i dati dal database in modo PERMANENTE e IRREVERSIBILE. Ho effettuato un backup se necessario."})]})]}),(0,s.jsxs)("div",{className:"bg-slate-50 border border-slate-200 rounded-lg p-4",children:[(0,s.jsx)("h5",{className:"font-medium text-slate-900 mb-2",children:"Stato Conferma:"}),(0,s.jsxs)("div",{className:"space-y-1 text-sm",children:[(0,s.jsxs)("div",{className:"flex items-center gap-2",children:[(0,s.jsx)("div",{className:`w-3 h-3 rounded-full ${"RESET DATABASE"===e?"bg-green-500":"bg-red-500"}`}),(0,s.jsxs)("span",{children:["Testo di conferma: ","RESET DATABASE"===e?"✓ Corretto":"✗ Richiesto"]})]}),(0,s.jsxs)("div",{className:"flex items-center gap-2",children:[(0,s.jsx)("div",{className:`w-3 h-3 rounded-full ${a?"bg-green-500":"bg-red-500"}`}),(0,s.jsxs)("span",{children:["Checkbox confermata: ",a?"✓ S\xec":"✗ Richiesta"]})]})]})]}),(0,s.jsx)(l.Qi,{onClick:p,disabled:!g,className:"w-full",size:"lg",loading:d,icon:(0,s.jsx)(c.A,{className:"h-5 w-5"}),glow:!0,children:d?"Reset in corso...":"RESET DATABASE - ELIMINA TUTTI I DATI"}),!g&&(0,s.jsx)("p",{className:"text-center text-sm text-slate-500",children:"Completa tutti i passaggi di conferma per abilitare il reset"})]}),(0,s.jsxs)("div",{className:"bg-blue-50 border border-blue-200 rounded-lg p-4 text-sm",children:[(0,s.jsx)("h5",{className:"font-medium text-blue-900 mb-2",children:"Informazioni Tecniche:"}),(0,s.jsxs)("ul",{className:"text-blue-700 space-y-1",children:[(0,s.jsx)("li",{children:"• Il reset manterr\xe0 la struttura delle tabelle"}),(0,s.jsx)("li",{children:"• L'utente amministratore principale verr\xe0 ricreato"}),(0,s.jsx)("li",{children:"• Le configurazioni di sistema verranno ripristinate ai valori di default"}),(0,s.jsx)("li",{children:"• L'operazione pu\xf2 richiedere alcuni minuti per completarsi"})]})]})]})]})}var L=a(23361);let $=(0,I.A)("tag",[["path",{d:"M12.586 2.586A2 2 0 0 0 11.172 2H4a2 2 0 0 0-2 2v7.172a2 2 0 0 0 .586 1.414l8.704 8.704a2.426 2.426 0 0 0 3.42 0l6.58-6.58a2.426 2.426 0 0 0 0-3.42z",key:"vktsd0"}],["circle",{cx:"7.5",cy:"7.5",r:".5",fill:"currentColor",key:"kqv944"}]]),U=(0,I.A)("building",[["rect",{width:"16",height:"20",x:"4",y:"2",rx:"2",ry:"2",key:"76otgf"}],["path",{d:"M9 22v-4h6v4",key:"r93iot"}],["path",{d:"M8 6h.01",key:"1dz90k"}],["path",{d:"M16 6h.01",key:"1x0f13"}],["path",{d:"M12 6h.01",key:"1vi96p"}],["path",{d:"M12 10h.01",key:"1nrarc"}],["path",{d:"M12 14h.01",key:"1etili"}],["path",{d:"M16 10h.01",key:"1m94wz"}],["path",{d:"M16 14h.01",key:"1gbofw"}],["path",{d:"M8 10h.01",key:"19clt8"}],["path",{d:"M8 14h.01",key:"6423bh"}]]);var O=a(10022),G=a(96474);function q(){let[e,t]=(0,r.useState)("categorie");return(0,s.jsxs)(i.Zp,{children:[(0,s.jsx)(i.aR,{children:(0,s.jsxs)(i.ZB,{className:"flex items-center gap-2",children:[(0,s.jsx)(L.A,{className:"h-5 w-5"}),"Database Tipologie Cavi"]})}),(0,s.jsxs)(i.Wu,{className:"space-y-6",children:[(0,s.jsx)("div",{className:"bg-blue-50 border border-blue-200 rounded-lg p-4",children:(0,s.jsxs)("div",{className:"flex items-start gap-3",children:[(0,s.jsx)(L.A,{className:"h-5 w-5 text-blue-600 mt-0.5"}),(0,s.jsxs)("div",{children:[(0,s.jsx)("h4",{className:"font-medium text-blue-900",children:"Database Enciclopedico Tipologie Cavi"}),(0,s.jsx)("p",{className:"text-sm text-blue-700 mt-1",children:"Gestisci il database delle tipologie di cavi organizzato per categorie, produttori, standard e tipologie specifiche. Questo database serve come riferimento per la classificazione e gestione dei cavi nei progetti."})]})]})}),(0,s.jsxs)(p.tU,{value:e,onValueChange:t,className:"w-full",children:[(0,s.jsxs)(p.j7,{className:"grid w-full grid-cols-4",children:[(0,s.jsxs)(p.Xi,{value:"categorie",className:"flex items-center gap-2",children:[(0,s.jsx)($,{className:"h-4 w-4"}),"Categorie"]}),(0,s.jsxs)(p.Xi,{value:"produttori",className:"flex items-center gap-2",children:[(0,s.jsx)(U,{className:"h-4 w-4"}),"Produttori"]}),(0,s.jsxs)(p.Xi,{value:"standard",className:"flex items-center gap-2",children:[(0,s.jsx)(O.A,{className:"h-4 w-4"}),"Standard"]}),(0,s.jsxs)(p.Xi,{value:"tipologie",className:"flex items-center gap-2",children:[(0,s.jsx)(L.A,{className:"h-4 w-4"}),"Tipologie"]})]}),(0,s.jsxs)(p.av,{value:"categorie",className:"space-y-4",children:[(0,s.jsxs)("div",{className:"flex items-center justify-between",children:[(0,s.jsxs)("div",{children:[(0,s.jsx)("h3",{className:"text-lg font-semibold",children:"Categorie Cavi"}),(0,s.jsx)("p",{className:"text-sm text-slate-600",children:"Gestisci le categorie principali di cavi (es. Energia, Controllo, Strumentazione, ecc.)"})]}),(0,s.jsxs)(j.$,{children:[(0,s.jsx)(G.A,{className:"h-4 w-4 mr-2"}),"Nuova Categoria"]})]}),(0,s.jsxs)("div",{className:"text-center py-12 text-slate-500",children:[(0,s.jsx)($,{className:"h-12 w-12 mx-auto mb-4 text-slate-400"}),(0,s.jsx)("p",{children:"Gestione categorie cavi - Da implementare"}),(0,s.jsx)("p",{className:"text-sm mt-2",children:"Qui sar\xe0 possibile creare, modificare ed eliminare le categorie di cavi"})]})]}),(0,s.jsxs)(p.av,{value:"produttori",className:"space-y-4",children:[(0,s.jsxs)("div",{className:"flex items-center justify-between",children:[(0,s.jsxs)("div",{children:[(0,s.jsx)("h3",{className:"text-lg font-semibold",children:"Produttori"}),(0,s.jsx)("p",{className:"text-sm text-slate-600",children:"Gestisci l'elenco dei produttori di cavi (es. Prysmian, Nexans, General Cable, ecc.)"})]}),(0,s.jsxs)(j.$,{children:[(0,s.jsx)(G.A,{className:"h-4 w-4 mr-2"}),"Nuovo Produttore"]})]}),(0,s.jsxs)("div",{className:"text-center py-12 text-slate-500",children:[(0,s.jsx)(U,{className:"h-12 w-12 mx-auto mb-4 text-slate-400"}),(0,s.jsx)("p",{children:"Gestione produttori - Da implementare"}),(0,s.jsx)("p",{className:"text-sm mt-2",children:"Qui sar\xe0 possibile gestire l'anagrafica dei produttori di cavi"})]})]}),(0,s.jsxs)(p.av,{value:"standard",className:"space-y-4",children:[(0,s.jsxs)("div",{className:"flex items-center justify-between",children:[(0,s.jsxs)("div",{children:[(0,s.jsx)("h3",{className:"text-lg font-semibold",children:"Standard e Normative"}),(0,s.jsx)("p",{className:"text-sm text-slate-600",children:"Gestisci gli standard tecnici e le normative (es. CEI, IEC, EN, CENELEC, ecc.)"})]}),(0,s.jsxs)(j.$,{children:[(0,s.jsx)(G.A,{className:"h-4 w-4 mr-2"}),"Nuovo Standard"]})]}),(0,s.jsxs)("div",{className:"text-center py-12 text-slate-500",children:[(0,s.jsx)(O.A,{className:"h-12 w-12 mx-auto mb-4 text-slate-400"}),(0,s.jsx)("p",{children:"Gestione standard - Da implementare"}),(0,s.jsx)("p",{className:"text-sm mt-2",children:"Qui sar\xe0 possibile gestire gli standard tecnici e le normative di riferimento"})]})]}),(0,s.jsxs)(p.av,{value:"tipologie",className:"space-y-4",children:[(0,s.jsxs)("div",{className:"flex items-center justify-between",children:[(0,s.jsxs)("div",{children:[(0,s.jsx)("h3",{className:"text-lg font-semibold",children:"Tipologie Specifiche"}),(0,s.jsx)("p",{className:"text-sm text-slate-600",children:"Gestisci le tipologie specifiche di cavi con tutte le caratteristiche tecniche"})]}),(0,s.jsxs)(j.$,{children:[(0,s.jsx)(G.A,{className:"h-4 w-4 mr-2"}),"Nuova Tipologia"]})]}),(0,s.jsxs)("div",{className:"text-center py-12 text-slate-500",children:[(0,s.jsx)(L.A,{className:"h-12 w-12 mx-auto mb-4 text-slate-400"}),(0,s.jsx)("p",{children:"Gestione tipologie - Da implementare"}),(0,s.jsx)("p",{className:"text-sm mt-2",children:"Qui sar\xe0 possibile gestire le tipologie specifiche con caratteristiche tecniche dettagliate"})]})]})]}),(0,s.jsxs)("div",{className:"bg-slate-50 border border-slate-200 rounded-lg p-4",children:[(0,s.jsx)("h5",{className:"font-medium text-slate-900 mb-2",children:"Struttura Database Tipologie:"}),(0,s.jsxs)("div",{className:"text-sm text-slate-600 space-y-1",children:[(0,s.jsxs)("p",{children:[(0,s.jsx)("strong",{children:"Categorie:"})," Classificazione principale (Energia, Controllo, Strumentazione, Dati, ecc.)"]}),(0,s.jsxs)("p",{children:[(0,s.jsx)("strong",{children:"Produttori:"})," Aziende produttrici con informazioni di contatto"]}),(0,s.jsxs)("p",{children:[(0,s.jsx)("strong",{children:"Standard:"})," Normative tecniche di riferimento (CEI, IEC, EN, CENELEC)"]}),(0,s.jsxs)("p",{children:[(0,s.jsx)("strong",{children:"Tipologie:"})," Specifiche tecniche dettagliate per ogni tipo di cavo"]})]})]})]})]})}var F=a(41312);let Z=(0,I.A)("user-plus",[["path",{d:"M16 21v-2a4 4 0 0 0-4-4H6a4 4 0 0 0-4 4v2",key:"1yyitq"}],["circle",{cx:"9",cy:"7",r:"4",key:"nufk8"}],["line",{x1:"19",x2:"19",y1:"8",y2:"14",key:"1bvyxn"}],["line",{x1:"22",x2:"16",y1:"11",y2:"11",key:"1shjgl"}]]),B=(0,I.A)("log-in",[["path",{d:"m10 17 5-5-5-5",key:"1bsop3"}],["path",{d:"M15 12H3",key:"6jk70r"}],["path",{d:"M15 3h4a2 2 0 0 1 2 2v14a2 2 0 0 1-2 2h-4",key:"u53s6r"}]]);function H(){let e=(0,b.useRouter)(),[t,a]=(0,r.useState)("visualizza-utenti"),[d,o]=(0,r.useState)(""),[c,j]=(0,r.useState)([]),[f,N]=(0,r.useState)([]),[y,w]=(0,r.useState)(!0),[A,z]=(0,r.useState)(""),[E,k]=(0,r.useState)(null),[S,T]=(0,r.useState)({open:!1,message:"",severity:"success"}),{user:I,impersonateUser:D}=(0,g.A)(),$=async()=>{try{if(w(!0),z(""),"visualizza-utenti"===t||"crea-utente"===t||"accedi-come-utente"===t){let e=await v.dG.getUsers();j(e)}else if("cantieri"===t){let e=await v._I.getCantieri();N(e)}}catch(e){z(e.response?.data?.detail||e.message||"Errore durante il caricamento dei dati")}finally{w(!1)}},U=e=>{k(e),a("modifica-utente")},O=async e=>{try{await v.dG.toggleUserStatus(e),$()}catch(e){z(e.response?.data?.detail||"Errore durante la modifica dello stato utente")}},G=async e=>{if(confirm("Sei sicuro di voler eliminare questo utente?"))try{await v.dG.deleteUser(e),$()}catch(e){z(e.response?.data?.detail||"Errore durante l'eliminazione dell'utente")}},H=e=>{k(null),a("visualizza-utenti"),$()},X=()=>{k(null),a("visualizza-utenti")},J=async t=>{try{await D(t.id_utente),"user"===t.ruolo?e.push("/cantieri"):"cantieri_user"===t.ruolo?e.push("/cavi"):e.push("/")}catch(e){z(e.response?.data?.detail||e.message||"Errore durante l'impersonificazione")}},W=e=>{let t="NEUTRAL";switch(e){case"owner":t="PROGRESS";break;case"user":t="INFO";break;case"cantieri_user":t="SUCCESS";break;default:t="NEUTRAL"}let a=(0,u.qn)(t);return(0,s.jsx)(m.E,{className:a.badge,children:e})},Q=(e,t)=>{let a="SUCCESS",r="Attivo";if(e){if(t){let e=new Date(t),s=new Date;e<s?(a="ERROR",r="Scaduto"):e.getTime()-s.getTime()<6048e5&&(a="WARNING",r="In Scadenza")}}else a="ERROR",r="Disabilitato";let i=(0,u.qn)(a);return(0,s.jsx)(m.E,{className:i.badge,children:r})};return(c.filter(e=>e.username?.toLowerCase().includes(d.toLowerCase())||e.ragione_sociale?.toLowerCase().includes(d.toLowerCase())||e.email?.toLowerCase().includes(d.toLowerCase())),I&&"owner"===I.ruolo)?(0,s.jsx)("div",{className:"min-h-screen bg-gradient-to-br from-slate-50 to-slate-100 p-6",children:(0,s.jsx)("div",{className:"max-w-[90%] mx-auto space-y-6",children:(0,s.jsxs)(p.tU,{value:t,onValueChange:a,className:"w-full",children:[(0,s.jsxs)(p.j7,{className:`grid w-full ${E?"grid-cols-6":"grid-cols-5"}`,children:[(0,s.jsxs)(p.Xi,{value:"visualizza-utenti",className:"tab-trigger flex items-center gap-2",children:[(0,s.jsx)(F.A,{className:"h-4 w-4"}),"Visualizza Utenti"]}),(0,s.jsxs)(p.Xi,{value:"crea-utente",className:"tab-trigger flex items-center gap-2",children:[(0,s.jsx)(Z,{className:"h-4 w-4"}),"Crea Nuovo Utente"]}),E&&(0,s.jsxs)(p.Xi,{value:"modifica-utente",className:"tab-trigger flex items-center gap-2",children:[(0,s.jsx)(n.A,{className:"h-4 w-4"}),"Modifica Utente"]}),(0,s.jsxs)(p.Xi,{value:"database-tipologie-cavi",className:"tab-trigger flex items-center gap-2",children:[(0,s.jsx)(L.A,{className:"h-4 w-4"}),"Database Tipologie Cavi"]}),(0,s.jsxs)(p.Xi,{value:"visualizza-database-raw",className:"tab-trigger flex items-center gap-2",children:[(0,s.jsx)(R.A,{className:"h-4 w-4"}),"Visualizza Database Raw"]}),(0,s.jsxs)(p.Xi,{value:"reset-database",className:"tab-trigger flex items-center gap-2",children:[(0,s.jsx)(M,{className:"h-4 w-4"}),"Reset Database"]})]}),(0,s.jsxs)(p.av,{value:"visualizza-utenti",className:"space-y-4",children:[A&&(0,s.jsx)("div",{className:"bg-red-50 border border-red-200 rounded-lg p-4",children:(0,s.jsx)("p",{className:"text-red-600",children:A})}),(0,s.jsxs)(i.Zp,{children:[(0,s.jsx)(i.aR,{children:(0,s.jsx)(i.ZB,{children:"Lista Utenti"})}),(0,s.jsx)(i.Wu,{children:(0,s.jsx)("div",{className:"overflow-x-auto",children:(0,s.jsx)("div",{className:"rounded-md border",children:(0,s.jsxs)(h.XI,{className:"min-w-full",children:[(0,s.jsx)(h.A0,{children:(0,s.jsxs)(h.Hj,{children:[(0,s.jsx)(h.nd,{className:"w-[60px] text-center",children:"ID"}),(0,s.jsx)(h.nd,{className:"w-[120px]",children:"Username"}),(0,s.jsx)(h.nd,{className:"w-[100px] text-center",children:"Password"}),(0,s.jsx)(h.nd,{className:"w-[100px] text-center",children:"Ruolo"}),(0,s.jsx)(h.nd,{className:"w-[250px]",children:"Ragione Sociale"}),(0,s.jsx)(h.nd,{className:"w-[200px]",children:"Email"}),(0,s.jsx)(h.nd,{className:"w-[120px] text-center",children:"VAT"}),(0,s.jsx)(h.nd,{className:"w-[100px] text-center",children:"Nazione"}),(0,s.jsx)(h.nd,{className:"w-[150px]",children:"Referente"}),(0,s.jsx)(h.nd,{className:"w-[100px] text-center",children:"Scadenza"}),(0,s.jsx)(h.nd,{className:"w-[100px] text-center",children:"Stato"}),(0,s.jsx)(h.nd,{className:"w-[120px] text-center",children:"Azioni"})]})}),(0,s.jsx)(h.BF,{children:y?(0,s.jsx)(h.Hj,{children:(0,s.jsx)(h.nA,{colSpan:12,className:"text-center py-8",children:(0,s.jsxs)("div",{className:"flex items-center justify-center gap-2",children:[(0,s.jsx)(_.A,{className:"h-4 w-4 animate-spin"}),"Caricamento..."]})})}):0===c.length?(0,s.jsx)(h.Hj,{children:(0,s.jsx)(h.nA,{colSpan:12,className:"text-center py-8 text-slate-500",children:"Nessun utente trovato"})}):c.map(e=>(0,s.jsxs)(h.Hj,{className:"hover:bg-slate-50",children:[(0,s.jsx)(h.nA,{className:"text-center text-slate-500 text-sm font-mono",children:e.id_utente}),(0,s.jsx)(h.nA,{className:"font-semibold text-slate-900",children:e.username}),(0,s.jsx)(h.nA,{className:"text-center font-mono text-xs text-slate-500",children:e.password_plain||"***"}),(0,s.jsx)(h.nA,{className:"text-center",children:W(e.ruolo)}),(0,s.jsx)(h.nA,{className:"max-w-[250px] truncate",title:e.ragione_sociale,children:(0,s.jsx)("span",{className:"text-slate-900",children:e.ragione_sociale||"-"})}),(0,s.jsx)(h.nA,{className:"max-w-[200px] truncate text-sm text-slate-600",title:e.email,children:e.email||"-"}),(0,s.jsx)(h.nA,{className:"text-center text-sm text-slate-600",children:e.vat||"-"}),(0,s.jsx)(h.nA,{className:"text-center text-sm text-slate-600",children:e.nazione||"-"}),(0,s.jsx)(h.nA,{className:"max-w-[150px] truncate text-sm text-slate-600",title:e.referente_aziendale,children:e.referente_aziendale||"-"}),(0,s.jsx)(h.nA,{className:"text-center text-sm text-slate-600",children:e.data_scadenza?new Date(e.data_scadenza).toLocaleDateString("it-IT"):"N/A"}),(0,s.jsx)(h.nA,{className:"text-center",children:Q(e.abilitato,e.data_scadenza)}),(0,s.jsx)(h.nA,{className:"text-center",children:(0,s.jsxs)("div",{className:"flex items-center justify-center gap-2",children:[(0,s.jsx)(x,{user:e,onEdit:()=>U(e),onToggleStatus:()=>O(e.id_utente),onDelete:()=>G(e.id_utente)}),(0,s.jsx)(l.jn,{size:"sm",onClick:()=>J(e),disabled:"owner"===e.ruolo||!e.abilitato,className:"px-3 py-1.5 text-xs",icon:(0,s.jsx)(B,{className:"h-3.5 w-3.5"}),children:"Entra"})]})})]},e.id_utente))})]})})})})]})]}),(0,s.jsx)(p.av,{value:"crea-utente",className:"space-y-4",children:(0,s.jsx)(C,{user:null,onSave:H,onCancel:X})}),E&&(0,s.jsx)(p.av,{value:"modifica-utente",className:"space-y-4",children:(0,s.jsx)(C,{user:E,onSave:H,onCancel:X})}),(0,s.jsx)(p.av,{value:"database-tipologie-cavi",className:"space-y-4",children:(0,s.jsx)(q,{})}),(0,s.jsx)(p.av,{value:"visualizza-database-raw",className:"space-y-4",children:(0,s.jsx)(V,{})}),(0,s.jsx)(p.av,{value:"reset-database",className:"space-y-4",children:(0,s.jsx)(P,{})})]})})}):null}},24883:(e,t,a)=>{Promise.resolve().then(a.bind(a,23652))},27910:e=>{"use strict";e.exports=require("stream")},28354:e=>{"use strict";e.exports=require("util")},29021:e=>{"use strict";e.exports=require("fs")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},33873:e=>{"use strict";e.exports=require("path")},55511:e=>{"use strict";e.exports=require("crypto")},55591:e=>{"use strict";e.exports=require("https")},56896:(e,t,a)=>{"use strict";a.d(t,{S:()=>n});var s=a(60687);a(43210);var r=a(40211),i=a(13964),l=a(4780);function n({className:e,...t}){return(0,s.jsx)(r.bL,{"data-slot":"checkbox",className:(0,l.cn)("peer border-input dark:bg-input/30 data-[state=checked]:bg-primary data-[state=checked]:text-primary-foreground dark:data-[state=checked]:bg-primary data-[state=checked]:border-primary focus-visible:border-ring focus-visible:ring-ring/50 aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive size-4 shrink-0 rounded-[4px] border shadow-xs transition-shadow outline-none focus-visible:ring-[3px] disabled:cursor-not-allowed disabled:opacity-50",e),...t,children:(0,s.jsx)(r.C1,{"data-slot":"checkbox-indicator",className:"flex items-center justify-center text-current transition-none",children:(0,s.jsx)(i.A,{className:"size-3.5"})})})}},61611:(e,t,a)=>{"use strict";a.d(t,{A:()=>s});let s=(0,a(62688).A)("database",[["ellipse",{cx:"12",cy:"5",rx:"9",ry:"3",key:"msslwz"}],["path",{d:"M3 5V19A9 3 0 0 0 21 19V5",key:"1wlel7"}],["path",{d:"M3 12A9 3 0 0 0 21 12",key:"mv7ke4"}]])},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},63143:(e,t,a)=>{"use strict";a.d(t,{A:()=>s});let s=(0,a(62688).A)("square-pen",[["path",{d:"M12 3H5a2 2 0 0 0-2 2v14a2 2 0 0 0 2 2h14a2 2 0 0 0 2-2v-7",key:"1m0v6g"}],["path",{d:"M18.375 2.625a1 1 0 0 1 3 3l-9.013 9.014a2 2 0 0 1-.853.505l-2.873.84a.5.5 0 0 1-.62-.62l.84-2.873a2 2 0 0 1 .506-.852z",key:"ohrbg2"}]])},74075:e=>{"use strict";e.exports=require("zlib")},78122:(e,t,a)=>{"use strict";a.d(t,{A:()=>s});let s=(0,a(62688).A)("refresh-cw",[["path",{d:"M3 12a9 9 0 0 1 9-9 9.75 9.75 0 0 1 6.74 2.74L21 8",key:"v9h5vc"}],["path",{d:"M21 3v5h-5",key:"1q7to0"}],["path",{d:"M21 12a9 9 0 0 1-9 9 9.75 9.75 0 0 1-6.74-2.74L3 16",key:"3uifl3"}],["path",{d:"M8 16H3v5",key:"1cv678"}]])},79551:e=>{"use strict";e.exports=require("url")},80013:(e,t,a)=>{"use strict";a.d(t,{J:()=>l});var s=a(60687);a(43210);var r=a(78148),i=a(4780);function l({className:e,...t}){return(0,s.jsx)(r.b,{"data-slot":"label",className:(0,i.cn)("flex items-center gap-2 text-sm leading-none font-medium select-none group-data-[disabled=true]:pointer-events-none group-data-[disabled=true]:opacity-50 peer-disabled:cursor-not-allowed peer-disabled:opacity-50",e),...t})}},81630:e=>{"use strict";e.exports=require("http")},81806:(e,t,a)=>{"use strict";a.d(t,{Eb:()=>h,GN:()=>p});let s=/[<>\"'&\x00-\x1f\x7f-\x9f]/g,r=/(\b(SELECT|INSERT|UPDATE|DELETE|DROP|CREATE|ALTER|EXEC|UNION|SCRIPT)\b)/gi,i=/(<script|javascript:|vbscript:|onload|onerror|onclick)/gi,l=e=>"string"!=typeof e?"":e.trim().replace(s,"").replace(/\s+/g," ").substring(0,1e3),n=e=>{let t=l(e);return t.length<3?{isValid:!1,error:"Username deve essere almeno 3 caratteri"}:t.length>20?{isValid:!1,error:"Username non pu\xf2 superare 20 caratteri"}:/^[a-zA-Z0-9._-]+$/.test(t)?/^[._-]|[._-]$/.test(t)?{isValid:!1,error:"Username non pu\xf2 iniziare o finire con caratteri speciali"}:{isValid:!0}:{isValid:!1,error:"Username pu\xf2 contenere solo lettere, numeri, punti, underscore e trattini"}},d=e=>{if(!e||e.length<8)return{isValid:!1,error:"Password deve essere almeno 8 caratteri",strength:0};if(e.length>128)return{isValid:!1,error:"Password troppo lunga (max 128 caratteri)",strength:0};let t=0;return(/[a-z]/.test(e)&&t++,/[A-Z]/.test(e)&&t++,/[0-9]/.test(e)&&t++,/[^a-zA-Z0-9]/.test(e)&&t++,e.length>=12&&t++,t<3)?{isValid:!1,error:"Password deve contenere almeno: 1 minuscola, 1 maiuscola, 1 numero o 1 carattere speciale",strength:t}:["password","123456","admin","qwerty","letmein"].some(t=>e.toLowerCase().includes(t))?{isValid:!1,error:"Password troppo comune",strength:t}:{isValid:!0,strength:t}},o=e=>{let t=l(e);return t?t.length>254?{isValid:!1,error:"Email troppo lunga"}:/^[a-zA-Z0-9.!#$%&'*+/=?^_`{|}~-]+@[a-zA-Z0-9](?:[a-zA-Z0-9-]{0,61}[a-zA-Z0-9])?(?:\.[a-zA-Z0-9](?:[a-zA-Z0-9-]{0,61}[a-zA-Z0-9])?)*$/.test(t)?{isValid:!0}:{isValid:!1,error:"Formato email non valido"}:{isValid:!1,error:"Email \xe8 obbligatoria"}},c=(e,t=255)=>l(e).length>t?{isValid:!1,error:`Testo troppo lungo (max ${t} caratteri)`}:i.test(e)||r.test(e)?{isValid:!1,error:"Contenuto non consentito rilevato"}:{isValid:!0},x=e=>{let t=l(e);return t?t.length<2?{isValid:!1,error:"Ragione sociale troppo corta"}:t.length>100?{isValid:!1,error:"Ragione sociale troppo lunga (max 100 caratteri)"}:/^[a-zA-Z0-9\s\.\-&']+$/.test(t)?{isValid:!0}:{isValid:!1,error:"Ragione sociale contiene caratteri non consentiti"}:{isValid:!1,error:"Ragione sociale \xe8 obbligatoria"}},m=e=>{if(!e)return{isValid:!0};let t=l(e).replace(/\s/g,"");return t.length<8||t.length>15?{isValid:!1,error:"VAT deve essere tra 8 e 15 caratteri"}:/^[A-Z0-9]+$/i.test(t)?{isValid:!0}:{isValid:!1,error:"VAT pu\xf2 contenere solo lettere e numeri"}},u=new Map,h=(e,t,a)=>{let s=Date.now(),r=u.get(e);return!r||s>r.resetTime?(u.set(e,{count:1,resetTime:s+a}),!0):!(r.count>=t)&&(r.count++,!0)},p=e=>{let t={},a=n(e.username);if(a.isValid||(t.username=a.error),e.password){let a=d(e.password);a.isValid||(t.password=a.error)}let s=x(e.ragione_sociale);if(s.isValid||(t.ragione_sociale=s.error),e.email){let a=o(e.email);a.isValid||(t.email=a.error)}if(e.vat){let a=m(e.vat);a.isValid||(t.vat=a.error)}if(e.indirizzo){let a=c(e.indirizzo,200);a.isValid||(t.indirizzo=a.error)}if(e.nazione){let a=c(e.nazione,50);a.isValid||(t.nazione=a.error)}if(e.referente_aziendale){let a=c(e.referente_aziendale,100);a.isValid||(t.referente_aziendale=a.error)}return{isValid:0===Object.keys(t).length,errors:t}}},83997:e=>{"use strict";e.exports=require("tty")},88091:(e,t,a)=>{Promise.resolve().then(a.bind(a,1132))},88233:(e,t,a)=>{"use strict";a.d(t,{A:()=>s});let s=(0,a(62688).A)("trash-2",[["path",{d:"M3 6h18",key:"d0wm0j"}],["path",{d:"M19 6v14c0 1-1 2-2 2H7c-1 0-2-1-2-2V6",key:"4alrt4"}],["path",{d:"M8 6V4c0-1 1-2 2-2h4c1 0 2 1 2 2v2",key:"v07s0e"}],["line",{x1:"10",x2:"10",y1:"11",y2:"17",key:"1uufr5"}],["line",{x1:"14",x2:"14",y1:"11",y2:"17",key:"xtxkd"}]])},89667:(e,t,a)=>{"use strict";a.d(t,{p:()=>i});var s=a(60687);a(43210);var r=a(4780);function i({className:e,type:t,...a}){return(0,s.jsx)("input",{type:t,"data-slot":"input",className:(0,r.cn)("file:text-foreground placeholder:text-muted-foreground selection:bg-primary selection:text-primary-foreground dark:bg-input/30 border-input flex h-9 w-full min-w-0 rounded-md border bg-transparent px-3 py-1 text-base shadow-xs transition-[color,box-shadow] outline-none file:inline-flex file:h-7 file:border-0 file:bg-transparent file:text-sm file:font-medium disabled:pointer-events-none disabled:cursor-not-allowed disabled:opacity-50 md:text-sm","focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px]","aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive",e),...a})}},94735:e=>{"use strict";e.exports=require("events")},96474:(e,t,a)=>{"use strict";a.d(t,{A:()=>s});let s=(0,a(62688).A)("plus",[["path",{d:"M5 12h14",key:"1ays0h"}],["path",{d:"M12 5v14",key:"s699le"}]])},96504:(e,t,a)=>{"use strict";a.r(t),a.d(t,{GlobalError:()=>l.a,__next_app__:()=>x,pages:()=>c,routeModule:()=>m,tree:()=>o});var s=a(65239),r=a(48088),i=a(88170),l=a.n(i),n=a(30893),d={};for(let e in n)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(d[e]=()=>n[e]);a.d(t,d);let o={children:["",{children:["admin",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(a.bind(a,1132)),"C:\\CMS\\webapp-nextjs\\src\\app\\admin\\page.tsx"]}]},{metadata:{icon:[async e=>(await Promise.resolve().then(a.bind(a,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(a.bind(a,94431)),"C:\\CMS\\webapp-nextjs\\src\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(a.t.bind(a,57398,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(a.t.bind(a,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(a.t.bind(a,65284,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(a.bind(a,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]}.children,c=["C:\\CMS\\webapp-nextjs\\src\\app\\admin\\page.tsx"],x={require:a,loadChunk:()=>Promise.resolve()},m=new s.AppPageRouteModule({definition:{kind:r.RouteKind.APP_PAGE,page:"/admin/page",pathname:"/admin",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:o}})},96834:(e,t,a)=>{"use strict";a.d(t,{E:()=>d});var s=a(60687);a(43210);var r=a(8730),i=a(24224),l=a(4780);let n=(0,i.F)("inline-flex items-center justify-center rounded-md border px-2 py-0.5 text-xs font-medium w-fit whitespace-nowrap shrink-0 [&>svg]:size-3 gap-1 [&>svg]:pointer-events-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive transition-[color,box-shadow] overflow-hidden",{variants:{variant:{default:"border-transparent bg-primary text-primary-foreground [a&]:hover:bg-primary/90",secondary:"border-transparent bg-secondary text-secondary-foreground [a&]:hover:bg-secondary/90",destructive:"border-transparent bg-destructive text-white [a&]:hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60",outline:"text-foreground [a&]:hover:bg-accent [a&]:hover:text-accent-foreground"}},defaultVariants:{variant:"default"}});function d({className:e,variant:t,asChild:a=!1,...i}){let d=a?r.DX:"span";return(0,s.jsx)(d,{"data-slot":"badge",className:(0,l.cn)(n({variant:t}),e),...i})}}};var t=require("../../webpack-runtime.js");t.C(e);var a=e=>t(t.s=e),s=t.X(0,[447,538,658,952,797,611,615,685],()=>a(96504));module.exports=s})();
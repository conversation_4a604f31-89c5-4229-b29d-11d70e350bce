(()=>{var e={};e.id=472,e.ids=[472],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},17495:(e,t,r)=>{"use strict";r.r(t),r.d(t,{patchFetch:()=>w,routeModule:()=>d,serverHooks:()=>l,workAsyncStorage:()=>u,workUnitAsyncStorage:()=>c});var s={};r.r(s),r.d(s,{POST:()=>i});var a=r(96559),o=r(48088),n=r(37719),p=r(32190);async function i(e){try{let t=await e.json(),r=await fetch("http://localhost:8001/api/password/validate-password",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify(t)}),s=await r.json();return p.NextResponse.json(s,{status:r.status,headers:{"Content-Type":"application/json"}})}catch(e){return p.NextResponse.json({success:!1,detail:"Errore interno del server"},{status:500})}}let d=new a.AppRouteRouteModule({definition:{kind:o.RouteKind.APP_ROUTE,page:"/api/password/validate-password/route",pathname:"/api/password/validate-password",filename:"route",bundlePath:"app/api/password/validate-password/route"},resolvedPagePath:"C:\\CMS\\webapp-nextjs\\src\\app\\api\\password\\validate-password\\route.ts",nextConfigOutput:"",userland:s}),{workAsyncStorage:u,workUnitAsyncStorage:c,serverHooks:l}=d;function w(){return(0,n.patchFetch)({workAsyncStorage:u,workUnitAsyncStorage:c})}},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},44870:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},78335:()=>{},96487:()=>{}};var t=require("../../../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),s=t.X(0,[447,580],()=>r(17495));module.exports=s})();
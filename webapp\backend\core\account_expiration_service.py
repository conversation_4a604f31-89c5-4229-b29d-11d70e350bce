"""
Servizio per la gestione delle notifiche di scadenza account.
"""

import logging
from datetime import datetime, date, timedelta
from typing import List, Dict, Optional, Tuple
from sqlalchemy.orm import Session
from sqlalchemy import and_, or_

from backend.models.user import User
from backend.models.cantiere import Cantiere
from backend.models.security_models import AccountExpirationNotification, AccountExpirationLog, SecurityEvent
from backend.core.email_service import EmailService
from backend.database import get_db

logger = logging.getLogger(__name__)

class AccountExpirationService:
    """
    Servizio per gestire le notifiche di scadenza account e le azioni automatiche.
    """
    
    def __init__(self, email_service: EmailService):
        self.email_service = email_service
        
    def check_and_notify_expiring_accounts(self, db: Session) -> Dict[str, int]:
        """
        Controlla tutti gli account in scadenza e invia notifiche appropriate.
        
        Returns:
            Dict con contatori delle azioni eseguite
        """
        today = date.today()
        warning_5_days = today + timedelta(days=5)
        warning_1_day = today + timedelta(days=1)
        
        results = {
            'users_warned_5_days': 0,
            'users_warned_1_day': 0,
            'users_expired': 0,
            'cantieri_warned_5_days': 0,
            'cantieri_warned_1_day': 0,
            'cantieri_expired': 0,
            'emails_sent': 0,
            'emails_failed': 0
        }
        
        # Controlla utenti standard
        results.update(self._check_users_expiration(db, today, warning_5_days, warning_1_day))
        
        # Controlla cantieri (se hanno scadenza)
        results.update(self._check_cantieri_expiration(db, today, warning_5_days, warning_1_day))
        
        return results
    
    def _check_users_expiration(self, db: Session, today: date, warning_5_days: date, warning_1_day: date) -> Dict[str, int]:
        """Controlla scadenza utenti standard."""
        results = {'users_warned_5_days': 0, 'users_warned_1_day': 0, 'users_expired': 0}
        
        # Utenti che scadono tra 5 giorni
        users_5_days = db.query(User).filter(
            User.ruolo == "user",
            User.abilitato == True,
            User.data_scadenza == warning_5_days,
            User.email.isnot(None)
        ).all()
        
        for user in users_5_days:
            if not self._notification_already_sent(db, user.id_utente, None, 'warning_5_days', warning_5_days):
                if self._send_expiration_warning(db, user, None, 'warning_5_days', warning_5_days):
                    results['users_warned_5_days'] += 1
        
        # Utenti che scadono domani
        users_1_day = db.query(User).filter(
            User.ruolo == "user",
            User.abilitato == True,
            User.data_scadenza == warning_1_day,
            User.email.isnot(None)
        ).all()
        
        for user in users_1_day:
            if not self._notification_already_sent(db, user.id_utente, None, 'warning_1_day', warning_1_day):
                if self._send_expiration_warning(db, user, None, 'warning_1_day', warning_1_day):
                    results['users_warned_1_day'] += 1
        
        # Utenti scaduti oggi
        expired_users = db.query(User).filter(
            User.ruolo == "user",
            User.abilitato == True,
            User.data_scadenza == today
        ).all()
        
        for user in expired_users:
            if self._disable_expired_account(db, user, None):
                results['users_expired'] += 1
        
        return results
    
    def _check_cantieri_expiration(self, db: Session, today: date, warning_5_days: date, warning_1_day: date) -> Dict[str, int]:
        """Controlla scadenza cantieri (se implementata in futuro)."""
        # Per ora i cantieri non hanno scadenza, ma prepariamo la struttura
        return {'cantieri_warned_5_days': 0, 'cantieri_warned_1_day': 0, 'cantieri_expired': 0}
    
    def _notification_already_sent(self, db: Session, user_id: Optional[int], cantiere_id: Optional[int], 
                                 notification_type: str, expiration_date: date) -> bool:
        """Controlla se una notifica è già stata inviata."""
        query = db.query(AccountExpirationNotification).filter(
            AccountExpirationNotification.notification_type == notification_type,
            AccountExpirationNotification.expiration_date == expiration_date,
            AccountExpirationNotification.email_sent == True
        )
        
        if user_id:
            query = query.filter(AccountExpirationNotification.user_id == user_id)
        if cantiere_id:
            query = query.filter(AccountExpirationNotification.cantiere_id == cantiere_id)
            
        return query.first() is not None
    
    def _send_expiration_warning(self, db: Session, user: Optional[User], cantiere: Optional[Cantiere], 
                               notification_type: str, expiration_date: date) -> bool:
        """Invia email di avviso scadenza."""
        try:
            # Determina email e dati
            if user:
                email = user.email
                name = user.username
                account_type = "account utente"
                user_id = user.id_utente
                cantiere_id = None
            else:
                # Per cantieri futuri
                email = None
                name = cantiere.commessa if cantiere else "Unknown"
                account_type = "cantiere"
                user_id = None
                cantiere_id = cantiere.id_cantiere if cantiere else None
            
            if not email:
                logger.warning(f"Email non disponibile per {account_type} {name}")
                return False
            
            # Prepara contenuto email
            days_remaining = (expiration_date - date.today()).days
            subject, body = self._prepare_expiration_email(name, account_type, days_remaining, expiration_date)
            
            # Registra tentativo di notifica
            notification = AccountExpirationNotification(
                user_id=user_id,
                cantiere_id=cantiere_id,
                notification_type=notification_type,
                expiration_date=expiration_date,
                email=email,
                email_sent=False
            )
            db.add(notification)
            db.flush()
            
            # Invia email
            success = self.email_service.send_email(
                to_email=email,
                subject=subject,
                body=body,
                is_html=True
            )
            
            # Aggiorna stato notifica
            notification.email_sent = success
            if not success:
                notification.email_error = "Errore durante l'invio"
            
            # Log azione
            log_entry = AccountExpirationLog(
                user_id=user_id,
                cantiere_id=cantiere_id,
                action='warning_sent',
                expiration_date=expiration_date,
                details=f"Notifica {notification_type} inviata a {email}"
            )
            db.add(log_entry)
            
            # Evento di sicurezza
            security_event = SecurityEvent(
                event_type='account_expiration_warning',
                user_id=user_id,
                cantiere_id=cantiere_id,
                email=email,
                success=success,
                additional_data={
                    'notification_type': notification_type,
                    'days_remaining': days_remaining,
                    'expiration_date': expiration_date.isoformat()
                }
            )
            db.add(security_event)
            
            db.commit()
            
            if success:
                logger.info(f"Notifica scadenza inviata a {email} per {account_type} {name}")
            else:
                logger.error(f"Errore invio notifica scadenza a {email} per {account_type} {name}")
            
            return success
            
        except Exception as e:
            logger.error(f"Errore durante invio notifica scadenza: {e}")
            db.rollback()
            return False
    
    def _disable_expired_account(self, db: Session, user: Optional[User], cantiere: Optional[Cantiere]) -> bool:
        """Disabilita account scaduto."""
        try:
            if user:
                user.abilitato = False
                account_type = "utente"
                name = user.username
                user_id = user.id_utente
                cantiere_id = None
                expiration_date = user.data_scadenza
            else:
                # Per cantieri futuri
                account_type = "cantiere"
                name = cantiere.commessa if cantiere else "Unknown"
                user_id = None
                cantiere_id = cantiere.id_cantiere if cantiere else None
                expiration_date = date.today()  # Placeholder
            
            # Log azione
            log_entry = AccountExpirationLog(
                user_id=user_id,
                cantiere_id=cantiere_id,
                action='disabled_expired',
                expiration_date=expiration_date,
                details=f"Account {account_type} {name} disabilitato automaticamente per scadenza"
            )
            db.add(log_entry)
            
            # Evento di sicurezza
            security_event = SecurityEvent(
                event_type='account_expired_disabled',
                user_id=user_id,
                cantiere_id=cantiere_id,
                success=True,
                additional_data={
                    'account_type': account_type,
                    'expiration_date': expiration_date.isoformat() if expiration_date else None
                }
            )
            db.add(security_event)
            
            # Invia notifica di disabilitazione se c'è email
            if user and user.email:
                subject, body = self._prepare_disabled_email(name, account_type, expiration_date)
                self.email_service.send_email(
                    to_email=user.email,
                    subject=subject,
                    body=body,
                    is_html=True
                )
            
            db.commit()
            logger.warning(f"Account {account_type} {name} disabilitato per scadenza")
            return True
            
        except Exception as e:
            logger.error(f"Errore durante disabilitazione account scaduto: {e}")
            db.rollback()
            return False
    
    def _prepare_expiration_email(self, name: str, account_type: str, days_remaining: int, 
                                expiration_date: date) -> Tuple[str, str]:
        """Prepara contenuto email di avviso scadenza."""
        if days_remaining <= 1:
            urgency = "URGENTE"
            time_text = "domani" if days_remaining == 1 else "oggi"
        else:
            urgency = "AVVISO"
            time_text = f"tra {days_remaining} giorni"
        
        subject = f"[{urgency}] Il tuo {account_type} scade {time_text}"
        
        body = f"""
        <html>
        <body style="font-family: Arial, sans-serif; line-height: 1.6; color: #333;">
            <div style="max-width: 600px; margin: 0 auto; padding: 20px;">
                <div style="background: {'#ff4444' if days_remaining <= 1 else '#ff8800'}; color: white; padding: 15px; border-radius: 5px; text-align: center;">
                    <h2 style="margin: 0;">⚠️ {urgency}: Account in Scadenza</h2>
                </div>
                
                <div style="padding: 20px; background: #f9f9f9; border-radius: 5px; margin: 20px 0;">
                    <p><strong>Gentile {name},</strong></p>
                    
                    <p>Il tuo {account_type} nel sistema CMS scadrà <strong>{time_text}</strong> 
                    (il {expiration_date.strftime('%d/%m/%Y')}).</p>
                    
                    {'<p style="color: #ff4444;"><strong>⚠️ AZIONE RICHIESTA IMMEDIATAMENTE!</strong></p>' if days_remaining <= 1 else ''}
                    
                    <p><strong>Cosa succede alla scadenza:</strong></p>
                    <ul>
                        <li>L'account verrà automaticamente disabilitato</li>
                        <li>Non potrai più accedere al sistema</li>
                        <li>Tutti i dati rimarranno salvati</li>
                    </ul>
                    
                    <p><strong>Per rinnovare l'account:</strong></p>
                    <ul>
                        <li>Contatta l'amministratore del sistema</li>
                        <li>Richiedi l'estensione della data di scadenza</li>
                    </ul>
                </div>
                
                <div style="text-align: center; padding: 20px;">
                    <p style="color: #666; font-size: 12px;">
                        Questa è una notifica automatica del sistema CMS.<br>
                        Non rispondere a questa email.
                    </p>
                </div>
            </div>
        </body>
        </html>
        """
        
        return subject, body
    
    def _prepare_disabled_email(self, name: str, account_type: str, expiration_date: date) -> Tuple[str, str]:
        """Prepara contenuto email di account disabilitato."""
        subject = f"Account {account_type} disabilitato per scadenza"
        
        body = f"""
        <html>
        <body style="font-family: Arial, sans-serif; line-height: 1.6; color: #333;">
            <div style="max-width: 600px; margin: 0 auto; padding: 20px;">
                <div style="background: #ff4444; color: white; padding: 15px; border-radius: 5px; text-align: center;">
                    <h2 style="margin: 0;">🔒 Account Disabilitato</h2>
                </div>
                
                <div style="padding: 20px; background: #f9f9f9; border-radius: 5px; margin: 20px 0;">
                    <p><strong>Gentile {name},</strong></p>
                    
                    <p>Il tuo {account_type} nel sistema CMS è stato automaticamente disabilitato 
                    il {date.today().strftime('%d/%m/%Y')} a causa della scadenza avvenuta il {expiration_date.strftime('%d/%m/%Y')}.</p>
                    
                    <p><strong>Stato attuale:</strong></p>
                    <ul>
                        <li>❌ Account disabilitato</li>
                        <li>❌ Accesso al sistema non consentito</li>
                        <li>✅ Tutti i dati sono conservati</li>
                    </ul>
                    
                    <p><strong>Per riattivare l'account:</strong></p>
                    <ul>
                        <li>Contatta immediatamente l'amministratore del sistema</li>
                        <li>Richiedi il rinnovo e la riattivazione</li>
                        <li>L'account può essere riattivato in qualsiasi momento</li>
                    </ul>
                </div>
                
                <div style="text-align: center; padding: 20px;">
                    <p style="color: #666; font-size: 12px;">
                        Questa è una notifica automatica del sistema CMS.<br>
                        Non rispondere a questa email.
                    </p>
                </div>
            </div>
        </body>
        </html>
        """
        
        return subject, body
    
    def get_expiration_summary(self, db: Session) -> Dict:
        """Ottieni riepilogo degli account in scadenza."""
        today = date.today()
        
        # Utenti in scadenza nei prossimi 7 giorni
        users_expiring_soon = db.query(User).filter(
            User.ruolo == "user",
            User.abilitato == True,
            User.data_scadenza.isnot(None),
            User.data_scadenza >= today,
            User.data_scadenza <= today + timedelta(days=7)
        ).all()
        
        # Utenti già scaduti ma ancora abilitati
        users_expired = db.query(User).filter(
            User.ruolo == "user",
            User.abilitato == True,
            User.data_scadenza.isnot(None),
            User.data_scadenza < today
        ).all()
        
        return {
            'users_expiring_soon': len(users_expiring_soon),
            'users_expired': len(users_expired),
            'expiring_details': [
                {
                    'id': user.id_utente,
                    'username': user.username,
                    'email': user.email,
                    'expiration_date': user.data_scadenza.isoformat(),
                    'days_remaining': (user.data_scadenza - today).days
                }
                for user in users_expiring_soon
            ],
            'expired_details': [
                {
                    'id': user.id_utente,
                    'username': user.username,
                    'email': user.email,
                    'expiration_date': user.data_scadenza.isoformat(),
                    'days_overdue': (today - user.data_scadenza).days
                }
                for user in users_expired
            ]
        }

"use strict";exports.id=685,exports.ids=[685],exports.modules={44493:(e,t,r)=>{r.d(t,{BT:()=>l,Wu:()=>u,ZB:()=>s,Zp:()=>o,aR:()=>i});var a=r(60687);r(43210);var n=r(4780);function o({className:e,...t}){return(0,a.jsx)("div",{"data-slot":"card",className:(0,n.cn)("bg-card text-card-foreground flex flex-col gap-6 rounded-xl border py-6 shadow-sm",e),...t})}function i({className:e,...t}){return(0,a.jsx)("div",{"data-slot":"card-header",className:(0,n.cn)("@container/card-header grid auto-rows-min grid-rows-[auto_auto] items-start gap-1.5 px-6 has-data-[slot=card-action]:grid-cols-[1fr_auto] [.border-b]:pb-6",e),...t})}function s({className:e,...t}){return(0,a.jsx)("div",{"data-slot":"card-title",className:(0,n.cn)("leading-none font-semibold",e),...t})}function l({className:e,...t}){return(0,a.jsx)("div",{"data-slot":"card-description",className:(0,n.cn)("text-muted-foreground text-sm",e),...t})}function u({className:e,...t}){return(0,a.jsx)("div",{"data-slot":"card-content",className:(0,n.cn)("px-6",e),...t})}},56770:(e,t,r)=>{r.d(t,{tU:()=>q,av:()=>X,j7:()=>O,Xi:()=>W});var a=r(60687),n=r(43210),o=r(70569),i=r(11273),s=r(9510),l=r(98599),u=r(96963),d=r(14163),c=r(13495),f=r(65551),m=r(43),v="rovingFocusGroup.onEntryFocus",p={bubbles:!1,cancelable:!0},b="RovingFocusGroup",[g,x,w]=(0,s.N)(b),[y,h]=(0,i.A)(b,[w]),[j,R]=y(b),N=n.forwardRef((e,t)=>(0,a.jsx)(g.Provider,{scope:e.__scopeRovingFocusGroup,children:(0,a.jsx)(g.Slot,{scope:e.__scopeRovingFocusGroup,children:(0,a.jsx)(F,{...e,ref:t})})}));N.displayName=b;var F=n.forwardRef((e,t)=>{let{__scopeRovingFocusGroup:r,orientation:i,loop:s=!1,dir:u,currentTabStopId:g,defaultCurrentTabStopId:w,onCurrentTabStopIdChange:y,onEntryFocus:h,preventScrollOnEntryFocus:R=!1,...N}=e,F=n.useRef(null),I=(0,l.s)(t,F),A=(0,m.jH)(u),[D,C]=(0,f.i)({prop:g,defaultProp:w??null,onChange:y,caller:b}),[E,G]=n.useState(!1),k=(0,c.c)(h),K=x(r),S=n.useRef(!1),[M,B]=n.useState(0);return n.useEffect(()=>{let e=F.current;if(e)return e.addEventListener(v,k),()=>e.removeEventListener(v,k)},[k]),(0,a.jsx)(j,{scope:r,orientation:i,dir:A,loop:s,currentTabStopId:D,onItemFocus:n.useCallback(e=>C(e),[C]),onItemShiftTab:n.useCallback(()=>G(!0),[]),onFocusableItemAdd:n.useCallback(()=>B(e=>e+1),[]),onFocusableItemRemove:n.useCallback(()=>B(e=>e-1),[]),children:(0,a.jsx)(d.sG.div,{tabIndex:E||0===M?-1:0,"data-orientation":i,...N,ref:I,style:{outline:"none",...e.style},onMouseDown:(0,o.m)(e.onMouseDown,()=>{S.current=!0}),onFocus:(0,o.m)(e.onFocus,e=>{let t=!S.current;if(e.target===e.currentTarget&&t&&!E){let t=new CustomEvent(v,p);if(e.currentTarget.dispatchEvent(t),!t.defaultPrevented){let e=K().filter(e=>e.focusable);T([e.find(e=>e.active),e.find(e=>e.id===D),...e].filter(Boolean).map(e=>e.ref.current),R)}}S.current=!1}),onBlur:(0,o.m)(e.onBlur,()=>G(!1))})})}),I="RovingFocusGroupItem",A=n.forwardRef((e,t)=>{let{__scopeRovingFocusGroup:r,focusable:i=!0,active:s=!1,tabStopId:l,children:c,...f}=e,m=(0,u.B)(),v=l||m,p=R(I,r),b=p.currentTabStopId===v,w=x(r),{onFocusableItemAdd:y,onFocusableItemRemove:h,currentTabStopId:j}=p;return n.useEffect(()=>{if(i)return y(),()=>h()},[i,y,h]),(0,a.jsx)(g.ItemSlot,{scope:r,id:v,focusable:i,active:s,children:(0,a.jsx)(d.sG.span,{tabIndex:b?0:-1,"data-orientation":p.orientation,...f,ref:t,onMouseDown:(0,o.m)(e.onMouseDown,e=>{i?p.onItemFocus(v):e.preventDefault()}),onFocus:(0,o.m)(e.onFocus,()=>p.onItemFocus(v)),onKeyDown:(0,o.m)(e.onKeyDown,e=>{if("Tab"===e.key&&e.shiftKey)return void p.onItemShiftTab();if(e.target!==e.currentTarget)return;let t=function(e,t,r){var a;let n=(a=e.key,"rtl"!==r?a:"ArrowLeft"===a?"ArrowRight":"ArrowRight"===a?"ArrowLeft":a);if(!("vertical"===t&&["ArrowLeft","ArrowRight"].includes(n))&&!("horizontal"===t&&["ArrowUp","ArrowDown"].includes(n)))return D[n]}(e,p.orientation,p.dir);if(void 0!==t){if(e.metaKey||e.ctrlKey||e.altKey||e.shiftKey)return;e.preventDefault();let r=w().filter(e=>e.focusable).map(e=>e.ref.current);if("last"===t)r.reverse();else if("prev"===t||"next"===t){"prev"===t&&r.reverse();let a=r.indexOf(e.currentTarget);r=p.loop?function(e,t){return e.map((r,a)=>e[(t+a)%e.length])}(r,a+1):r.slice(a+1)}setTimeout(()=>T(r))}}),children:"function"==typeof c?c({isCurrentTabStop:b,hasTabStop:null!=j}):c})})});A.displayName=I;var D={ArrowLeft:"prev",ArrowUp:"prev",ArrowRight:"next",ArrowDown:"next",PageUp:"first",Home:"first",PageDown:"last",End:"last"};function T(e,t=!1){let r=document.activeElement;for(let a of e)if(a===r||(a.focus({preventScroll:t}),document.activeElement!==r))return}var C=r(46059),E="Tabs",[G,k]=(0,i.A)(E,[h]),K=h(),[S,M]=G(E),B=n.forwardRef((e,t)=>{let{__scopeTabs:r,value:n,onValueChange:o,defaultValue:i,orientation:s="horizontal",dir:l,activationMode:c="automatic",...v}=e,p=(0,m.jH)(l),[b,g]=(0,f.i)({prop:n,onChange:o,defaultProp:i??"",caller:E});return(0,a.jsx)(S,{scope:r,baseId:(0,u.B)(),value:b,onValueChange:g,orientation:s,dir:p,activationMode:c,children:(0,a.jsx)(d.sG.div,{dir:p,"data-orientation":s,...v,ref:t})})});B.displayName=E;var L="TabsList",P=n.forwardRef((e,t)=>{let{__scopeTabs:r,loop:n=!0,...o}=e,i=M(L,r),s=K(r);return(0,a.jsx)(N,{asChild:!0,...s,orientation:i.orientation,dir:i.dir,loop:n,children:(0,a.jsx)(d.sG.div,{role:"tablist","aria-orientation":i.orientation,...o,ref:t})})});P.displayName=L;var _="TabsTrigger",U=n.forwardRef((e,t)=>{let{__scopeTabs:r,value:n,disabled:i=!1,...s}=e,l=M(_,r),u=K(r),c=z(l.baseId,n),f=H(l.baseId,n),m=n===l.value;return(0,a.jsx)(A,{asChild:!0,...u,focusable:!i,active:m,children:(0,a.jsx)(d.sG.button,{type:"button",role:"tab","aria-selected":m,"aria-controls":f,"data-state":m?"active":"inactive","data-disabled":i?"":void 0,disabled:i,id:c,...s,ref:t,onMouseDown:(0,o.m)(e.onMouseDown,e=>{i||0!==e.button||!1!==e.ctrlKey?e.preventDefault():l.onValueChange(n)}),onKeyDown:(0,o.m)(e.onKeyDown,e=>{[" ","Enter"].includes(e.key)&&l.onValueChange(n)}),onFocus:(0,o.m)(e.onFocus,()=>{let e="manual"!==l.activationMode;m||i||!e||l.onValueChange(n)})})})});U.displayName=_;var V="TabsContent",$=n.forwardRef((e,t)=>{let{__scopeTabs:r,value:o,forceMount:i,children:s,...l}=e,u=M(V,r),c=z(u.baseId,o),f=H(u.baseId,o),m=o===u.value,v=n.useRef(m);return n.useEffect(()=>{let e=requestAnimationFrame(()=>v.current=!1);return()=>cancelAnimationFrame(e)},[]),(0,a.jsx)(C.C,{present:i||m,children:({present:r})=>(0,a.jsx)(d.sG.div,{"data-state":m?"active":"inactive","data-orientation":u.orientation,role:"tabpanel","aria-labelledby":c,hidden:!r,id:f,tabIndex:0,...l,ref:t,style:{...e.style,animationDuration:v.current?"0s":void 0},children:r&&s})})});function z(e,t){return`${e}-trigger-${t}`}function H(e,t){return`${e}-content-${t}`}$.displayName=V;var Z=r(4780);let q=B,O=n.forwardRef(({className:e,...t},r)=>(0,a.jsx)(P,{ref:r,className:(0,Z.cn)("inline-flex h-10 items-center justify-center rounded-md bg-muted p-1 text-muted-foreground",e),...t}));O.displayName=P.displayName;let W=n.forwardRef(({className:e,...t},r)=>(0,a.jsx)(U,{ref:r,className:(0,Z.cn)("inline-flex items-center justify-center whitespace-nowrap rounded-sm px-3 py-1.5 text-sm font-medium ring-offset-background transition-all focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 data-[state=active]:bg-background data-[state=active]:text-foreground data-[state=active]:shadow-sm",e),...t}));W.displayName=U.displayName;let X=n.forwardRef(({className:e,...t},r)=>(0,a.jsx)($,{ref:r,className:(0,Z.cn)("mt-2 ring-offset-background focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2",e),...t}));X.displayName=$.displayName},70440:(e,t,r)=>{r.r(t),r.d(t,{default:()=>n});var a=r(31658);let n=async e=>[{type:"image/x-icon",sizes:"16x16",url:(0,a.fillMetadataSegment)(".",await e.params,"favicon.ico")+""}]}};
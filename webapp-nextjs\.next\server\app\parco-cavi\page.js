(()=>{var e={};e.id=562,e.ids=[562],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},12412:e=>{"use strict";e.exports=require("assert")},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},21820:e=>{"use strict";e.exports=require("os")},27910:e=>{"use strict";e.exports=require("stream")},28354:e=>{"use strict";e.exports=require("util")},29021:e=>{"use strict";e.exports=require("fs")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},33873:e=>{"use strict";e.exports=require("path")},55511:e=>{"use strict";e.exports=require("crypto")},55591:e=>{"use strict";e.exports=require("https")},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},70440:(e,r,t)=>{"use strict";t.r(r),t.d(r,{default:()=>a});var s=t(31658);let a=async e=>[{type:"image/x-icon",sizes:"16x16",url:(0,s.fillMetadataSegment)(".",await e.params,"favicon.ico")+""}]},71902:()=>{throw Error('Module build failed (from ./node_modules/next/dist/build/webpack/loaders/next-swc-loader.js):\nError:   \x1b[31mx\x1b[0m Unexpected token `CantiereErrorBoundary`. Expected jsx identifier\n     ,-[\x1b[36;1;4mC:\\CMS\\webapp-nextjs\\src\\app\\parco-cavi\\page.tsx\x1b[0m:277:1]\n \x1b[2m274\x1b[0m |   })\n \x1b[2m275\x1b[0m | \n \x1b[2m276\x1b[0m |   return (\n \x1b[2m277\x1b[0m |     <CantiereErrorBoundary>\n     : \x1b[35;1m     ^^^^^^^^^^^^^^^^^^^^^\x1b[0m\n \x1b[2m278\x1b[0m |       <div className="min-h-screen bg-gradient-to-br from-slate-50 to-slate-100 p-6">\n \x1b[2m279\x1b[0m |         <div className="max-w-[90%] mx-auto space-y-6">\n     `----\n\n\nCaused by:\n    Syntax Error')},74075:e=>{"use strict";e.exports=require("zlib")},78335:()=>{},79551:e=>{"use strict";e.exports=require("url")},81630:e=>{"use strict";e.exports=require("http")},83997:e=>{"use strict";e.exports=require("tty")},85902:(e,r,t)=>{"use strict";t.r(r),t.d(r,{GlobalError:()=>n.a,__next_app__:()=>x,pages:()=>d,routeModule:()=>c,tree:()=>u});var s=t(65239),a=t(48088),i=t(88170),n=t.n(i),o=t(30893),p={};for(let e in o)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(p[e]=()=>o[e]);t.d(r,p);let u={children:["",{children:["parco-cavi",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(t.t.bind(t,71902,23)),"C:\\CMS\\webapp-nextjs\\src\\app\\parco-cavi\\page.tsx"]}]},{metadata:{icon:[async e=>(await Promise.resolve().then(t.bind(t,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(t.bind(t,94431)),"C:\\CMS\\webapp-nextjs\\src\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(t.t.bind(t,57398,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(t.t.bind(t,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(t.t.bind(t,65284,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(t.bind(t,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]}.children,d=["C:\\CMS\\webapp-nextjs\\src\\app\\parco-cavi\\page.tsx"],x={require:t,loadChunk:()=>Promise.resolve()},c=new s.AppPageRouteModule({definition:{kind:a.RouteKind.APP_PAGE,page:"/parco-cavi/page",pathname:"/parco-cavi",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:u}})},94735:e=>{"use strict";e.exports=require("events")},96487:()=>{}};var r=require("../../webpack-runtime.js");r.C(e);var t=e=>r(r.s=e),s=r.X(0,[447,538,658,615],()=>t(85902));module.exports=s})();
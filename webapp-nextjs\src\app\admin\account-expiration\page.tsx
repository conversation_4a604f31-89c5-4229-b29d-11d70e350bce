'use client'

import { useEffect } from 'react'
import { useRouter } from 'next/navigation'
import { useAuth } from '@/hooks/useAuth'
import AccountExpirationDashboard from '@/components/admin/AccountExpirationDashboard'

export default function AccountExpirationPage() {
  const { user, loading } = useAuth()
  const router = useRouter()

  useEffect(() => {
    if (!loading && (!user || user.ruolo !== 'admin')) {
      router.push('/login')
    }
  }, [user, loading, router])

  if (loading) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="animate-spin rounded-full h-32 w-32 border-b-2 border-gray-900"></div>
      </div>
    )
  }

  if (!user || user.ruolo !== 'admin') {
    return null
  }

  return (
    <div className="container mx-auto px-4 py-8">
      <AccountExpirationDashboard />
    </div>
  )
}
